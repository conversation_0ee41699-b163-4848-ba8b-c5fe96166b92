# 卡密生成器 - 网页版

这是一个基于Web的卡密生成器，可以生成和管理API密钥。

## 功能特点

- 生成指定配额和有效期的卡密
- 支持批量生成多个卡密
- 复制生成的卡密信息
- 下载卡密记录为文本文件
- 响应式设计，支持移动设备

## 技术栈

- 前端：HTML, CSS, JavaScript, Bootstrap 5
- 后端：Python, Flask
- 数据库：PostgreSQL

## 安装和部署

### 方法一：直接运行（自动安装依赖）

1. 克隆仓库
```bash
git clone https://github.com/yourusername/web-card-generator.git
cd web-card-generator
```

2. 运行应用（会自动安装依赖）
```bash
python run.py
```

3. 访问应用
在浏览器中访问 http://localhost:5000

### 方法二：手动安装依赖

1. 克隆仓库
```bash
git clone https://github.com/yourusername/web-card-generator.git
cd web-card-generator
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，填入正确的数据库连接信息
```

4. 运行应用
```bash
python run.py
```

5. 访问应用
在浏览器中访问 http://localhost:5000

### 方法三：使用Docker部署

1. 构建Docker镜像
```bash
docker build -t card-generator .
```

2. 运行Docker容器
```bash
docker run -d -p 5000:5000 --env-file .env --name card-generator-app card-generator
```

3. 访问应用
在浏览器中访问 http://localhost:5000

## 生产环境部署

对于生产环境，建议：

1. 使用Nginx作为反向代理
2. 启用HTTPS
3. 设置适当的安全头部
4. 使用环境变量管理敏感信息

## 数据库结构

应用使用PostgreSQL数据库，需要创建以下表结构：

```sql
CREATE TABLE api_keys (
    id SERIAL PRIMARY KEY,
    key_hash VARCHAR(64) NOT NULL,
    total_quota INTEGER NOT NULL,
    remaining INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);
```

## 用户认证

应用使用简单的用户认证系统：

- 默认用户名：`admin`
- 默认密码：`admin123`

在生产环境中，建议修改 `app/models.py` 文件中的默认凭据。

## 安全注意事项

- 请确保数据库凭据安全
- 生产环境中应启用HTTPS
- 修改默认的管理员密码
- 定期备份数据库

## 许可证

[MIT License](LICENSE) 