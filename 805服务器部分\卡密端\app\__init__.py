from flask import Flask, request
from flask_cors import CORS
from flask_login import LoginManager
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(script_info=None):
    logger.info("开始创建Flask应用")
    
    app = Flask(__name__)
    CORS(app)  # 启用跨域请求支持
    
    # 配置 Secret Key
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev_key_for_testing')
    
    # 支持反向代理
    app.config['PREFERRED_URL_SCHEME'] = 'https'
    
    # 如果应用运行在反向代理后面，需要设置这个选项
    app_root = os.getenv('APPLICATION_ROOT', '/chaxun')
    app.config['APPLICATION_ROOT'] = app_root
    logger.info(f"应用根路径设置为: {app_root}")
    
    # 设置会话cookie安全选项
    app.config['SESSION_COOKIE_SECURE'] = True
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    
    # 初始化 Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'main.login'
    login_manager.login_message = '请先登录'
    
    # 注册蓝图
    from .routes import main_bp
    app.register_blueprint(main_bp)
    logger.info("已注册主蓝图")
    
    # 用户加载函数
    from .models import User
    
    @login_manager.user_loader
    def load_user(user_id):
        logger.info(f"加载用户: {user_id}")
        return User.get(user_id)
    
    # 添加请求处理器，记录每个请求
    @app.before_request
    def log_request():
        logger.info(f"收到请求: {request.method} {request.path}")
        logger.info(f"请求头: {dict(request.headers)}")
    
    # 添加错误处理器
    @app.errorhandler(404)
    def page_not_found(e):
        logger.error(f"404错误: {request.path}")
        return f"页面未找到: {request.path}", 404
    
    @app.errorhandler(500)
    def server_error(e):
        logger.error(f"500错误: {str(e)}")
        return "服务器内部错误，请稍后再试", 500
    
    logger.info("Flask应用创建完成")
    return app 