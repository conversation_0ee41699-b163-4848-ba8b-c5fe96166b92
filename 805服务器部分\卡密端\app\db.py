import os
import pg8000.native

# 数据库连接配置
DB_CONFIG = {
    "user": os.getenv("DB_USER", "上线测试"),
    "password": os.getenv("DB_PASSWORD", "Ming98091.3"),
    "host": os.getenv("DB_HOST", "***************"),
    "port": int(os.getenv("DB_PORT", "5432")),
    "database": os.getenv("DB_NAME", "上线测试")
}

def get_db_connection():
    """创建并返回数据库连接"""
    conn = pg8000.native.Connection(
        user=DB_CONFIG["user"],
        password=DB_CONFIG["password"],
        host=DB_CONFIG["host"],
        port=DB_CONFIG["port"],
        database=DB_CONFIG["database"]
    )
    return conn

def close_db_connection(conn):
    """关闭数据库连接"""
    if conn:
        conn.close() 