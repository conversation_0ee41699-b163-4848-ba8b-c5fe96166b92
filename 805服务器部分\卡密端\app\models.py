from flask_login import UserMixin
import hashlib
import os

# 简单的用户模型，实际应用中应该使用数据库存储
class User(UserMixin):
    users = {
        'imcycyc': {
            'password': hashlib.sha256('Ming980913.'.encode()).hexdigest(),
            'id': '1'
        }
    }
    
    def __init__(self, username, password_hash, id):
        self.username = username
        self.password_hash = password_hash
        self.id = id
    
    @classmethod
    def get(cls, user_id):
        for username, user_data in cls.users.items():
            if user_data['id'] == user_id:
                return cls(username, user_data['password'], user_id)
        return None
    
    @classmethod
    def authenticate(cls, username, password):
        if username in cls.users:
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            if cls.users[username]['password'] == password_hash:
                return cls(username, password_hash, cls.users[username]['id'])
        return None 