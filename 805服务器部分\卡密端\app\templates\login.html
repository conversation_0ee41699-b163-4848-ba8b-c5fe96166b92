<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密生成器 - 登录</title>
    <!-- 强制使用HTTPS -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css', _external=True) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css', _external=True) }}">
    <style>
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background-color: #f5f5f5;
        }
        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 15px;
        }
        .login-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            background: linear-gradient(135deg, #2196F3, #0D47A1);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .login-body {
            padding: 20px;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card login-card">
            <div class="login-header">
                <h2 class="mb-0">卡密生成器</h2>
                <p class="mb-0">管理员登录</p>
            </div>
            <div class="login-body">
                {% if error %}
                <div class="error-message">
                    {{ error }}
                </div>
                {% endif %}
                
                <form method="post" action="{{ url_for('main.login', _external=True) }}">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">登录</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js', _external=True) }}"></script>
    
    <!-- 调试信息 -->
    <script>
        console.log("登录页面加载完成");
        console.log("当前URL:", window.location.href);
        console.log("当前路径:", window.location.pathname);
    </script>
</body>
</html> 