import sys
import os
import subprocess

def check_and_install_dependencies():
    """检查并安装依赖"""
    print("正在检查依赖...")
    
    # 获取requirements.txt的路径
    requirements_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'requirements.txt')
    
    if not os.path.exists(requirements_path):
        print("错误: 找不到requirements.txt文件")
        sys.exit(1)
    
    # 简单方法：直接尝试安装所有依赖
    try:
        print("正在安装依赖...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_path])
        print("依赖安装完成!")
        return
    except Exception as e:
        print(f"依赖安装过程中出现错误: {e}")
        print("将尝试更详细的检查...")
    
    # 如果直接安装失败，尝试更详细的检查
    try:
        # 尝试导入importlib.metadata（Python 3.8+）
        try:
            import importlib.metadata
            use_importlib = True
        except ImportError:
            # 如果importlib.metadata不可用，尝试安装setuptools
            print("正在安装setuptools...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "setuptools"])
                
                # 再次尝试导入
                try:
                    import importlib.metadata
                    use_importlib = True
                except ImportError:
                    import pkg_resources
                    use_importlib = False
            except Exception:
                print("无法安装setuptools，将尝试继续...")
                use_importlib = False
    except Exception:
        # 如果仍然失败，直接安装所有依赖
        print("无法检查依赖，将直接安装所有依赖...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_path])
            print("依赖安装完成!")
            return
        except subprocess.CalledProcessError as e:
            print(f"依赖安装失败: {e}")
            print("\n" + "="*50)
            print("自动安装依赖失败，请尝试手动安装:")
            print(f"pip install -r {requirements_path}")
            print("="*50 + "\n")
            
            # 询问用户是否继续
            try:
                response = input("是否尝试继续运行应用？(y/n): ").strip().lower()
                if response != 'y':
                    sys.exit(1)
                print("将尝试继续运行，但可能会出现错误...")
                return
            except:
                sys.exit(1)
    
    # 读取requirements.txt
    try:
        with open(requirements_path, 'r') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        
        # 检查是否需要安装依赖
        missing_packages = []
        for requirement in requirements:
            # 解析包名和版本
            package_name = requirement.split('==')[0]
            try:
                if use_importlib:
                    importlib.metadata.distribution(package_name)
                else:
                    pkg_resources.get_distribution(package_name)
            except Exception:  # 捕获所有异常
                missing_packages.append(requirement)
        
        # 安装缺失的依赖
        if missing_packages:
            print(f"需要安装以下依赖: {', '.join(missing_packages)}")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_path])
                print("依赖安装完成!")
            except Exception as e:
                print(f"依赖安装失败: {e}")
                print("\n" + "="*50)
                print("自动安装依赖失败，请尝试手动安装:")
                print(f"pip install -r {requirements_path}")
                print("="*50 + "\n")
                
                # 询问用户是否继续
                try:
                    response = input("是否尝试继续运行应用？(y/n): ").strip().lower()
                    if response != 'y':
                        sys.exit(1)
                    print("将尝试继续运行，但可能会出现错误...")
                except:
                    sys.exit(1)
        else:
            print("所有依赖已安装")
    except Exception as e:
        print(f"检查依赖过程中出现错误: {e}")
        print("\n" + "="*50)
        print("自动安装依赖失败，请尝试手动安装:")
        print(f"pip install -r {requirements_path}")
        print("="*50 + "\n")
        
        # 询问用户是否继续
        try:
            response = input("是否尝试继续运行应用？(y/n): ").strip().lower()
            if response != 'y':
                sys.exit(1)
            print("将尝试继续运行，但可能会出现错误...")
        except:
            sys.exit(1)

# 导入应用（在依赖安装后导入，避免导入错误）
try:
    from app import create_app
    app = create_app()
except ImportError as e:
    if __name__ == '__main__':
        print(f"导入应用失败: {e}")
        print("\n" + "="*50)
        print("应用启动失败，可能是依赖未正确安装。")
        print("请尝试手动安装依赖:")
        requirements_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'requirements.txt')
        print(f"pip install -r {requirements_path}")
        print("="*50 + "\n")
        sys.exit(1)

if __name__ == '__main__':
    # 检查并安装依赖
    try:
        check_and_install_dependencies()
    except Exception as e:
        print(f"依赖检查过程中出现未预期的错误: {e}")
        print("将尝试继续运行，但可能会出现错误...")
    
    # 运行应用
    app.run(host='0.0.0.0', port=1111, debug=True) 