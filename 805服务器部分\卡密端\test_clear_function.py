#!/usr/bin/env python3
"""
测试清除最近换号时间功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db_connection, close_db_connection

def test_clear_last_request_time():
    """测试清除最近换号时间功能"""
    print("开始测试清除最近换号时间功能...")
    
    try:
        conn = get_db_connection()
        
        # 首先查看是否有API Key记录
        query = """
            SELECT key_hash, last_cookie_request_at 
            FROM api_keys 
            WHERE last_cookie_request_at IS NOT NULL 
            LIMIT 5
        """
        result = conn.run(query)
        
        if not result:
            print("没有找到有最近换号时间记录的API Key")
            
            # 创建一个测试记录
            print("创建测试记录...")
            test_key = "test_key_for_clear_function"
            
            # 先删除可能存在的测试记录
            conn.run("DELETE FROM api_keys WHERE key_hash = :key_hash", key_hash=test_key)
            
            # 插入测试记录
            conn.run("""
                INSERT INTO api_keys (
                    key_hash, total_quota, remaining, is_active, 
                    created_at, expires_at, daily_usage_limit, last_cookie_request_at
                ) VALUES (
                    :key_hash, 100, 100, true, 
                    NOW(), NOW() + INTERVAL '30 days', 20, NOW()
                )
            """, key_hash=test_key)
            
            print(f"已创建测试API Key: {test_key}")
            
            # 测试清除功能
            print("测试清除功能...")
            clear_query = """
                UPDATE api_keys
                SET last_cookie_request_at = NULL
                WHERE key_hash = :key_hash
                RETURNING id
            """
            clear_result = conn.run(clear_query, key_hash=test_key)
            
            if clear_result:
                print("✅ 清除功能测试成功！")
                
                # 验证清除结果
                verify_query = """
                    SELECT last_cookie_request_at 
                    FROM api_keys 
                    WHERE key_hash = :key_hash
                """
                verify_result = conn.run(verify_query, key_hash=test_key)
                
                if verify_result and verify_result[0][0] is None:
                    print("✅ 验证成功：最近换号时间已被清除")
                else:
                    print("❌ 验证失败：最近换号时间未被清除")
                
                # 清理测试记录
                conn.run("DELETE FROM api_keys WHERE key_hash = :key_hash", key_hash=test_key)
                print("已清理测试记录")
            else:
                print("❌ 清除功能测试失败")
        else:
            print(f"找到 {len(result)} 个有最近换号时间记录的API Key:")
            for row in result:
                key_hash, last_request_time = row
                print(f"  - {key_hash[:20]}... : {last_request_time}")
            
            # 测试第一个记录的清除功能
            test_key = result[0][0]
            print(f"\n测试清除 {test_key[:20]}... 的最近换号时间")
            
            clear_query = """
                UPDATE api_keys
                SET last_cookie_request_at = NULL
                WHERE key_hash = :key_hash
                RETURNING id
            """
            clear_result = conn.run(clear_query, key_hash=test_key)
            
            if clear_result:
                print("✅ 清除功能测试成功！")
                
                # 恢复原始时间（用于测试目的）
                restore_query = """
                    UPDATE api_keys
                    SET last_cookie_request_at = NOW()
                    WHERE key_hash = :key_hash
                """
                conn.run(restore_query, key_hash=test_key)
                print("已恢复原始时间（测试目的）")
            else:
                print("❌ 清除功能测试失败")
        
        close_db_connection(conn)
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_clear_last_request_time()
