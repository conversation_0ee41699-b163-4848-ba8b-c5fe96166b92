from app import create_app
import os

# 创建应用实例
app = create_app()

# 获取应用根路径
prefix = os.environ.get('APPLICATION_ROOT', '/chaxun')
print(f"应用根路径设置为: {prefix}")

# 添加中间件处理反向代理路径
class PathPrefixMiddleware:
    def __init__(self, app, prefix='/'):
        self.app = app
        self.prefix = prefix
        print(f"初始化中间件，路径前缀: {prefix}")

    def __call__(self, environ, start_response):
        original_path = environ.get('PATH_INFO', '')
        script_name = environ.get('SCRIPT_NAME', '')
        
        print(f"收到请求: PATH_INFO={original_path}, SCRIPT_NAME={script_name}")
        
        # 设置SCRIPT_NAME
        if not script_name:
            environ['SCRIPT_NAME'] = self.prefix
        
        # 如果请求路径以前缀开头，则移除前缀
        if original_path.startswith(self.prefix):
            path_info = original_path[len(self.prefix):]
            if not path_info:
                path_info = '/'
            environ['PATH_INFO'] = path_info
            print(f"修改后的路径: PATH_INFO={path_info}, SCRIPT_NAME={environ['SCRIPT_NAME']}")
        else:
            print(f"路径未修改: {original_path} 不以 {self.prefix} 开头")
        
        return self.app(environ, start_response)

# 应用中间件
app.wsgi_app = PathPrefixMiddleware(app.wsgi_app, prefix)

# 设置环境变量，确保Flask内部URL生成正确
os.environ['SCRIPT_NAME'] = prefix

if __name__ == "__main__":
    # 使用环境变量中的端口或默认端口
    port = int(os.environ.get('PORT', 1111))
    print(f"应用将在 http://0.0.0.0:{port} 启动")
    app.run(host='0.0.0.0', port=port, debug=True) 