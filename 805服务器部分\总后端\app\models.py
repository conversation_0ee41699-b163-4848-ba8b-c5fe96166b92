from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, func
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime, timezone, timedelta
from .database import Base, get_db
import secrets

# 定义中国时区
CHINA_TZ = timezone(timedelta(hours=8))

class APIKey(Base):
    __tablename__ = "api_keys"

    id = Column(Integer, primary_key=True, index=True)
    key_hash = Column(String, unique=True, index=True)
    total_quota = Column(Integer)
    remaining = Column(Integer)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)
    daily_usage_limit = Column(Integer, default=20)  # 每日使用限制，默认20
    first_used_at = Column(DateTime(timezone=True), nullable=True)  # 首次使用时间
    last_cookie_request_at = Column(DateTime(timezone=True), nullable=True) # 新增：最后一次成功获取Cookie的时间
    usage_logs = relationship("UsageLog", back_populates="api_key")

    def get_today_usage(self, db) -> int:
        """获取今天的使用次数"""
        # 使用中国时区计算今天的开始时间
        china_now = datetime.now(CHINA_TZ)
        today_start = china_now.replace(hour=0, minute=0, second=0, microsecond=0)
        # 转换为UTC时间进行数据库查询
        today_start_utc = today_start.astimezone(timezone.utc)
        
        # 使用数据库查询统计今天的使用次数
        return db.query(func.count(UsageLog.id)).filter(
            UsageLog.api_key_id == self.id,
            UsageLog.timestamp >= today_start_utc
        ).scalar() or 0

    def get_usage_count_last_n_hours(self, db, hours: int) -> int:
        """获取过去 N 小时内的使用次数"""
        start_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        return db.query(func.count(UsageLog.id)).filter(
            UsageLog.api_key_id == self.id,
            UsageLog.timestamp >= start_time
        ).scalar() or 0

    def check_basic_validity(self) -> bool:
        """检查API key的基本有效性（不包括每日限制）"""
        if not self.is_active:
            return False
        if self.remaining <= 0:
            return False
        if self.expires_at is None:
            return False
            
        # 计算实际过期时间
        current_time = datetime.now(timezone.utc)
        # 如果尚未使用，则使用固定过期时间
        if self.first_used_at is None:
            if current_time > self.expires_at:
                return False
        else:
            # 如果已经使用过，计算动态过期时间
            # 计算创建时设定的有效期（使用秒数计算更精确）
            delta_seconds = (self.expires_at - self.created_at).total_seconds()
            # 从首次使用时间开始计算新的过期时间
            dynamic_expiry = self.first_used_at + timedelta(seconds=delta_seconds)
            if current_time > dynamic_expiry:
                return False
                
        return True

    def is_valid(self) -> bool:
        """检查API key是否有效，包括过期检查和每日限制检查"""
        if not self.is_active:
            return False
        if self.remaining <= 0:
            return False
        if self.expires_at is None:
            return False
            
        # 计算实际过期时间
        current_time = datetime.now(timezone.utc)
        # 如果尚未使用，则使用固定过期时间
        if self.first_used_at is None:
            if current_time > self.expires_at:
                return False
        else:
            # 如果已经使用过，计算动态过期时间
            # 计算创建时设定的有效期（使用秒数计算更精确）
            delta_seconds = (self.expires_at - self.created_at).total_seconds()
            # 从首次使用时间开始计算新的过期时间
            dynamic_expiry = self.first_used_at + timedelta(seconds=delta_seconds)
            if current_time > dynamic_expiry:
                return False
        
        # 获取数据库会话
        db = next(get_db())
        try:
            # 检查每日使用限制
            today_usage = self.get_today_usage(db)
            if today_usage >= self.daily_usage_limit:
                return False
        finally:
            db.close()
            
        return True

    @staticmethod
    def generate_key(expires_days=None):
        key = secrets.token_hex(32)  # 生成64字符的随机字符串
        api_key = APIKey(key_hash=key)
        if expires_days:
            api_key.expires_at = datetime.now(timezone.utc) + timedelta(days=expires_days)
        else:
            # 如果没有指定过期时间，设置为2999年12月31日
            api_key.expires_at = datetime(2999, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
        return api_key

class Cookie(Base):
    __tablename__ = "cookies"

    id = Column(Integer, primary_key=True, index=True)
    value = Column(String, unique=True, index=True)
    is_available = Column(Boolean, default=True)
    last_used_at = Column(DateTime(timezone=True), nullable=True)

class UsageLog(Base):
    __tablename__ = "usage_logs"

    id = Column(Integer, primary_key=True, index=True)
    api_key_id = Column(Integer, ForeignKey("api_keys.id"))
    used_cookie = Column(String)
    client_ip = Column(String)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    api_key = relationship("APIKey", back_populates="usage_logs") 