#!C:/Users/<USER>/AppData/Local/Programs/Python/Python310/python.exe
# start.py (放在与app文件夹同级的目录)
import os
import sys
import logging
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    # 从环境变量获取数据库连接信息
    # 数据库用户名，默认为"imcycyc"
    db_user = os.getenv("DB_USER", "上线测试")
    # 数据库密码，默认为"Ming98091.3" 
    db_password = os.getenv("DB_PASSWORD", "Ming98091.3")
    # 数据库主机名，默认为"1Panel-postgresql-7WTW"
    # db_host = os.getenv("DB_HOST", "1Panel-postgresql-HDNv")
    db_host = os.getenv("DB_HOST", "***************")  
    # 数据库端口，默认为"5432"
    db_port = os.getenv("DB_PORT", "5432")
    # 数据库名称，默认为"apikey"
    db_name = os.getenv("DB_NAME", "上线测试")
    
    # 构建数据库URL
    os.environ["DATABASE_URL"] = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    # 先安装依赖
    os.system("pip3 install -r requirements.txt")
    
    # 数据库初始化策略
    try:
        # 尝试使用迁移脚本
        logger.info("开始执行数据库迁移...")
        try:
            from db_migration import run_migration
            run_migration()
            logger.info("数据库迁移完成")
        except ImportError as e:
            logger.error(f"导入迁移脚本失败: {str(e)}")
            logger.info("将尝试使用基本的表初始化...")
            # 回退到基本的表初始化
            from app.database import Base, engine
            from app.models import *  # 导入所有models
            logger.info("正在初始化数据库表...")
            Base.metadata.create_all(bind=engine)
            logger.info("数据库表初始化完成")
        except Exception as e:
            logger.error(f"执行迁移脚本失败: {str(e)}")
            logger.error(traceback.format_exc())
            logger.info("将尝试使用基本的表初始化...")
            # 回退到基本的表初始化
            from app.database import Base, engine
            from app.models import *  # 导入所有models
            logger.info("正在初始化数据库表...")
            Base.metadata.create_all(bind=engine)
            logger.info("数据库表初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        logger.error(traceback.format_exc())
        logger.warning("将继续启动应用，但可能会出现问题")
    
    # 然后启动服务
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=7001)


    # http://localhost:3001/