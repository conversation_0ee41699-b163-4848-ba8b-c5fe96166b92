#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查找系统中已安装的Cursor程序（通过控制面板卸载列表和默认安装路径）
"""

import sys
import os
import platform

# 条件导入Windows特定模块
if platform.system() == "Windows":
    import winreg
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

def get_default_cursor_paths() -> List[Dict[str, str]]:
    """
    获取默认安装路径下的Cursor程序
    
    Returns:
        List[Dict[str, str]]: 包含Cursor默认安装信息的列表
    """
    default_paths = []
    
    try:
        system = platform.system()
        
        if system == "Windows":
            # Windows默认安装路径
            default_path = Path(os.getenv("LOCALAPPDATA", "")) / "Programs" / "Cursor"
            try:
                # 规范化路径
                default_path = default_path.resolve()
            except Exception as e:
                print(f"规范化路径时出错: {e}")
                
            if default_path.exists():
                app_path = default_path / "resources" / "app"
                if app_path.exists() and (app_path / "package.json").exists():
                    # 找到有效的默认安装
                    program_info = {
                        "名称": "Cursor (默认安装路径)",
                        "版本": "未知",
                        "安装位置": str(default_path),
                        "默认安装": True
                    }
                    
                    # 尝试从package.json读取版本信息
                    try:
                        import json
                        pkg_path = app_path / "package.json"
                        if pkg_path.exists():
                            with open(pkg_path, 'r', encoding='utf-8') as f:
                                pkg_data = json.load(f)
                                program_info["版本"] = pkg_data.get("version", "未知")
                    except Exception:
                        pass
                        
                    default_paths.append(program_info)
        
        elif system == "Darwin":  # macOS
            # Mac默认安装路径
            mac_default_paths = [
                Path("/Applications/Cursor.app"),
                Path.home() / "Applications" / "Cursor.app"
            ]
            
            for default_path in mac_default_paths:
                try:
                    # 规范化路径
                    default_path = default_path.resolve()
                except Exception as e:
                    print(f"规范化路径时出错: {e}")
                    continue
                    
                if default_path.exists():
                    app_path = default_path / "Contents" / "Resources" / "app"
                    if app_path.exists() and (app_path / "package.json").exists():
                        # 找到有效的默认安装
                        program_info = {
                            "名称": "Cursor (macOS)",
                            "版本": "未知",
                            "安装位置": str(default_path),
                            "默认安装": True
                        }
                        
                        # 尝试从package.json读取版本信息
                        try:
                            import json
                            pkg_path = app_path / "package.json"
                            if pkg_path.exists():
                                with open(pkg_path, 'r', encoding='utf-8') as f:
                                    pkg_data = json.load(f)
                                    program_info["版本"] = pkg_data.get("version", "未知")
                        except Exception:
                            pass
                            
                        default_paths.append(program_info)
        
    except Exception as e:
        print(f"检查默认路径时出错: {e}")
    
    return default_paths

def find_cursor_installations() -> List[Dict[str, str]]:
    """
    查找系统中所有已安装的Cursor程序
    
    Returns:
        List[Dict[str, str]]: 包含Cursor程序信息的列表，每个字典包含名称、版本、安装位置等信息
    """
    cursor_programs = []
    
    # 用于去重的路径集合，存储规范化后的小写路径
    unique_paths = set()
    
    try:
        system = platform.system()
        
        # Windows系统通过注册表查找已安装程序
        if system == "Windows":
            # 通过控制面板的卸载列表查找程序
            uninstall_keys = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            ]
            
            for base_key in [winreg.HKEY_LOCAL_MACHINE, winreg.HKEY_CURRENT_USER]:
                for uninstall_key in uninstall_keys:
                    try:
                        with winreg.OpenKey(base_key, uninstall_key) as key:
                            subkey_count = winreg.QueryInfoKey(key)[0]
                            
                            for i in range(subkey_count):
                                try:
                                    subkey_name = winreg.EnumKey(key, i)
                                    with winreg.OpenKey(key, subkey_name) as subkey:
                                        try:
                                            display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                            
                                            if "cursor" in display_name.lower():
                                                program_info = {
                                                    "名称": display_name,
                                                    "版本": "未知",
                                                    "安装位置": "未知",
                                                    "卸载命令": "未知",
                                                    "默认安装": False
                                                }
                                                
                                                try:
                                                    program_info["版本"] = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                                except:
                                                    pass
                                                    
                                                try:
                                                    program_info["安装位置"] = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                                except:
                                                    pass
                                                    
                                                try:
                                                    program_info["卸载命令"] = winreg.QueryValueEx(subkey, "UninstallString")[0]
                                                except:
                                                    pass
                                                    
                                                # 检查安装位置是否有效且不重复
                                                if program_info["安装位置"] and program_info["安装位置"] != "未知":
                                                    # 标准化路径并转为小写进行去重比较
                                                    norm_path = str(Path(program_info["安装位置"]).resolve()).lower()
                                                    if norm_path not in unique_paths:
                                                        unique_paths.add(norm_path)
                                                        cursor_programs.append(program_info)
                                                else:
                                                    # 如果没有有效安装位置，仍然添加到列表
                                                    cursor_programs.append(program_info)
                                        except:
                                            continue
                                except:
                                    continue
                    except:
                        continue
        
        # macOS系统，搜索应用程序文件夹
        elif system == "Darwin":
            # macOS上搜索常见的应用程序位置
            mac_app_locations = [
                Path("/Applications"),
                Path.home() / "Applications"
            ]
            
            for app_dir in mac_app_locations:
                if app_dir.exists():
                    # 查找所有.app文件
                    for app_path in app_dir.glob("*.app"):
                        if "cursor" in app_path.name.lower():
                            # 验证是否为有效的Cursor.app
                            resources_path = app_path / "Contents" / "Resources" / "app"
                            if resources_path.exists() and (resources_path / "package.json").exists():
                                # 尝试读取版本信息
                                version = "未知"
                                try:
                                    import json
                                    pkg_path = resources_path / "package.json"
                                    with open(pkg_path, 'r', encoding='utf-8') as f:
                                        pkg_data = json.load(f)
                                        version = pkg_data.get("version", "未知")
                                except Exception:
                                    pass
                                
                                # 创建程序信息
                                program_info = {
                                    "名称": f"Cursor ({app_path.name})",
                                    "版本": version,
                                    "安装位置": str(app_path),
                                    "默认安装": app_path.name == "Cursor.app"
                                }
                                
                                # 确保不重复
                                norm_path = str(app_path.resolve()).lower()
                                if norm_path not in unique_paths:
                                    unique_paths.add(norm_path)
                                    cursor_programs.append(program_info)
        
        # 检查默认安装路径
        default_installations = get_default_cursor_paths()
        
        # 将默认安装添加到列表中（如果它们还没有被添加）
        for default_install in default_installations:
            default_path = default_install["安装位置"]
            if default_path:
                # 标准化默认路径并转为小写
                norm_default_path = str(Path(default_path).resolve()).lower()
                
                # 检查这个路径是否已经在我们找到的安装中
                if norm_default_path not in unique_paths:
                    unique_paths.add(norm_default_path)
                    cursor_programs.append(default_install)
                else:
                    # 路径已存在，找到对应项并标记为默认安装
                    for existing in cursor_programs:
                        existing_path = existing.get("安装位置", "")
                        if existing_path:
                            norm_existing_path = str(Path(existing_path).resolve()).lower()
                            if norm_existing_path == norm_default_path:
                                existing["默认安装"] = True
                                break
                    
    except Exception as e:
        print(f"查询时发生错误: {e}")
        
    return cursor_programs

def validate_cursor_installation(install_location: str) -> bool:
    """
    验证Cursor安装路径是否有效
    
    Args:
        install_location: Cursor的安装路径
        
    Returns:
        bool: 如果是有效的Cursor安装路径则返回True，否则返回False
    """
    if not install_location:
        return False
    
    try:
        # 规范化路径，处理可能的路径差异（如结尾斜杠、相对路径等）
        path = Path(install_location).resolve()
        
        # 检查系统类型，区分Windows和Mac的验证逻辑
        system = platform.system()
        
        if system == "Darwin":  # macOS
            # 检查是否是.app包
            if path.name.endswith('.app'):
                # Mac上.app包的结构为 Cursor.app/Contents/Resources/app
                app_path = path / "Contents" / "Resources" / "app"
                return app_path.exists() and (app_path / "package.json").exists()
            else:
                # 如果用户选择了.app内部的某个目录，尝试向上查找
                parts = path.parts
                for i, part in enumerate(parts):
                    if part.endswith('.app'):
                        # 找到了.app，检查内部结构
                        app_path = Path(*parts[:i+1]) / "Contents" / "Resources" / "app"
                        if app_path.exists() and (app_path / "package.json").exists():
                            return True
                
                # 最后再尝试直接检查当前路径
                app_path = path / "package.json"
                return app_path.exists()
        else:  # Windows或其他
            # 检查resources/app/package.json是否存在
            app_path = path / "resources" / "app"
            return app_path.exists() and (app_path / "package.json").exists()
            
    except Exception as e:
        print(f"验证安装路径时出错: {e}")
        return False

def main():
    print("正在查找系统中已安装的Cursor程序...")
    cursor_programs = find_cursor_installations()
        
    # 输出结果
    if cursor_programs:
        print(f"\n找到 {len(cursor_programs)} 个Cursor程序:")
        for idx, program in enumerate(cursor_programs, 1):
            print(f"\n{idx}. {program['名称']}")
            print(f"   版本: {program['版本']}")
            print(f"   安装位置: {program['安装位置']}")
            if "卸载命令" in program:
                print(f"   卸载命令: {program['卸载命令']}")
                
            # 验证安装位置
            install_location = program.get("安装位置", "")
            if install_location:
                is_valid = validate_cursor_installation(install_location)
                print(f"   有效安装: {'是' if is_valid else '否'}")
                
            # 标记默认安装
            if program.get("默认安装", False):
                print(f"   默认安装: 是")
    else:
        print("\n未找到任何Cursor程序。")
    
    print("\n此程序通过查询控制面板的卸载列表和默认安装路径来获取已安装的Cursor程序信息。")
    
    # 防止窗口立即关闭
    input("\n按回车键退出...")

if __name__ == "__main__":
    main() 