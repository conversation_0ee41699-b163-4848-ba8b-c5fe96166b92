import requests
import logging
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
import platform
import ssl
import urllib3  # 添加直接导入
import traceback

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

@dataclass
class AccountQuota:
    total: int
    remaining: int
    used: int
    expires_at: Optional[str] = None  # 添加过期时间字段，默认为None
    daily_usage_limit: int = 0 # 新增：每日总限额
    today_usage: int = 0       # 新增：今日已使用

@dataclass
class AccountStatus:
    is_valid: bool
    expires_at: Optional[str]
    message: str

class AccountChecker:
    def __init__(self, base_url: str, api_key: str):
        logger.info("=== 初始化账号检查器 ===")
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {"X-API-Key": api_key}
        logger.info(f"基础URL: {base_url}")
        logger.debug(f"API Key前缀: {api_key[:5]}...")  # 只显示API Key的前缀
        
        # 为macOS创建自定义会话
        self.session = requests.Session()
        if platform.system() == "Darwin":  # macOS
            logger.info("检测到macOS系统，配置SSL验证...")
            self.session.verify = False
            # 禁用警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            logger.info("已禁用SSL验证和警告")

    def get_quota_info(self) -> Optional[AccountQuota]:
        """获取账号配额信息"""
        logger.info("=== 开始获取配额信息 ===")
        try:
            url = f"{self.base_url}/api/keys/{self.api_key}/quota"
            logger.info(f"请求URL: {url}")
            logger.debug("发送GET请求获取配额...")
            
            response = self.session.get(url)
            logger.info(f"响应状态码: {response.status_code}")
            response.raise_for_status()
            
            data = response.json()
            logger.debug(f"原始响应数据: {data}")
            
            total = data['total']
            remaining = data['remaining']
            expires_at = data.get('expires_at')
            # 新增：读取每日限额和今日使用
            daily_usage_limit = data.get('daily_usage_limit', 0)
            today_usage = data.get('today_usage', 0)
            
            logger.info("=== 配额信息解析结果 ===")
            logger.info(f"总配额: {total}")
            logger.info(f"剩余配额: {remaining}")
            logger.info(f"已使用: {total - remaining}")
            logger.info(f"使用率: {((total - remaining) / total * 100):.2f}%" if total > 0 else "N/A") # 避免除零错误
            logger.info(f"过期时间: {expires_at or '永不过期'}")
            logger.info(f"每日限额: {daily_usage_limit}") # 新增日志
            logger.info(f"今日已用: {today_usage}")     # 新增日志
            
            # 添加配额警告
            usage_percent = ((total - remaining) / total * 100) if total > 0 else 0
            if usage_percent > 90:
                logger.warning("⚠️ 配额使用已超过90%，请注意及时更新")
            elif usage_percent > 80:
                logger.warning("⚠️ 配额使用已超过80%，请注意关注")
            
            return AccountQuota(
                total=total,
                remaining=remaining,
                used=total - remaining,
                expires_at=expires_at,
                daily_usage_limit=daily_usage_limit, # 新增
                today_usage=today_usage            # 新增
            )
        except requests.exceptions.RequestException as e:
            logger.error(f"请求配额信息失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None
        except (KeyError, ValueError) as e:
            logger.error(f"解析配额数据失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None
        except Exception as e:
            logger.error(f"获取配额信息时发生未知错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None

    def check_auth_status(self) -> AccountStatus:
        """检查API认证状态"""
        logger.info("=== 开始检查API认证状态 ===")
        try:
            url = f"{self.base_url}/api/auth"
            logger.info(f"认证URL: {url}")
            logger.debug("发送POST请求验证认证...")
            
            response = self.session.post(url, headers=self.headers)
            logger.info(f"认证响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("API认证成功，正在获取配额信息...")
                # 获取配额信息（包含过期时间）
                quota_info = self.get_quota_info()
                if quota_info:
                    logger.info(f"配额信息获取成功，过期时间: {quota_info.expires_at or '永不过期'}")
                    return AccountStatus(True, quota_info.expires_at, "API Key有效且已启用")
                logger.warning("API认证成功但未获取到配额信息")
                return AccountStatus(True, None, "API Key有效且已启用")
            elif response.status_code == 403:
                # 处理每日限额达到的情况
                try:
                    error_data = response.json()
                    if "detail" in error_data:
                        logger.warning(f"API Key使用受限: {error_data['detail']}")
                        # 直接返回后端的错误信息
                        return AccountStatus(False, None, error_data["detail"])
                except Exception:
                    pass
                # 如果无法解析JSON或没有匹配到预期内容，使用通用错误消息
                logger.warning("API Key使用受限")
                return AccountStatus(False, None, "API Key使用受限，可能是每日使用次数已达上限，请明天再试或联系客服")
            elif response.status_code == 401:
                logger.error("无效的API Key")
                return AccountStatus(False, None, "无效的API Key")
            elif response.status_code == 429:
                # 处理配额用完的情况
                try:
                    error_data = response.json()
                    if "detail" in error_data and "配额已用完" in error_data["detail"]:
                        logger.warning("API Key配额已用完")
                        return AccountStatus(False, None, "当前API Key配额已用完，请更换其他API Key或联系客服续费")
                except Exception:
                    pass
                # 如果无法解析JSON或没有匹配到预期内容，使用通用错误消息
                logger.warning("API请求受限，可能是配额已用完")
                return AccountStatus(False, None, "API请求受限，可能是配额已用完，请更换API Key或稍后再试")
            else:
                error_msg = f"未知错误: {response.text}"
                logger.error(error_msg)
                return AccountStatus(False, None, error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"请求异常: {str(e)}"
            logger.error(error_msg)
            logger.error(f"错误详情: {traceback.format_exc()}")
            return AccountStatus(False, None, error_msg)
        except Exception as e:
            error_msg = f"检查认证状态时发生未知错误: {str(e)}"
            logger.error(error_msg)
            logger.error(f"错误详情: {traceback.format_exc()}")
            return AccountStatus(False, None, error_msg)

def utc_to_china_timezone(utc_datetime):
    """
    将UTC时间转换为中国时区（UTC+8）时间
    
    Args:
        utc_datetime: UTC时区的datetime对象
        
    Returns:
        中国时区的datetime对象
    """
    china_timezone = timezone(timedelta(hours=8))
    # 如果输入的时间没有时区信息，假定它是UTC时间
    if utc_datetime.tzinfo is None:
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
    # 转换到中国时区
    return utc_datetime.astimezone(china_timezone)

def format_expires_at(expires_at: Optional[str]) -> str:
    """格式化过期时间显示"""
    logger.debug(f"格式化过期时间: {expires_at}")
    if not expires_at:
        logger.info("无过期时间，返回'永不过期'")
        return "永不过期"
    try:
        expires_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
        now = datetime.now(timezone.utc)
        
        # 转换为中国时区
        china_expires_date = utc_to_china_timezone(expires_date)
        china_now = utc_to_china_timezone(now)
        
        diff_days = (expires_date - now).days
        diff_hours = ((expires_date - now).seconds // 3600)
        
        logger.debug(f"UTC过期时间: {expires_date}")
        logger.debug(f"中国时区过期时间: {china_expires_date}")
        logger.debug(f"UTC当前时间: {now}")
        logger.debug(f"中国时区当前时间: {china_now}")
        logger.debug(f"相差天数: {diff_days}")
        logger.debug(f"相差小时: {diff_hours}")
        
        if diff_days < 0:
            result = f"{china_expires_date.strftime('%Y-%m-%d %H:%M:%S')} (已过期)"
            logger.warning(f"账号已过期: {result}")
            return result
        elif diff_days == 0:
            result = f"{china_expires_date.strftime('%Y-%m-%d %H:%M:%S')} (剩余 {diff_hours} 小时)"
            logger.warning(f"账号即将过期: {result}")
            return result
        result = f"{china_expires_date.strftime('%Y-%m-%d %H:%M:%S')} (剩余 {diff_days} 天)"
        logger.info(f"过期时间格式化结果: {result}")
        return result
    except ValueError as e:
        logger.error(f"日期格式解析错误: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return str(expires_at)

def main():
    # API配置
    # BASE_URL = "http://localhost:3001"  # 本地测试地址
    PRIMARY_URL = "https://api2.naoy.me"  # 主服务器
    BACKUP_URL = "https://api2.naoy.me"  # 备用服务器
    
    # 默认使用主服务器，如果失败则尝试备用服务器
    BASE_URL = PRIMARY_URL
    
    # 测试用的API Keys
    # API_KEY = "xMAT70Rxt2eYL6IALutGIF4kkIdzF65HtobTm8f6A0s"  # 过期
    # API_KEY = "tMfaJE-CtCqYrzZwjwGx2iAtCFyBs98j6p6dlmsuXi0"  # 有效
    API_KEY = "163f4d1e32c7b9477c590185f378ee5fc3ebda6f96c38376930eafcfcab8fb8d"  # 使用main.py中正常工作的key

    logger.info("开始检查账号信息...")
    logger.info(f"API Key: {API_KEY}")
    
    # 尝试使用主服务器
    checker = AccountChecker(BASE_URL, API_KEY)
    auth_status = checker.check_auth_status()
    
    # 如果主服务器连接失败，尝试备用服务器
    if not auth_status.is_valid:
        logger.warning(f"主服务器连接失败，尝试使用备用服务器")
        BASE_URL = BACKUP_URL
        checker = AccountChecker(BASE_URL, API_KEY)
    
    # 获取配额信息（包含过期时间）
    quota = checker.get_quota_info()
    if quota:
        usage_percent = quota.used / quota.total * 100
        logger.info("\n=== API Key信息 ===")
        logger.info(f"过期时间: {format_expires_at(quota.expires_at)}")
        logger.info(f"总配额: {quota.total}")
        logger.info(f"剩余配额: {quota.remaining}")
        logger.info(f"已使用: {quota.used}")
        logger.info(f"使用率: {usage_percent:.2f}%")
        
        # 添加配额警告
        if usage_percent > 90:
            logger.warning("⚠️ 配额使用已超过90%，请注意及时更新")
        elif usage_percent > 80:
            logger.warning("⚠️ 配额使用已超过80%，请注意关注")
    
    # 检查认证状态
    logger.info("\n=== API认证状态 ===")
    logger.info(f"状态: {auth_status.message}")
    
    logger.info("\n检查完成")

if __name__ == "__main__":
    main() 