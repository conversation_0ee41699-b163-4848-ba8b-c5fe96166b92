#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析main.js文件修改前后的差异
"""

import re
import difflib
from pathlib import Path

def read_file_content(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return None

def find_machine_id_functions(content):
    """查找机器码相关的函数"""
    patterns = [
        r"async getMachineId\(\)[^}]+\}",
        r"async getMacMachineId\(\)[^}]+\}",
        r"getMachineId\(\)[^}]+\}",
        r"getMacMachineId\(\)[^}]+\}",
    ]
    
    found_functions = []
    for pattern in patterns:
        matches = re.finditer(pattern, content, re.DOTALL)
        for match in matches:
            found_functions.append({
                'pattern': pattern,
                'match': match.group(),
                'start': match.start(),
                'end': match.end()
            })
    
    return found_functions

def analyze_changes():
    """分析main.js文件的变化"""
    print("=== 分析main.js文件修改情况 ===\n")
    
    # 文件路径
    before_file = Path("main.js对比/main修改之前.js")
    after_file = Path("main.js对比/main修改之后.js")
    
    # 检查文件是否存在
    if not before_file.exists():
        print(f"错误: 文件 {before_file} 不存在")
        return
    
    if not after_file.exists():
        print(f"错误: 文件 {after_file} 不存在")
        return
    
    # 读取文件内容
    before_content = read_file_content(before_file)
    after_content = read_file_content(after_file)
    
    if not before_content or not after_content:
        print("无法读取文件内容")
        return
    
    print(f"修改前文件大小: {len(before_content)} 字符")
    print(f"修改后文件大小: {len(after_content)} 字符")
    print(f"大小差异: {len(after_content) - len(before_content)} 字符\n")
    
    # 查找机器码相关函数
    print("=== 查找机器码相关函数 ===")
    before_functions = find_machine_id_functions(before_content)
    after_functions = find_machine_id_functions(after_content)
    
    print(f"修改前找到 {len(before_functions)} 个机器码相关函数")
    print(f"修改后找到 {len(after_functions)} 个机器码相关函数\n")
    
    # 显示找到的函数
    if before_functions:
        print("修改前的机器码函数:")
        for i, func in enumerate(before_functions):
            print(f"  {i+1}. {func['match'][:100]}...")
        print()
    
    if after_functions:
        print("修改后的机器码函数:")
        for i, func in enumerate(after_functions):
            print(f"  {i+1}. {func['match'][:100]}...")
        print()
    
    # 检查具体的修改模式
    print("=== 检查Python脚本中定义的修改模式 ===")
    patterns = {
        r"async getMachineId\(\)\{return [^??]+\?\?([^}]+)\}": r"async getMachineId(){return \1}",
        r"async getMacMachineId\(\)\{return [^??]+\?\?([^}]+)\}": r"async getMacMachineId(){return \1}"
    }
    
    for pattern, replacement in patterns.items():
        print(f"\n模式: {pattern}")
        print(f"替换: {replacement}")
        
        # 在修改前的内容中查找
        before_matches = re.findall(pattern, before_content)
        after_matches = re.findall(pattern, after_content)
        
        print(f"修改前匹配数量: {len(before_matches)}")
        print(f"修改后匹配数量: {len(after_matches)}")
        
        if before_matches:
            print("修改前匹配内容:")
            for match in before_matches:
                print(f"  - {match}")
        
        if after_matches:
            print("修改后匹配内容:")
            for match in after_matches:
                print(f"  - {match}")
    
    # 生成详细的差异报告
    print("\n=== 生成差异报告 ===")
    
    # 按行分割内容进行比较
    before_lines = before_content.splitlines()
    after_lines = after_content.splitlines()
    
    # 生成统一差异格式
    diff = list(difflib.unified_diff(
        before_lines, 
        after_lines, 
        fromfile='修改前.js', 
        tofile='修改后.js', 
        lineterm=''
    ))
    
    if diff:
        print("发现以下差异:")
        for line in diff[:50]:  # 只显示前50行差异
            print(line)
        
        if len(diff) > 50:
            print(f"... 还有 {len(diff) - 50} 行差异")
    else:
        print("未发现任何差异")
    
    # 检查是否有实际的功能性修改
    print("\n=== 功能性修改分析 ===")
    
    # 查找可能的机器码检查绕过
    bypass_patterns = [
        r"return [^}]*\?\?",  # 查找 ?? 操作符
        r"getMachineId",
        r"getMacMachineId",
    ]
    
    for pattern in bypass_patterns:
        before_count = len(re.findall(pattern, before_content))
        after_count = len(re.findall(pattern, after_content))
        
        if before_count != after_count:
            print(f"模式 '{pattern}' 出现次数变化: {before_count} -> {after_count}")

if __name__ == "__main__":
    analyze_changes()
