import os
import shutil
import subprocess
import sys
from PyInstaller.__main__ import run
import platform

def clean_build():
    """清理build和dist目录"""
    dirs_to_clean = ['build', 'dist']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    if os.path.exists('main.spec'):
        os.remove('main.spec')
    if os.path.exists('ARM_重置2.5.spec'):
        os.remove('ARM_重置2.5.spec')
    if os.path.exists('Intel_重置2.5.spec'):
        os.remove('Intel_重置2.5.spec')
    if os.path.exists('WIN_重置2.5.spec'):
        os.remove('WIN_重置2.5.spec')

def create_config():
    """创建配置文件"""
    if not os.path.exists('config.json'):
        default_config = {
            'save_api_key': True,
            'api_key': '',
            'refresh_interval': {
                'min': 60,
                'max': 80
            },
            'auto_refresh': True
        }
        import json
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=4, ensure_ascii=False)

def build_exe():
    """编译主程序"""
    # 获取当前系统平台和架构
    current_platform = platform.system()
    current_arch = platform.machine()
    
    # 打印构建信息
    print("正在打包应用程序...")
    print(f"平台: {current_platform}")
    print(f"架构: {current_arch}")
    print("注意: 生产环境打包，将禁用所有控制台日志输出")
    
    # 基本的PyInstaller参数
    args = [
        'main.py',
        '--noconfirm',
        '--onefile',
        '--windowed',
        '--icon=app.ico',  # 如果有图标的话
        '--hidden-import=PIL._tkinter_finder',
        '--hidden-import=tkinterdnd2',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=_tkinter',
        '--hidden-import=ctypes',  # 添加ctypes库
        '--collect-all=tkinterdnd2',  # 收集所有tkinterdnd2相关文件
    ]
    
    # 添加环境变量定义，确保日志被禁用
    args.append('--add-data=config.json:.')
    
    # 根据当前平台和架构设置不同的名称
    if current_platform == 'Darwin':  # macOS
        if current_arch == 'arm64':
            # Apple Silicon (M1/M2/M3)
            args.append('--name=ARM_重置2.6.1测')
        else:
            # Intel
            args.append('--name=Intel_重置2.6.1测')
    elif current_platform == 'Windows':
        # Windows系统
        args.append('--name=WIN_重置2.7测')
    else:
        # 其他系统（如Linux）
        args.append('--name=Cursor重置工具专业版')
    
    # 使用PyInstaller Python API
    run(args)
    
    # 创建一个README文件，说明不同版本的用途
    os.makedirs('dist', exist_ok=True)
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write('Cursor重置工具专业版使用说明\n\n')
        f.write('1. ARM_重置2.5.app - 适用于搭载M1/M2/M3芯片的Mac电脑\n')
        f.write('2. Intel_重置2.5.app - 适用于搭载Intel芯片的Mac电脑\n')
        f.write('3. WIN_重置2.5.exe - 适用于Windows系统\n\n')
        f.write('请根据您的操作系统类型选择对应的版本运行。\n')

def main():
    """主函数"""
    print("开始构建...")
    
    # 清理旧的构建文件
    print("清理旧的构建文件...")
    clean_build()
    
    # 创建配置文件
    print("创建配置文件...")
    create_config()
    
    # 编译程序
    print("正在编译程序...")
    build_exe()
    
    print("构建完成！")
    print(f"当前系统平台: {platform.system()}")
    print(f"当前系统架构: {platform.machine()}")
    
    # 根据不同平台显示不同的提示信息
    if platform.system() == 'Darwin':
        print("请注意：当前构建的版本只适用于相同架构的Mac系统。")
        print("如需支持Intel Mac，请在Intel Mac上运行此脚本。")
    elif platform.system() == 'Windows':
        print("请注意：当前构建的版本适用于Windows系统。")
    else:
        print("请注意：当前构建的版本可能不适用于所有系统。")

if __name__ == "__main__":
    main() 