#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor IDE 自动更新管理器
用于在账号切换过程中自动禁用Cursor IDE的自动更新功能
"""

import json
import os
import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
import platform

# 获取日志记录器
logger = logging.getLogger(__name__)


def get_saved_cursor_path() -> Optional[str]:
    """
    从主程序的配置文件中获取已保存的cursor路径

    Returns:
        Optional[str]: 已保存的cursor路径，如果未找到则返回None
    """
    try:
        system_name = platform.system()

        if system_name == "Windows":
            config_dir = Path(os.getenv('LOCALAPPDATA')) / 'CursorPro'
        elif system_name == "Darwin":  # macOS
            config_dir = Path.home() / 'Library' / 'Application Support' / 'CursorPro'
        else:  # Linux
            config_dir = Path.home() / '.config' / 'CursorPro'

        config_path = config_dir / 'settings.dat'

        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            cursor_path = config.get('cursor_path')
            if cursor_path:
                logger.info(f"从配置文件中获取到cursor路径: {cursor_path}")
                return cursor_path
            else:
                logger.info("配置文件中没有cursor_path设置")
                return None
        else:
            logger.info("主程序配置文件不存在")
            return None

    except Exception as e:
        logger.error(f"读取主程序配置文件时出错: {e}")
        return None


class CursorAutoUpdateManager:
    """Cursor自动更新管理器"""

    def __init__(self):
        """初始化管理器"""
        self.system = platform.system()
        logger.info(f"初始化CursorAutoUpdateManager，当前系统: {self.system}")

        # 尝试确保Config.CURSOR_PATH被正确设置
        self._ensure_cursor_path_set()

    def _ensure_cursor_path_set(self):
        """确保Config.CURSOR_PATH被正确设置"""
        try:
            # 尝试导入Config类
            from update_cursor_token_main import Config

            # 如果Config.CURSOR_PATH为空，尝试从配置文件中读取
            if not Config.CURSOR_PATH:
                saved_path = get_saved_cursor_path()
                if saved_path:
                    Config.CURSOR_PATH = saved_path
                    logger.info(f"已设置Config.CURSOR_PATH为: {Config.CURSOR_PATH}")
                else:
                    logger.info("无法从配置文件中获取cursor路径，但这不影响自动更新禁用功能")
            else:
                logger.info(f"Config.CURSOR_PATH已设置: {Config.CURSOR_PATH}")

        except ImportError:
            logger.warning("无法导入update_cursor_token_main模块，跳过Config.CURSOR_PATH设置")
        except Exception as e:
            logger.error(f"设置Config.CURSOR_PATH时出错: {e}")

    def get_cursor_settings_path(self) -> Optional[Path]:
        """
        获取Cursor settings.json文件路径
        直接使用系统标准路径，不依赖Cursor安装路径

        Returns:
            Optional[Path]: settings.json文件路径，如果无法确定则返回None
        """
        try:
            if self.system == "Windows":  # Windows
                appdata = os.environ.get('APPDATA')
                if appdata:
                    settings_path = Path(appdata) / 'Cursor' / 'User' / 'settings.json'
                    logger.info(f"Windows系统Cursor设置文件路径: {settings_path}")
                    return settings_path
                else:
                    logger.error("无法获取Windows APPDATA环境变量")
                    return None

            elif self.system == "Darwin":  # macOS
                home = Path.home()
                settings_path = home / 'Library' / 'Application Support' / 'Cursor' / 'User' / 'settings.json'
                logger.info(f"macOS系统Cursor设置文件路径: {settings_path}")
                return settings_path

            elif self.system == "Linux":  # Linux
                home = Path.home()
                settings_path = home / '.config' / 'Cursor' / 'User' / 'settings.json'
                logger.info(f"Linux系统Cursor设置文件路径: {settings_path}")
                return settings_path
            else:
                logger.error(f"不支持的操作系统: {self.system}")
                return None

        except Exception as e:
            logger.error(f"获取Cursor设置文件路径时出错: {e}")
            return None
    
    def backup_settings(self, settings_path: Path) -> Optional[Path]:
        """
        备份原始设置文件
        
        Args:
            settings_path: 设置文件路径
            
        Returns:
            Optional[Path]: 备份文件路径，如果备份失败则返回None
        """
        try:
            if settings_path.exists():
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = settings_path.with_suffix(f'.backup_{timestamp}.json')
                shutil.copy2(settings_path, backup_path)
                logger.info(f"已备份原始设置文件到: {backup_path}")
                return backup_path
            else:
                logger.info("设置文件不存在，无需备份")
                return None
        except Exception as e:
            logger.error(f"备份设置文件时出错: {e}")
            return None
    
    def load_current_settings(self, settings_path: Path) -> Dict[str, Any]:
        """
        加载当前设置

        Args:
            settings_path: 设置文件路径

        Returns:
            Dict[str, Any]: 当前设置字典，如果加载失败返回空字典
        """
        if settings_path.exists():
            try:
                with open(settings_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 处理JSONC格式（带注释的JSON）
                # 移除单行注释 //
                lines = content.split('\n')
                cleaned_lines = []
                for line in lines:
                    # 查找注释位置，但要避免字符串内的//
                    in_string = False
                    escape_next = False
                    comment_pos = -1

                    for i, char in enumerate(line):
                        if escape_next:
                            escape_next = False
                            continue
                        if char == '\\':
                            escape_next = True
                            continue
                        if char == '"' and not escape_next:
                            in_string = not in_string
                            continue
                        if not in_string and char == '/' and i + 1 < len(line) and line[i + 1] == '/':
                            comment_pos = i
                            break

                    if comment_pos >= 0:
                        line = line[:comment_pos].rstrip()

                    cleaned_lines.append(line)

                # 过滤空行
                cleaned_lines = [line for line in cleaned_lines if line.strip()]

                # 处理尾随逗号问题
                cleaned_content = '\n'.join(cleaned_lines)

                # 移除对象和数组中的尾随逗号
                import re
                # 移除对象中的尾随逗号 (,})
                cleaned_content = re.sub(r',(\s*})', r'\1', cleaned_content)
                # 移除数组中的尾随逗号 (,])
                cleaned_content = re.sub(r',(\s*])', r'\1', cleaned_content)

                settings = json.loads(cleaned_content)
                logger.info(f"成功加载当前设置，包含 {len(settings)} 项配置")
                return settings

            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                logger.error(f"读取设置文件时出错: {e}")
                logger.error(f"这可能是因为JSON格式错误或包含不支持的注释格式")
                return {}
        else:
            logger.info("设置文件不存在，返回空设置")
            return {}
    
    def get_disable_auto_update_settings(self) -> Dict[str, Any]:
        """
        返回禁用自动更新的设置
        
        Returns:
            Dict[str, Any]: 禁用自动更新的设置字典
        """
        return {
            "update.enableWindowsBackgroundUpdates": False,
            "update.mode": "manual",
            "update.showReleaseNotes": False
        }
    
    def update_settings(self, current_settings: Dict[str, Any], new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新设置
        
        Args:
            current_settings: 当前设置
            new_settings: 新设置
            
        Returns:
            Dict[str, Any]: 更新后的设置
        """
        updated_settings = current_settings.copy()
        updated_settings.update(new_settings)
        logger.info(f"设置已更新，新增/修改了 {len(new_settings)} 项配置")
        return updated_settings
    
    def save_settings(self, settings_path: Path, settings: Dict[str, Any]) -> bool:
        """
        保存设置到文件
        
        Args:
            settings_path: 设置文件路径
            settings: 要保存的设置
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            settings_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            logger.info(f"设置已保存到: {settings_path}")
            return True
        except Exception as e:
            logger.error(f"保存设置时出错: {e}")
            return False
    
    def disable_auto_update(self) -> bool:
        """
        禁用Cursor自动更新
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logger.info("开始禁用Cursor自动更新")
            
            # 获取设置文件路径
            settings_path = self.get_cursor_settings_path()
            if not settings_path:
                logger.error("无法确定Cursor设置文件路径")
                return False
            
            logger.info(f"设置文件路径: {settings_path}")
            
            # 备份原始设置
            backup_path = self.backup_settings(settings_path)
            if backup_path:
                logger.info(f"已创建备份文件: {backup_path}")
            
            # 加载当前设置
            current_settings = self.load_current_settings(settings_path)
            
            # 获取禁用自动更新的设置
            disable_settings = self.get_disable_auto_update_settings()
            
            # 检查是否需要更新
            needs_update = False
            for key, value in disable_settings.items():
                if current_settings.get(key) != value:
                    needs_update = True
                    logger.info(f"需要更新设置: {key} = {value} (当前值: {current_settings.get(key, '未设置')})")
            
            if not needs_update:
                logger.info("自动更新已经被禁用，无需修改")
                return True
            
            # 更新设置
            updated_settings = self.update_settings(current_settings, disable_settings)
            
            # 保存设置
            if self.save_settings(settings_path, updated_settings):
                logger.info("✅ Cursor自动更新已成功禁用!")
                logger.info("修改的设置项:")
                for key, value in disable_settings.items():
                    logger.info(f"  • {key}: {value}")
                return True
            else:
                logger.error("❌ 设置保存失败")
                return False
                
        except Exception as e:
            logger.error(f"禁用自动更新时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def is_auto_update_disabled(self) -> bool:
        """
        检查自动更新是否已被禁用

        Returns:
            bool: 如果自动更新已被禁用返回True，否则返回False
        """
        try:
            settings_path = self.get_cursor_settings_path()
            if not settings_path or not settings_path.exists():
                logger.info("设置文件不存在，认为自动更新未被禁用")
                return False

            current_settings = self.load_current_settings(settings_path)
            disable_settings = self.get_disable_auto_update_settings()

            for key, expected_value in disable_settings.items():
                current_value = current_settings.get(key)
                if current_value != expected_value:
                    logger.info(f"设置项 {key} 当前值为 {current_value}，期望值为 {expected_value}")
                    return False

            logger.info("所有自动更新设置项都已正确配置")
            return True

        except Exception as e:
            logger.error(f"检查自动更新状态时出错: {e}")
            return False

    def get_auto_update_status_info(self) -> Dict[str, Any]:
        """
        获取详细的自动更新状态信息

        Returns:
            Dict[str, Any]: 包含状态信息的字典
        """
        try:
            settings_path = self.get_cursor_settings_path()
            if not settings_path:
                return {
                    "status": "error",
                    "message": "无法确定设置文件路径",
                    "settings_path": None,
                    "file_exists": False,
                    "current_settings": {},
                    "expected_settings": self.get_disable_auto_update_settings()
                }

            file_exists = settings_path.exists()
            current_settings = self.load_current_settings(settings_path) if file_exists else {}
            expected_settings = self.get_disable_auto_update_settings()

            # 检查每个设置项的状态
            settings_status = {}
            all_correct = True

            for key, expected_value in expected_settings.items():
                current_value = current_settings.get(key)
                is_correct = current_value == expected_value
                settings_status[key] = {
                    "current": current_value,
                    "expected": expected_value,
                    "correct": is_correct
                }
                if not is_correct:
                    all_correct = False

            return {
                "status": "disabled" if all_correct else "enabled",
                "message": "自动更新已禁用" if all_correct else "自动更新未完全禁用",
                "settings_path": str(settings_path),
                "file_exists": file_exists,
                "current_settings": current_settings,
                "expected_settings": expected_settings,
                "settings_status": settings_status,
                "all_correct": all_correct
            }

        except Exception as e:
            logger.error(f"获取自动更新状态信息时出错: {e}")
            return {
                "status": "error",
                "message": f"获取状态信息时出错: {e}",
                "settings_path": None,
                "file_exists": False,
                "current_settings": {},
                "expected_settings": self.get_disable_auto_update_settings()
            }


def disable_cursor_auto_update() -> bool:
    """
    便捷函数：禁用Cursor自动更新
    
    Returns:
        bool: 操作是否成功
    """
    manager = CursorAutoUpdateManager()
    return manager.disable_auto_update()


def is_cursor_auto_update_disabled() -> bool:
    """
    便捷函数：检查Cursor自动更新是否已被禁用

    Returns:
        bool: 如果自动更新已被禁用返回True，否则返回False
    """
    manager = CursorAutoUpdateManager()
    return manager.is_auto_update_disabled()


def get_cursor_auto_update_status() -> Dict[str, Any]:
    """
    便捷函数：获取Cursor自动更新的详细状态信息

    Returns:
        Dict[str, Any]: 包含状态信息的字典
    """
    manager = CursorAutoUpdateManager()
    return manager.get_auto_update_status_info()


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🚀 Cursor IDE 自动更新禁用工具")
    print("=" * 50)
    
    success = disable_cursor_auto_update()
    if success:
        print("\n🎉 操作完成!")
    else:
        print("\n💥 操作失败!")
