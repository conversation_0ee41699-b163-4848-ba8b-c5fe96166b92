#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor隐私模式管理器
用于在换号过程中自动启用隐私模式
基于隐私模式测试成功.py，适配换号流程
"""

import asyncio
import aiohttp
import time
import logging
try:
    import jwt
except ImportError:
    jwt = None

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class CursorPrivacyManager:
    """Cursor隐私模式管理器 - 换号专用版本"""
    
    def __init__(self, token: str):
        """
        初始化隐私模式管理器
        
        Args:
            token: Cursor的访问令牌（可以是原始JWT或带用户ID的格式）
        """
        # 提取实际的JWT token
        actual_token = token.split('%3A%3A')[-1] if '%3A%3A' in token else token
        self.token = actual_token
        
        # 解析用户ID
        try:
            if jwt is None:
                raise ImportError("PyJWT库未安装")
            decoded = jwt.decode(actual_token, options={"verify_signature": False})
            self.user_id = decoded['sub'].split('|')[1]
            logger.info(f"成功解析用户ID: {self.user_id}")
        except Exception as e:
            logger.error(f"无法解析JWT token: {e}")
            raise ValueError(f"无法解析JWT token: {e}")
        
        # 设置最小头部组合（避免踩雷）
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/proto"
        }
        logger.debug("已设置请求头部")

    def _encode_varint(self, value):
        """编码protobuf varint"""
        result = []
        while value >= 0x80:
            result.append((value & 0x7F) | 0x80)
            value >>= 7
        result.append(value & 0x7F)
        return bytes(result)

    def _build_protobuf_message(self, privacy_enabled=True, code_training_opt_out=True):
        """构建protobuf消息 - 使用最简结构"""
        message = b""
        
        # 字段1: privacy_mode_enabled
        message += bytes([0x08, 0x01 if privacy_enabled else 0x00])
        
        # 字段3: code_training_opt_out  
        message += bytes([0x18, 0x01 if code_training_opt_out else 0x00])
        
        # 字段6: 时间戳
        timestamp = int(time.time() * 1000)
        message += bytes([0x30]) + self._encode_varint(timestamp)
        
        return message

    async def get_privacy_mode(self):
        """获取当前隐私模式设置"""
        logger.info("正在获取当前隐私模式设置...")
        async with aiohttp.ClientSession() as session:
            url = "https://api2.cursor.sh/aiserver.v1.DashboardService/GetUserPrivacyMode"
            try:
                async with session.post(url, data=b"", headers=self.headers, timeout=15) as response:
                    if response.status == 200:
                        data = await response.read()
                        logger.info("成功获取隐私模式设置")
                        return {"success": True, "data": data.hex()}
                    else:
                        error = await response.text()
                        logger.warning(f"获取隐私模式设置失败: HTTP {response.status}: {error}")
                        return {"success": False, "error": f"HTTP {response.status}: {error}"}
            except Exception as e:
                logger.error(f"获取隐私模式设置时发生异常: {e}")
                return {"success": False, "error": str(e)}

    async def set_privacy_mode(self, privacy_enabled=True, code_training_opt_out=True):
        """设置隐私模式"""
        logger.info(f"正在设置隐私模式: privacy_enabled={privacy_enabled}, code_training_opt_out={code_training_opt_out}")
        async with aiohttp.ClientSession() as session:
            url = "https://api2.cursor.sh/aiserver.v1.DashboardService/SetUserPrivacyMode"
            try:
                request_body = self._build_protobuf_message(privacy_enabled, code_training_opt_out)
                async with session.post(url, data=request_body, headers=self.headers, timeout=15) as response:
                    if response.status == 200:
                        logger.info("隐私模式设置成功")
                        return {"success": True}
                    else:
                        error = await response.text()
                        logger.warning(f"设置隐私模式失败: HTTP {response.status}: {error}")
                        return {"success": False, "error": f"HTTP {response.status}: {error}"}
            except Exception as e:
                logger.error(f"设置隐私模式时发生异常: {e}")
                return {"success": False, "error": str(e)}

    def parse_privacy_data(self, hex_data):
        """解析隐私模式数据"""
        try:
            data = bytes.fromhex(hex_data)
            if len(data) >= 2:
                field1_value = data[1]
                return {
                    "privacy_enabled": field1_value == 1,
                    "code_training_opt_out": field1_value == 1
                }
        except Exception as e:
            logger.error(f"解析隐私模式数据失败: {e}")
        return {"privacy_enabled": False, "code_training_opt_out": False}


async def enable_privacy_mode_for_token(token: str):
    """
    为指定token启用隐私模式的便捷函数
    
    Args:
        token: Cursor的访问令牌
        
    Returns:
        dict: 操作结果
    """
    logger.info("=== 开始为换号后的账号启用隐私模式 ===")
    
    try:
        # 检查PyJWT库
        if jwt is None:
            error_msg = "PyJWT库未安装，无法启用隐私模式"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        
        # 创建隐私模式管理器
        manager = CursorPrivacyManager(token)
        
        # 获取当前设置
        logger.info("获取当前隐私模式设置...")
        current = await manager.get_privacy_mode()
        if not current["success"]:
            logger.warning(f"获取当前设置失败: {current['error']}")
            # 不因为获取失败而中断，继续尝试设置
        
        # 设置隐私模式
        logger.info("正在启用隐私模式...")
        result = await manager.set_privacy_mode(privacy_enabled=True, code_training_opt_out=True)
        if not result["success"]:
            error_msg = f"设置隐私模式失败: {result['error']}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        
        # 验证设置
        logger.info("验证隐私模式设置...")
        verify = await manager.get_privacy_mode()
        if verify["success"]:
            settings = manager.parse_privacy_data(verify["data"])
            logger.info(f"隐私模式验证结果: privacy_enabled={settings['privacy_enabled']}, code_training_opt_out={settings['code_training_opt_out']}")
            
            return {
                "success": True,
                "user_id": manager.user_id,
                "privacy_enabled": settings["privacy_enabled"],
                "code_training_opt_out": settings["code_training_opt_out"],
                "before": current.get("data", "unknown"),
                "after": verify["data"]
            }
        else:
            logger.warning("验证隐私模式设置失败，但设置操作可能已成功")
            return {
                "success": True, 
                "user_id": manager.user_id,
                "message": "设置完成但验证失败"
            }
        
    except Exception as e:
        error_msg = f"启用隐私模式时发生异常: {str(e)}"
        logger.error(error_msg)
        logger.error(f"异常详情: {e}", exc_info=True)
        return {"success": False, "error": error_msg}


def enable_privacy_mode_sync(token: str):
    """
    同步版本的隐私模式启用函数，用于在主线程中调用
    
    Args:
        token: Cursor的访问令牌
        
    Returns:
        dict: 操作结果
    """
    try:
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(enable_privacy_mode_for_token(token))
            return result
        finally:
            loop.close()
            
    except Exception as e:
        error_msg = f"同步执行隐私模式设置时发生异常: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": error_msg}
