#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import subprocess
import psutil
import json
from urllib.parse import urlparse, unquote
import platform
import logging
import traceback

# 判断是否为开发环境
def is_development_mode():
    """
    检测当前是否处于开发模式（源代码运行）而非打包后的环境
    
    Returns:
        bool: 如果是开发模式返回True，否则返回False
    """
    # PyInstaller打包后，__file__变量会指向临时目录中的提取文件
    # 而sys.frozen属性会被设置
    return not getattr(sys, 'frozen', False)

# 全局日志显示控制
ENABLE_CONSOLE_LOGS = is_development_mode()  # 只在开发模式下启用控制台日志

# 条件导入Windows特定模块
if platform.system() == "Windows":
    import pythoncom
    import win32com.client

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

def is_admin():
    """判断当前进程是否以管理员身份运行（仅 Windows 用到）"""
    if platform.system() != "Windows":
        return False
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    except Exception:
        return False

def run_as_admin(args=None):
    """
    以管理员身份重新运行当前脚本（仅Windows系统）
    
    Args:
        args: 要传递给新进程的命令行参数列表
        
    Returns:
        bool: 是否成功启动管理员进程
    """
    if platform.system() != "Windows":
        logger.error("run_as_admin函数仅支持Windows系统")
        return False
        
    try:
        import ctypes
        import sys
        
        if args is None:
            args = []
            
        # 确保--admin参数存在，表示这是一个提权后的调用
        if "--admin" not in args:
            args.append("--admin")
            
        script_path = sys.executable
        params = " ".join([f'"{sys.argv[0]}"'] + [f'"{arg}"' for arg in args])
        
        logger.info(f"正在尝试以管理员身份启动: {script_path} {params}")
        
        # 使用ShellExecute API启动管理员进程
        ret = ctypes.windll.shell32.ShellExecuteW(
            None,  # hwnd
            "runas",  # 操作 - runas表示以管理员运行
            script_path,  # 可执行文件路径
            params,  # 参数
            None,  # 工作目录 - 使用默认
            1  # 窗口显示方式 - 正常显示
        )
        
        # ShellExecute返回值大于32表示成功
        if ret > 32:
            logger.info("管理员进程启动成功")
            return True
        else:
            logger.error(f"管理员进程启动失败，错误码: {ret}")
            return False
            
    except Exception as e:
        logger.error(f"提权过程中发生错误: {e}")
        return False

def path_to_url(path, protocol="cursor"):
    """
    将文件路径转换为URL协议格式
    """
    system = platform.system()
    path_str = str(path).replace("\\", "/")
    
    if system == "Windows":
        # Windows路径格式: cursor://file/c:/path/to/folder
        if ":" in path_str:  # 有驱动器号
            path_url = path_str.replace(":", "%3A")
            return f"{protocol}://file/{path_url}"
        else:
            return f"{protocol}://file/{path_str}"
    else:  # macOS或Linux
        # macOS/Linux路径格式: cursor://file//path/to/folder
        return f"{protocol}://file//{path_str}"

def start_cursor_with_url_protocol(folder_path=None):
    """
    使用URL协议启动Cursor，可以避免权限继承问题
    
    Args:
        folder_path: 要打开的文件夹路径，如果为None则只启动Cursor
        
    Returns:
        bool: 是否成功启动
    """
    system = platform.system()
    
    try:
        # 构建URL
        if folder_path and os.path.exists(folder_path):
            url = path_to_url(folder_path)
        else:
            url = "cursor://"  # 基本协议
        
        logger.info(f"使用URL协议启动Cursor: {url}")
        
        # 使用系统命令打开URL
        if system == "Windows":
            cmd = ["start", url]
            subprocess.run(cmd, shell=True, check=False)
        elif system == "Darwin":  # macOS
            cmd = ["open", url]
            subprocess.run(cmd, check=False)
        else:  # Linux
            cmd = ["xdg-open", url]
            subprocess.run(cmd, check=False)
            
        logger.info(f"已执行系统命令启动Cursor")
        return True
        
    except Exception as e:
        logger.error(f"使用URL协议启动Cursor失败: {e}")
        return False

def start_process_shell_com(executable, args=None, cwd=None):
    """
    使用Shell.Application COM以普通用户权限启动程序
    
    Args:
        executable: 要启动的程序路径
        args: 命令行参数（如果是文件夹路径，将被作为工作目录打开）
        cwd: 工作目录
        
    Returns:
        bool: 是否成功启动
    """
    # 此函数仅适用于Windows系统
    if platform.system() != "Windows":
        logger.error("start_process_shell_com函数仅支持Windows系统")
        return False
        
    try:
        # 初始化COM
        pythoncom.CoInitialize()
        shell = win32com.client.Dispatch("Shell.Application")
        
        # 准备启动参数
        if args and len(args) > 0:
            params = " ".join([f'"{arg}"' for arg in args])
            cmd_dir = cwd if cwd else os.path.dirname(executable)
            logger.info(f"使用Shell.Application COM启动: {executable} {params}")
            
            # 在Explorer进程中执行ShellExecute，确保以普通用户权限运行
            shell.ShellExecute(
                executable,    # 文件
                params,        # 参数
                cmd_dir,       # 工作目录
                "open",        # 动作
                1              # 显示方式 (1 = 正常窗口)
            )
        else:
            logger.info(f"使用Shell.Application COM启动: {executable}")
            shell.ShellExecute(
                executable,    # 文件
                "",            # 参数
                cwd if cwd else os.path.dirname(executable),  # 工作目录
                "open",        # 动作
                1              # 显示方式 (1 = 正常窗口)
            )
            
        # 释放COM
        pythoncom.CoUninitialize()
        return True
        
    except Exception as e:
        logger.error(f"使用Shell.Application COM启动失败: {e}")
        try:
            pythoncom.CoUninitialize()
        except:
            pass
        return False

def start_process_non_admin(executable, args=None, cwd=None, show_cmd=True):
    """
    在管理员进程中以【普通权限】启动可执行文件
    executable : 程序完整路径
    args       : 字符串参数列表（可为空）
    cwd        : 工作目录
    show_cmd   : True=窗口正常显示，False=隐藏
    return     : True/False
    """
    if platform.system() != "Windows":
        # 其它系统直接正常启动即可
        subprocess.Popen([executable] + (args or []),
                         cwd=cwd,
                         stdout=subprocess.DEVNULL,
                         stderr=subprocess.DEVNULL,
                         start_new_session=True)
        return True
    
    # 如果自身不是管理员，也没必要绕弯子
    if not is_admin():
        try:
            subprocess.Popen([executable] + (args or []),
                          cwd=cwd,
                          shell=False,
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW)
        except AttributeError:
            # 如果CREATE_NO_WINDOW不可用，尝试使用DETACHED_PROCESS
            subprocess.Popen([executable] + (args or []),
                          cwd=cwd,
                          shell=False,
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.DETACHED_PROCESS)
        return True
        
    # 在不同Windows版本上，有不同的方法可以降权启动进程
    # 使用Shell.Application COM方式启动（在管理员权限下也能以普通用户权限启动）
    return start_process_shell_com(executable, args, cwd)

class CursorRestartManager:
    """Cursor关闭与重启管理器，支持保存和恢复工作区"""
    
    def __init__(self):
        """初始化重启管理器"""
        self.system = platform.system()
        self.cursor_path = None
        self.active_workspaces = []
        
        if self.system not in ["Windows", "Darwin"]:
            logger.error(f"不支持的操作系统: {self.system}，此模块仅支持Windows和macOS")

    def get_cursor_window_count(self):
        """获取当前活跃的Cursor窗口数量"""
        renderer_procs = []
        
        try:
            # 查找所有Cursor Renderer进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 检查是否为Cursor的Renderer进程
                    is_cursor = False
                    is_renderer = False
                    
                    if proc.info['name'] and ('Cursor' in proc.info['name']):
                        is_cursor = True
                    
                    if proc.info['name'] and 'Renderer' in proc.info['name']:
                        is_renderer = True
                    elif proc.info['cmdline'] and any('--type=renderer' in arg for arg in proc.info['cmdline']):
                        is_renderer = True
                    
                    if is_cursor and is_renderer:
                        renderer_procs.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return len(renderer_procs)
        except Exception as e:
            logger.error(f"获取Cursor窗口数量时出错: {e}")
            return 0

    def get_cursor_path(self):
        """获取Cursor安装路径"""
        if self.cursor_path:
            return self.cursor_path

        # 优先使用换号时已验证的路径
        try:
            from update_cursor_token_main import Config
            if Config.CURSOR_PATH and os.path.exists(Config.CURSOR_PATH):
                if self.system == "Windows":
                    cursor_exe = os.path.join(Config.CURSOR_PATH, "Cursor.exe")
                    if os.path.exists(cursor_exe):
                        logger.info(f"使用已保存的Cursor路径: {cursor_exe}")
                        self.cursor_path = cursor_exe
                        return cursor_exe
                elif self.system == "Darwin":  # macOS
                    if Config.CURSOR_PATH.endswith('.app'):
                        cursor_exe = os.path.join(Config.CURSOR_PATH, "Contents", "MacOS", "Cursor")
                        if os.path.exists(cursor_exe):
                            logger.info(f"使用已保存的Cursor路径: {cursor_exe}")
                            self.cursor_path = cursor_exe
                            return cursor_exe
        except Exception as e:
            logger.warning(f"使用已保存路径失败: {e}")

        # 回退到原有的查找逻辑
        try:
            if self.system == "Darwin":  # macOS
                # macOS上常见的应用程序安装位置
                default_paths = [
                    "/Applications/Cursor.app",
                    os.path.expanduser("~/Applications/Cursor.app")
                ]
                
                for path in default_paths:
                    if os.path.exists(path):
                        executable = os.path.join(path, "Contents/MacOS/Cursor")
                        if os.path.exists(executable):
                            logger.info(f"在macOS上找到Cursor路径: {executable}")
                            self.cursor_path = executable
                            return executable
                
                # 尝试从运行进程获取
                for proc in psutil.process_iter(['name', 'exe']):
                    if proc.info.get('name') and 'cursor' in proc.info['name'].lower():
                        if proc.info.get('exe') and os.path.isfile(proc.info['exe']):
                            logger.info(f"从运行进程找到路径: {proc.info['exe']}")
                            self.cursor_path = proc.info['exe']
                            return proc.info['exe']
            
            elif self.system == "Windows":  # Windows
                # 尝试默认路径
                default_path = os.path.expanduser("~\\AppData\\Local\\Programs\\cursor\\Cursor.exe")
                if os.path.exists(default_path):
                    logger.info(f"使用默认路径: {default_path}")
                    self.cursor_path = default_path
                    return default_path
                    
                # 尝试从进程获取
                for proc in psutil.process_iter(['name', 'exe']):
                    if proc.info.get('name') and proc.info.get('exe') and 'cursor' in proc.info['name'].lower():
                        if os.path.isfile(proc.info['exe']):
                            logger.info(f"从运行进程找到路径: {proc.info['exe']}")
                            self.cursor_path = proc.info['exe']
                            return proc.info['exe']
                            
                # Windows特有的注册表查找逻辑
                if platform.system() == "Windows":
                    try:
                        uninstall_key_path = "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
                        keys_to_check = [winreg.HKEY_CURRENT_USER, winreg.HKEY_LOCAL_MACHINE]
                        for hkey in keys_to_check:
                            try:
                                with winreg.OpenKey(hkey, uninstall_key_path) as key:
                                    for i in range(0, winreg.QueryInfoKey(key)[0]):
                                        subkey_name = winreg.EnumKey(key, i)
                                        try:
                                            with winreg.OpenKey(key, subkey_name) as subkey:
                                                display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                                if "cursor" in display_name.lower():
                                                    install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                                    cursor_exe = os.path.join(install_location, "Cursor.exe")
                                                    if os.path.exists(cursor_exe):
                                                        logger.info(f"从注册表找到路径: {cursor_exe}")
                                                        self.cursor_path = cursor_exe
                                                        return cursor_exe
                                        except OSError: pass
                                        except Exception as e: 
                                            logger.error(f"检查注册表子键时出错: {e}")
                            except FileNotFoundError:
                                continue  # HKLM 或 HKCU 可能不存在该路径
                            except Exception as e:
                                logger.error(f"检查注册表 {hkey} 时出错: {e}")
                    except Exception as e:
                        logger.error(f"注册表查找失败: {e}")

        except Exception as e:
            logger.error(f"获取Cursor路径时发生错误: {e}")

        logger.error("错误：未能找到 Cursor 安装路径。")
        return None

    def get_workspace_paths(self):
        """获取所有工作区路径信息"""
        workspace_info = {}
        
        # 根据系统确定存储路径
        if self.system == "Darwin":  # macOS
            storage_path = os.path.expanduser("~/Library/Application Support/Cursor/User/workspaceStorage")
        elif self.system == "Windows":  # Windows
            storage_path = os.path.expanduser("~\\AppData\\Roaming\\Cursor\\User\\workspaceStorage")
        else:
            return workspace_info
        
        if not os.path.exists(storage_path):
            logger.warning(f"工作区存储路径不存在: {storage_path}")
            return workspace_info
            
        # 获取所有工作区文件夹并按修改时间排序
        workspace_folders = []
        for d in os.listdir(storage_path):
            folder_path = os.path.join(storage_path, d)
            if os.path.isdir(folder_path):
                workspace_folders.append((folder_path, os.path.getmtime(folder_path)))
        
        # 按最后修改时间降序排序
        workspace_folders.sort(key=lambda x: x[1], reverse=True)
        
        # 遍历所有工作区文件夹
        for folder_path, mtime in workspace_folders:
            workspace_json = os.path.join(folder_path, "workspace.json")
            if os.path.exists(workspace_json):
                try:
                    with open(workspace_json, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        folder_uri = data.get('folder')
                        if folder_uri and folder_uri.startswith('file:///'):
                            # 解析路径
                            parsed_uri = urlparse(folder_uri)
                            # 根据系统处理路径格式
                            if self.system == "Windows":
                                real_path = os.path.normpath(unquote(parsed_uri.path).lstrip('/'))
                            else:  # macOS
                                real_path = os.path.normpath(unquote(parsed_uri.path))
                                
                            # 使用文件夹名作为键
                            folder_name = os.path.basename(real_path)
                            if folder_name not in workspace_info:
                                workspace_info[folder_name] = {
                                    'path': real_path,
                                    'last_modified': mtime
                                }
                except Exception as e:
                    logger.error(f"读取工作区配置出错 {workspace_json}: {e}")
                    
        return workspace_info

    def save_active_workspaces(self):
        """获取并保存当前打开的工作区信息"""
        self.active_workspaces = []
        
        # 获取工作区路径信息
        workspace_paths = self.get_workspace_paths()
        
        # Windows特有的窗口检测方法
        if self.system == "Windows":
            try:
                # 使用已经条件导入的win32gui模块
                import win32gui
                
                def extract_workspace_from_title(title):
                    """从窗口标题中提取工作区名称"""
                    parts = title.split(' - ')
                    if len(parts) >= 3 and parts[-1] == 'Cursor':
                        return parts[-2]  # 返回工作区名
                    elif len(parts) == 2 and parts[-1] == 'Cursor':
                        return parts[0]  # 如果只有两部分，返回第一部分
                    return None

                def callback(hwnd, extra):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if 'cursor' in window_text.lower() and ' - cursor' in window_text.lower():
                            try:
                                # 从窗口标题提取工作区名称
                                workspace_name = extract_workspace_from_title(window_text)
                                
                                # 获取工作区路径
                                if workspace_name and workspace_name in workspace_paths:
                                    workspace_info = workspace_paths[workspace_name]
                                    workspace_path = workspace_info['path']
                                    if workspace_path and os.path.exists(workspace_path):
                                        logger.info(f"找到工作区: {workspace_path}")
                                        self.active_workspaces.append(workspace_path)
                                
                            except Exception as e:
                                logger.error(f"处理窗口时出错: {e}")

                # 枚举所有窗口
                win32gui.EnumWindows(callback, None)
                
            except ImportError:
                logger.info("未找到win32gui模块，将使用备用方法检测工作区")
        
        # 特定于macOS的处理逻辑 - 使用Renderer进程检测窗口数量
        if self.system == "Darwin":
            logger.info("在macOS上检测活跃工作区...")
            
            # 检测当前Cursor窗口数量
            cursor_window_count = self.get_cursor_window_count()
            logger.info(f"检测到 {cursor_window_count} 个活跃的Cursor窗口")
            
            # 获取最近修改的工作区文件
            recent_workspaces = sorted(
                [(info['path'], info['last_modified']) for name, info in workspace_paths.items()],
                key=lambda x: x[1],
                reverse=True
            )
            
            # 根据检测到的窗口数量获取相应数量的工作区
            for i, (workspace_path, _) in enumerate(recent_workspaces):
                if i < cursor_window_count and os.path.exists(workspace_path):
                    logger.info(f"添加活跃工作区: {workspace_path}")
                    self.active_workspaces.append(workspace_path)
                
                if i >= cursor_window_count:
                    break
            
            # 如果没有找到足够的工作区，但检测到窗口，至少添加一个最近的工作区
            if not self.active_workspaces and cursor_window_count > 0 and recent_workspaces:
                workspace_path, _ = recent_workspaces[0]
                if os.path.exists(workspace_path):
                    logger.info(f"添加最近工作区: {workspace_path}")
                    self.active_workspaces.append(workspace_path)
        
        # 如果Windows特定方法未找到工作区且不是macOS，使用备用方法
        if not self.active_workspaces and self.system == "Windows":
            logger.info("使用最近修改的工作区信息...")
            # 按修改时间排序，选择最近的工作区
            recent_workspaces = sorted(
                [(info['path'], info['last_modified']) for name, info in workspace_paths.items()],
                key=lambda x: x[1],
                reverse=True
            )
            
            # 只取最近的一个工作区
            if recent_workspaces:
                workspace_path, _ = recent_workspaces[0]
                if os.path.exists(workspace_path):
                    logger.info(f"添加最近工作区: {workspace_path}")
                    self.active_workspaces.append(workspace_path)
        
        # 去重并保持顺序
        self.active_workspaces = list(dict.fromkeys(self.active_workspaces))
        
        if self.active_workspaces:
            logger.info(f"已保存 {len(self.active_workspaces)} 个活动工作区:")
            for workspace in self.active_workspaces:
                logger.info(f"- {workspace}")
            return True
        else:
            logger.info("未找到活动工作区")
            return False

    def kill_cursor_processes(self):
        """强制关闭所有Cursor进程"""
        killed = False
        
        # 根据系统确定进程名
        if self.system == "Darwin":  # macOS
            process_names_to_kill = ["Cursor", "cursor"]
        elif self.system == "Windows":  # Windows
            process_names_to_kill = ["cursor.exe"]
        else:
            return False
            
        for proc in psutil.process_iter(['name', 'pid', 'exe']):
            try:
                proc_name = proc.info.get('name')
                if proc_name and any(pname in proc_name.lower() for pname in process_names_to_kill):
                    logger.info(f"正在关闭Cursor进程: PID={proc.info['pid']}, 名称={proc_name}")
                    proc.kill()
                    killed = True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.warning(f"关闭进程 {proc.info.get('pid')} 时跳过: {e}")
            except Exception as e:
                logger.error(f"关闭进程时发生意外错误: {e}")
                
        return killed

    def start_cursor_with_workspaces(self):
        """启动Cursor，并打开之前保存的工作区"""
        cursor_path = self.get_cursor_path()
        if not cursor_path or not os.path.exists(cursor_path):
            logger.error("找不到Cursor可执行文件，无法启动")
            return False

        try:
            if not self.active_workspaces:
                # 如果没有保存的工作区，则普通启动
                logger.info("未提供有效文件夹，将不带参数启动 Cursor (依赖内置会话恢复)")
                
                if self.system == "Darwin":  # macOS
                    # 在macOS上使用subprocess直接启动
                    subprocess.Popen([cursor_path], 
                                    stdout=subprocess.DEVNULL,
                                    stderr=subprocess.DEVNULL,
                                    start_new_session=True)
                    return True
                else:  # Windows
                    # 使用Shell.Application COM方式启动Cursor（即使在管理员权限下也能以普通用户权限启动）
                    return start_process_shell_com(cursor_path)
            else:
                # macOS处理方式：直接为每个工作区启动一个Cursor实例
                if self.system == "Darwin":  # macOS
                    logger.info(f"在macOS上启动 {len(self.active_workspaces)} 个工作区")
                    
                    # 遍历所有工作区并启动
                    for i, folder in enumerate(self.active_workspaces):
                        if os.path.exists(folder):
                            logger.info(f"macOS启动工作区 {i+1}/{len(self.active_workspaces)}: {folder}")
                            
                            # 使用subprocess直接启动并指定工作区
                            subprocess.Popen([cursor_path, folder], 
                                            stdout=subprocess.DEVNULL,
                                            stderr=subprocess.DEVNULL,
                                            start_new_session=True)
                            
                            # 稍微延迟以避免同时启动多个实例可能的问题
                            time.sleep(1)
                    
                    logger.info(f"在macOS上打开了 {len(self.active_workspaces)} 个工作区")
                    return True
                    
                # Windows处理方式：仍然使用原来的方法
                else:  # Windows
                    # 使用第一个工作区启动主实例
                    first_workspace = self.active_workspaces[0]
                    logger.info(f"使用第一个工作区启动Cursor: {first_workspace}")
                    
                    # 使用Shell.Application COM方式启动Cursor（即使在管理员权限下也能以普通用户权限启动）
                    success = start_process_shell_com(cursor_path, [first_workspace])
                    
                    if not success:
                        logger.error("启动第一个工作区失败")
                        return False
                    
                    # 等待主实例启动
                    time.sleep(2)
                    
                    # 如果有多个工作区，打开其余的工作区
                    if len(self.active_workspaces) > 1:
                        for i, folder in enumerate(self.active_workspaces[1:], 1):
                            if os.path.exists(folder):
                                logger.info(f"启动工作区 {i+1}/{len(self.active_workspaces)}: {folder}")
                                
                                # 使用Shell.Application COM方式启动Cursor
                                start_process_shell_com(cursor_path, [folder])
                                
                                # 稍微延迟以避免同时启动多个实例可能的问题
                                time.sleep(1)
                    
                    logger.info(f"打开了 {len(self.active_workspaces)} 个工作区")
                    return True

        except Exception as e:
            logger.error(f"启动Cursor时出错: {e}")
            return False

    def restart_cursor(self):
        """重启Cursor并恢复工作区，返回是否成功"""
        logger.info(f"=== 开始在{self.system}系统上重启Cursor ===")
        
        # 1. 保存当前打开的工作区信息
        self.save_active_workspaces()
        
        # 2. 关闭所有Cursor进程
        logger.info("正在关闭所有Cursor进程...")
        if self.kill_cursor_processes():
            logger.info("等待3秒确保进程退出...")
            time.sleep(3)
        else:
            logger.info("未找到正在运行的Cursor进程，或关闭过程中出错")
        
        # 3. 重新启动Cursor并打开保存的工作区
        logger.info("正在启动Cursor并恢复工作区...")
        result = self.start_cursor_with_workspaces()
        
        if result:
            logger.info("Cursor已成功重启并尝试恢复工作区")
        else:
            logger.error("Cursor重启失败")
            
        return result

# 提供便捷的全局函数
def restart_cursor_with_workspaces():
    """提供一个简单的全局函数用于重启Cursor并恢复工作区"""
    manager = CursorRestartManager()
    return manager.restart_cursor()

def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Cursor重启管理器")
    parser.add_argument("--admin", action="store_true", 
                      help="以管理员身份运行（Windows专用）")
    parser.add_argument("--skip-prompt", action="store_true",
                      help="跳过提示，直接执行")
    
    return parser.parse_args()

if __name__ == "__main__":
    import time
    
    # 设置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    logger.info("="*50)
    logger.info("开始测试Cursor重启管理器")
    logger.info("="*50)
    
    # 解析命令行参数
    args = parse_args()
    
    # Windows系统管理员权限处理
    if platform.system() == "Windows":
        current_is_admin = is_admin()
        
        # 如果指定了--admin参数但当前不是管理员，说明提权失败
        if args.admin and not current_is_admin:
            logger.error("提权失败：请确保在UAC提示中选择\"是\"")
            input("\n提权失败，按Enter键退出...")
            sys.exit(1)
            
        # 如果没有指定--admin参数也没有--skip-prompt参数，询问用户是否需要管理员权限
        if not args.admin and not args.skip_prompt:
            if current_is_admin:
                logger.info("当前已以管理员身份运行")
            else:
                print("\n在Windows系统上，以管理员身份运行可以避免某些权限问题。")
                choice = input("是否以管理员身份重新运行程序？(y/n) [n]: ").strip().lower()
                
                if choice in ['y', 'yes', '是']:
                    # 尝试提权
                    if run_as_admin():
                        logger.info("正在以管理员身份重新启动...")
                        time.sleep(1)  # 给一点时间让新进程启动
                        sys.exit(0)  # 退出当前非管理员进程
                    else:
                        logger.warning("提权请求被拒绝，将以当前权限继续运行")
                        # 继续以非管理员身份运行
                else:
                    logger.info("将以当前权限运行")
    
    try:
        # 创建管理器实例
        manager = CursorRestartManager()
        
        # 1. 保存当前工作区信息
        logger.info("\n第1步：保存当前工作区信息")
        success = manager.save_active_workspaces()
        if success:
            logger.info(f"成功保存 {len(manager.active_workspaces)} 个工作区:")
            for ws in manager.active_workspaces:
                logger.info(f"- {ws}")
        else:
            logger.warning("未找到活动工作区")
        
        # 2. 关闭所有Cursor进程
        logger.info("\n第2步：关闭所有Cursor进程")
        if manager.kill_cursor_processes():
            logger.info("成功关闭所有Cursor进程")
            logger.info("等待3秒确保进程完全退出...")
            time.sleep(3)
        else:
            logger.info("未找到正在运行的Cursor进程")
        
        # 3. 重新启动Cursor并恢复工作区
        logger.info("\n第3步：重新启动Cursor并恢复工作区")
        if manager.start_cursor_with_workspaces():
            logger.info("成功启动Cursor并恢复工作区")
        else:
            logger.error("启动Cursor失败")
        
        logger.info("\n测试完成")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
    
    # 等待用户确认
    input("\n按Enter键退出...") 