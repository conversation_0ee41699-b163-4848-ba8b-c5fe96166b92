#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor IDE 自动更新禁用脚本
用于修改 Cursor IDE 的 settings.json 文件，禁用自动更新功能
"""

import json
import os
import shutil
from pathlib import Path
from datetime import datetime


def get_cursor_settings_path():
    """获取 Cursor settings.json 文件路径"""
    if os.name == 'nt':  # Windows
        appdata = os.environ.get('APPDATA')
        if appdata:
            return Path(appdata) / 'Cursor' / 'User' / 'settings.json'
    elif os.name == 'posix':  # macOS/Linux
        home = Path.home()
        if os.uname().sysname == 'Darwin':  # macOS
            return home / 'Library' / 'Application Support' / 'Cursor' / 'User' / 'settings.json'
        else:  # Linux
            return home / '.config' / 'Cursor' / 'User' / 'settings.json'
    
    return None


def backup_settings(settings_path):
    """备份原始设置文件"""
    if settings_path.exists():
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = settings_path.with_suffix(f'.backup_{timestamp}.json')
        shutil.copy2(settings_path, backup_path)
        print(f"✅ 已备份原始设置文件到: {backup_path}")
        return backup_path
    return None


def load_current_settings(settings_path):
    """加载当前设置"""
    if settings_path.exists():
        try:
            with open(settings_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 处理JSONC格式（带注释的JSON）
            # 移除单行注释 //
            lines = content.split('\n')
            cleaned_lines = []
            for line in lines:
                # 查找注释位置，但要避免字符串内的//
                in_string = False
                escape_next = False
                comment_pos = -1

                for i, char in enumerate(line):
                    if escape_next:
                        escape_next = False
                        continue
                    if char == '\\':
                        escape_next = True
                        continue
                    if char == '"' and not escape_next:
                        in_string = not in_string
                        continue
                    if not in_string and char == '/' and i + 1 < len(line) and line[i + 1] == '/':
                        comment_pos = i
                        break

                if comment_pos >= 0:
                    line = line[:comment_pos].rstrip()

                cleaned_lines.append(line)

            # 过滤空行
            cleaned_lines = [line for line in cleaned_lines if line.strip()]

            # 处理尾随逗号问题
            cleaned_content = '\n'.join(cleaned_lines)

            # 移除对象和数组中的尾随逗号
            import re
            # 移除对象中的尾随逗号 (,})
            cleaned_content = re.sub(r',(\s*})', r'\1', cleaned_content)
            # 移除数组中的尾随逗号 (,])
            cleaned_content = re.sub(r',(\s*])', r'\1', cleaned_content)

            return json.loads(cleaned_content)

        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            print(f"⚠️  读取设置文件时出错: {e}")
            print(f"⚠️  这可能是因为JSON格式错误或包含不支持的注释格式")
            return {}
    return {}


def disable_auto_update_settings():
    """返回禁用自动更新的设置"""
    return {
        "update.enableWindowsBackgroundUpdates": False,
        "update.mode": "manual",
        "update.showReleaseNotes": False
    }


def update_settings(current_settings, new_settings):
    """更新设置"""
    updated_settings = current_settings.copy()
    updated_settings.update(new_settings)
    return updated_settings


def save_settings(settings_path, settings):
    """保存设置到文件"""
    # 确保目录存在
    settings_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        print(f"✅ 设置已保存到: {settings_path}")
        return True
    except Exception as e:
        print(f"❌ 保存设置时出错: {e}")
        return False


def display_changes(old_settings, new_settings, update_keys):
    """显示将要进行的更改"""
    print("\n📋 将要进行的更改:")
    print("-" * 50)
    
    for key in update_keys:
        old_value = old_settings.get(key, "未设置")
        new_value = new_settings.get(key)
        print(f"  {key}:")
        print(f"    旧值: {old_value}")
        print(f"    新值: {new_value}")
    print("-" * 50)


def main():
    """主函数"""
    print("🚀 Cursor IDE 自动更新禁用工具")
    print("=" * 50)
    
    # 获取设置文件路径
    settings_path = get_cursor_settings_path()
    if not settings_path:
        print("❌ 无法确定 Cursor 设置文件路径")
        return False
    
    print(f"📁 设置文件路径: {settings_path}")
    
    # 加载当前设置
    current_settings = load_current_settings(settings_path)
    print(f"📖 当前设置已加载 ({len(current_settings)} 项配置)")
    
    # 获取禁用自动更新的设置
    disable_settings = disable_auto_update_settings()
    
    # 显示将要进行的更改
    display_changes(current_settings, disable_settings, disable_settings.keys())
    
    # 询问用户确认
    confirm = input("\n❓ 是否继续执行更改? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 操作已取消")
        return False
    
    # 备份原始设置
    backup_path = backup_settings(settings_path)
    
    # 更新设置
    updated_settings = update_settings(current_settings, disable_settings)
    
    # 保存设置
    if save_settings(settings_path, updated_settings):
        print("\n✅ Cursor 自动更新已成功禁用!")
        print("\n📝 修改的设置项:")
        for key, value in disable_settings.items():
            print(f"  • {key}: {value}")
        
        if backup_path:
            print(f"\n💾 如需恢复，请将备份文件 {backup_path.name} 重命名为 settings.json")
        
        print("\n⚠️  注意: 需要重启 Cursor IDE 使设置生效")
        return True
    else:
        print("❌ 设置保存失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 操作完成!")
        else:
            print("\n💥 操作失败!")
    except KeyboardInterrupt:
        print("\n\n⏹️  操作被用户中断")
    except Exception as e:
        print(f"\n💥 发生未预期的错误: {e}")
