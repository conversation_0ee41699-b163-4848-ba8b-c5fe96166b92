#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor设置界面邮箱显示修改脚本
功能：只修改设置界面中显示的邮箱，将icloud替换为gmail，不影响数据层
例如：<EMAIL> -> <EMAIL>
"""

import json
import os
import platform
import psutil
import re
import shutil
import time
from pathlib import Path
from typing import Optional, Union


class Colors:
    """颜色常量"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    NC = '\033[0m'  # No Color


class Config:
    """配置类"""
    APP_NAME = "Cursor"
    PROCESS_TIMEOUT = 30
    CURSOR_PROCESS_NAMES = [APP_NAME + ".exe", APP_NAME]
    WINDOWS_APP_NAME_MAP = {"Cursor": "cursor", "Cursor Nightly": "cursor-nightly"}
    CURRENT_WINDOWS_APP_NAME_KEY = WINDOWS_APP_NAME_MAP[APP_NAME]


class FilePathManager:
    """文件路径管理器"""
    
    _OS_PATHS = {
        "Windows": {
            "app": lambda: next(
                (
                    path
                    for path in [
                        Path(os.getenv("LOCALAPPDATA", ""))
                        / "Programs"
                        / Config.CURRENT_WINDOWS_APP_NAME_KEY
                        / "resources",
                        Path("C:/Program Files")
                        / Config.CURRENT_WINDOWS_APP_NAME_KEY
                        / "resources",
                    ]
                    if path.exists()
                ),
                None,
            ),
        },
        "Darwin": {
            "app": lambda: Path(
                f"/Applications/{Config.APP_NAME}.app/Contents/Resources"
            ),
        },
        "Linux": {
            "app": lambda: None,  # Linux暂不支持获取应用路径
        },
    }

    @staticmethod
    def _get_path_by_key(key: str) -> Optional[Path]:
        """根据键获取对应的路径"""
        system = platform.system()
        if system not in FilePathManager._OS_PATHS:
            raise OSError(f"不支持的操作系统: {system}")

        path_func = FilePathManager._OS_PATHS[system].get(key)
        path = path_func() if path_func else None

        if path is None:
            raise OSError(f"无法找到有效的应用路径，请确认Cursor是否正确安装")

        return path

    @staticmethod
    def get_app_base_path() -> Path:
        """获取应用基础路径"""
        base_path = FilePathManager._get_path_by_key("app")
        if not base_path:
            raise OSError(f"无法获取应用路径，不支持的操作系统: {platform.system()}")
        return base_path

    @staticmethod
    def get_product_json_path() -> Path:
        """获取product.json文件路径"""
        return FilePathManager.get_app_base_path() / "app" / "product.json"

    @staticmethod
    def get_workbench_js_path() -> Path:
        """获取workbench.desktop.main.js文件路径"""
        return (
            FilePathManager.get_app_base_path()
            / "app"
            / "out"
            / "vs"
            / "workbench"
            / "workbench.desktop.main.js"
        )


class FilePermissionManager:
    """文件权限管理器"""

    @staticmethod
    def make_file_writable(file_path: Union[str, Path]) -> bool:
        """修改文件权限为可写"""
        try:
            file_path = Path(file_path)
            if platform.system() == "Windows":
                os.system(f'attrib -R "{file_path}"')
            else:
                os.chmod(file_path, 0o666)
            return True
        except Exception as e:
            print(f"{Colors.RED}修改文件权限失败: {e}{Colors.NC}")
            return False

    @staticmethod
    def make_file_readonly(file_path: Union[str, Path]) -> bool:
        """修改文件权限为只读"""
        try:
            file_path = Path(file_path)
            if platform.system() == "Windows":
                os.system(f'attrib +R "{file_path}"')
            else:
                os.chmod(file_path, 0o444)
            return True
        except Exception as e:
            print(f"{Colors.RED}修改文件权限失败: {e}{Colors.NC}")
            return False


class CursorProcessManager:
    """Cursor进程管理器"""

    @staticmethod
    def exit_cursor() -> bool:
        """安全退出Cursor进程"""
        print(f"{Colors.YELLOW}开始退出 {Config.APP_NAME}...{Colors.NC}")

        cursor_processes = [
            proc
            for proc in psutil.process_iter(["pid", "name"])
            if any(
                name.lower() == proc.info["name"].lower()
                for name in Config.CURSOR_PROCESS_NAMES
            )
        ]

        if not cursor_processes:
            print(f"{Colors.GREEN}未发现需要关闭的 {Config.APP_NAME} 主进程{Colors.NC}")
            return True

        for proc in cursor_processes:
            try:
                if proc.is_running():
                    print(
                        f"{Colors.YELLOW}正在关闭进程: {proc.info['name']} (PID: {proc.pid}){Colors.NC}"
                    )
                    proc.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        start_time = time.time()
        while time.time() - start_time < Config.PROCESS_TIMEOUT:
            still_running = [p for p in cursor_processes if p.is_running()]
            if not still_running:
                print(
                    f"{Colors.GREEN}所有 {Config.APP_NAME} 主进程已正常关闭{Colors.NC}"
                )
                return True
            time.sleep(0.5)

        still_running = [p for p in cursor_processes if p.is_running()]
        if still_running:
            process_list = ", ".join(
                f"{p.info['name']} (PID: {p.pid})" for p in still_running
            )
            print(
                f"{Colors.RED}以下进程未能在规定时间内关闭: {process_list}{Colors.NC}"
            )
            return False

        return True


def backup_file(file_path):
    """创建文件备份"""
    backup_path = file_path.with_suffix(file_path.suffix + ".bak")
    
    if backup_path.exists():
        print(f"{Colors.YELLOW}备份文件 {backup_path} 已存在，跳过备份{Colors.NC}")
        return True
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"{Colors.GREEN}已创建备份文件 {backup_path}{Colors.NC}")
        return True
    except Exception as e:
        print(f"{Colors.RED}创建备份失败: {e}{Colors.NC}")
        return False


def update_product_json() -> bool:
    """删除product.json中的checksums部分以绕过完整性校验"""
    try:
        product_path = FilePathManager.get_product_json_path()
        print(f"{Colors.YELLOW}正在处理 {product_path}...{Colors.NC}")

        if not product_path.exists():
            print(f"{Colors.RED}错误: 文件 {product_path} 不存在{Colors.NC}")
            return False

        if not backup_file(product_path):
            return False

        with open(product_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        if "checksums" in data:
            del data["checksums"]
            print(f"{Colors.GREEN}已删除checksums字段{Colors.NC}")

            FilePermissionManager.make_file_writable(product_path)
            with open(product_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
            FilePermissionManager.make_file_readonly(product_path)

            print(f"{Colors.GREEN}已成功更新 {product_path}{Colors.NC}")
            return True
        else:
            print(f"{Colors.YELLOW}checksums字段不存在，无需修改{Colors.NC}")
            return True

    except Exception as e:
        print(f"{Colors.RED}处理 product.json 时出错: {e}{Colors.NC}")
        return False


def modify_settings_email_display():
    """修改设置界面邮箱显示逻辑（仅UI层）"""
    try:
        js_path = FilePathManager.get_workbench_js_path()
        
        print(f"{Colors.YELLOW}正在处理 {js_path}...{Colors.NC}")
        
        if not js_path.exists():
            print(f"{Colors.RED}错误: 文件 {js_path} 不存在{Colors.NC}")
            return False
        
        if not backup_file(js_path):
            return False
        
        with open(js_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否已经修改过
        if "email:v.replace(/icloud/g," in content or "email:g.replace(/icloud/g," in content:
            print(f"{Colors.YELLOW}检测到已有的邮箱替换逻辑，无需重复修改{Colors.NC}")
            return True
        
        modified = False
        
        # Windows版本的模式匹配
        print(f"{Colors.BLUE}尝试Windows版本模式匹配...{Colors.NC}")
        
        # Windows方法1：在getEmailAndSignUpType调用后立即对邮箱进行UI层替换
        pattern1 = r'(\{email:v,signUpType:C\}\)=>\{v&&f\()v(\),C&&m\(C\)\})'
        replacement1 = r'\1v.replace(/icloud/g,"gmail")\2'
        
        if re.search(pattern1, content):
            content = re.sub(pattern1, replacement1, content)
            print(f"{Colors.GREEN}已修改设置界面邮箱显示逻辑（Windows方法1）{Colors.NC}")
            modified = True
        
        # Windows方法2：直接查找并替换v&&f(v)模式
        pattern2 = r'v&&f\(v\)'
        replacement2 = r'v&&f(v.replace(/icloud/g,"gmail"))'
        
        if re.search(pattern2, content):
            content = re.sub(pattern2, replacement2, content)
            print(f"{Colors.GREEN}已修改设置界面邮箱显示逻辑（Windows方法2）{Colors.NC}")
            modified = True
        
        # Windows方法3：查找更具体的getEmailAndSignUpType处理逻辑
        pattern3 = r'getEmailAndSignUpType\(\)\.then\(\(\{email:v,signUpType:C\}\)=>\{v&&f\(v\)'
        replacement3 = r'getEmailAndSignUpType().then(({email:v,signUpType:C})=>{v&&f(v.replace(/icloud/g,"gmail"))'
        
        if re.search(pattern3, content):
            content = re.sub(pattern3, replacement3, content)
            print(f"{Colors.GREEN}已修改设置界面邮箱显示逻辑（Windows方法3）{Colors.NC}")
            modified = True
        
        # Mac版本的模式匹配
        if not modified:
            print(f"{Colors.BLUE}尝试Mac版本模式匹配...{Colors.NC}")
            
            # Mac方法1：直接查找并替换g&&u(g)模式
            pattern_mac1 = r'g&&u\(g\)'
            replacement_mac1 = r'g&&u(g.replace(/icloud/g,"gmail"))'
            
            if re.search(pattern_mac1, content):
                content = re.sub(pattern_mac1, replacement_mac1, content)
                print(f"{Colors.GREEN}已修改设置界面邮箱显示逻辑（Mac方法1）{Colors.NC}")
                modified = True
            
            # Mac方法2：查找更具体的getEmailAndSignUpType处理逻辑
            pattern_mac2 = r'getEmailAndSignUpType\(\)\.then\(\(\{email:g,signUpType:p\}\)=>\{g&&u\(g\)'
            replacement_mac2 = r'getEmailAndSignUpType().then(({email:g,signUpType:p})=>{g&&u(g.replace(/icloud/g,"gmail"))'
            
            if re.search(pattern_mac2, content):
                content = re.sub(pattern_mac2, replacement_mac2, content)
                print(f"{Colors.GREEN}已修改设置界面邮箱显示逻辑（Mac方法2）{Colors.NC}")
                modified = True
            
            # Mac方法3：更宽泛的匹配模式
            pattern_mac3 = r'(\{email:g,signUpType:p\}\)=>\{g&&u\()g(\),p&&f\(p\)\})'
            replacement_mac3 = r'\1g.replace(/icloud/g,"gmail")\2'
            
            if re.search(pattern_mac3, content):
                content = re.sub(pattern_mac3, replacement_mac3, content)
                print(f"{Colors.GREEN}已修改设置界面邮箱显示逻辑（Mac方法3）{Colors.NC}")
                modified = True
        
        # 通用模式：查找v&&u(v)或其他变量名组合
        if not modified:
            print(f"{Colors.BLUE}尝试通用模式匹配...{Colors.NC}")
            
            # 通用模式1：匹配 变量&&函数(变量) 的形式
            pattern_generic1 = r'(\w)&&(\w)\(\1\)'
            matches = re.findall(pattern_generic1, content)
            
            for var, func in matches:
                # 查找包含email的上下文
                context_pattern = f'email:{var}.*?{var}&&{func}\\({var}\\)'
                if re.search(context_pattern, content):
                    replacement_generic = f'{var}&&{func}({var}.replace(/icloud/g,"gmail"))'
                    content = re.sub(f'{var}&&{func}\\({var}\\)', replacement_generic, content)
                    print(f"{Colors.GREEN}已修改设置界面邮箱显示逻辑（通用模式：{var}&&{func}({var})）{Colors.NC}")
                    modified = True
                    break
        
        if not modified:
            print(f"{Colors.YELLOW}未找到需要修改的设置界面邮箱显示代码{Colors.NC}")
            print(f"{Colors.BLUE}当前系统: {platform.system()}{Colors.NC}")
            
            # 提供诊断信息
            print(f"{Colors.BLUE}诊断信息：{Colors.NC}")
            if re.search(r'getEmailAndSignUpType', content):
                print(f"  ✓ 找到getEmailAndSignUpType函数")
            else:
                print(f"  ✗ 未找到getEmailAndSignUpType函数")
                
            if re.search(r'email:', content):
                print(f"  ✓ 找到email字段")
                # 显示email字段的上下文
                email_matches = re.finditer(r'email:(\w+)', content)
                email_vars = set()
                for match in email_matches:
                    email_vars.add(match.group(1))
                print(f"  email变量名: {', '.join(email_vars)}")
            else:
                print(f"  ✗ 未找到email字段")
            
            return False
        
        FilePermissionManager.make_file_writable(js_path)
        with open(js_path, "w", encoding="utf-8") as f:
            f.write(content)
        FilePermissionManager.make_file_readonly(js_path)
        
        print(f"{Colors.GREEN}已成功修改设置界面邮箱显示逻辑：icloud -> gmail{Colors.NC}")
        print(f"{Colors.GREEN}示例：<EMAIL> -> <EMAIL>{Colors.NC}")
        print(f"{Colors.BLUE}注意：此修改仅影响设置界面显示，不影响实际邮箱数据{Colors.NC}")
        return True
        
    except Exception as e:
        print(f"{Colors.RED}修改过程中发生错误: {e}{Colors.NC}")
        return False


def restore_backup():
    """恢复备份文件"""
    try:
        js_path = FilePathManager.get_workbench_js_path()
        product_path = FilePathManager.get_product_json_path()
        
        restored = False
        
        js_backup_path = js_path.with_suffix(js_path.suffix + ".bak")
        if js_backup_path.exists():
            FilePermissionManager.make_file_writable(js_path)
            shutil.copy2(js_backup_path, js_path)
            FilePermissionManager.make_file_readonly(js_path)
            print(f"{Colors.GREEN}已恢复 {js_path}{Colors.NC}")
            restored = True
        
        product_backup_path = product_path.with_suffix(product_path.suffix + ".bak")
        if product_backup_path.exists():
            FilePermissionManager.make_file_writable(product_path)
            shutil.copy2(product_backup_path, product_path)
            FilePermissionManager.make_file_readonly(product_path)
            print(f"{Colors.GREEN}已恢复 {product_path}{Colors.NC}")
            restored = True
        
        if restored:
            print(f"{Colors.GREEN}恢复完成！{Colors.NC}")
            return True
        else:
            print(f"{Colors.RED}未找到备份文件{Colors.NC}")
            return False
            
    except Exception as e:
        print(f"{Colors.RED}恢复备份失败: {e}{Colors.NC}")
        return False


def get_user_confirmation(message: str, default: bool = False) -> bool:
    """获取用户确认"""
    print(f"{Colors.YELLOW}{message} (y/n){Colors.NC}")
    response = input().strip().lower()
    if not response:
        return default
    return response == "y"


def main():
    """主函数"""
    print(f"{Colors.BLUE}=== Cursor设置界面邮箱显示修改工具 ==={Colors.NC}")
    print(f"{Colors.BLUE}功能：仅修改设置界面中的邮箱显示（icloud -> gmail）{Colors.NC}")
    print(f"{Colors.BLUE}范围：只影响UI显示层，不影响实际邮箱数据{Colors.NC}")
    print()
    
    while True:
        print("请选择操作：")
        print("1. 修改设置界面邮箱显示 (icloud -> gmail)")
        print("2. 恢复备份文件")
        print("3. 退出")
        
        choice = input("请输入选项 (1-3): ").strip()
        
        if choice == "1":
            try:
                system = platform.system()
                if system not in ["Windows", "Darwin", "Linux"]:
                    print(f"{Colors.RED}不支持的操作系统: {system}{Colors.NC}")
                    break

                if not get_user_confirmation(f"即将退出 {Config.APP_NAME} 并修改设置界面邮箱显示，请确保所有工作已保存。是否继续？"):
                    print("操作已取消")
                    continue

                if not CursorProcessManager.exit_cursor():
                    print(f"{Colors.RED}无法关闭 {Config.APP_NAME} 进程，请手动关闭后重试{Colors.NC}")
                    continue

                product_result = update_product_json()
                email_result = modify_settings_email_display()

                if product_result and email_result:
                    print(f"{Colors.GREEN}所有修改完成！重启Cursor后在设置界面查看效果。{Colors.NC}")
                else:
                    print(f"{Colors.YELLOW}修改过程中出现错误，请检查上面的日志{Colors.NC}")
                    
            except Exception as e:
                print(f"{Colors.RED}执行过程中出现错误: {e}{Colors.NC}")
            break
            
        elif choice == "2":
            if restore_backup():
                print(f"{Colors.GREEN}恢复完成！重启Cursor后生效。{Colors.NC}")
            break
            
        elif choice == "3":
            print("退出程序")
            break
            
        else:
            print(f"{Colors.RED}无效选项，请重新输入{Colors.NC}")


if __name__ == "__main__":
    main() 