import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import requests
import asyncio
import threading
import aiohttp
import jwt
import ssl
from account_info_checker import Account<PERSON><PERSON><PERSON>
from update_cursor_token_main import TokenManager, CursorManager, CursorAuthManager, FilePathManager, CursorPatcher, Utils, Config
from tongji import CursorUsageTracker
import json
from pathlib import Path
import logging
import platform
import os
import sys
import traceback
from datetime import datetime, timezone, timedelta
import urllib3
import time
from cryptography.fernet import Fernet
import base64
import hashlib
import subprocess
import ctypes  # 添加ctypes库用于检查和请求管理员权限
import colorsys
import math
# 导入Cursor重启管理器
from cursor_restart_manager import CursorRestartManager

# 判断是否为开发环境
def is_development_mode():
    """
    检测当前是否处于开发模式（源代码运行）而非打包后的环境
    
    Returns:
        bool: 如果是开发模式返回True，否则返回False
    """
    # PyInstaller打包后，__file__变量会指向临时目录中的提取文件
    # 而sys.frozen属性会被设置
    return not getattr(sys, 'frozen', False)

# 全局日志显示控制
ENABLE_CONSOLE_LOGS = is_development_mode()  # 只在开发模式下启用控制台日志

# 配置日志
desktop_path = os.path.join(str(Path.home()), "Desktop")
log_file_path = os.path.join(desktop_path, "cursor_pro_app_log.txt")

# 临时禁用桌面日志文件输出
'''
# 配置日志处理器
file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s")
file_handler.setFormatter(file_formatter)
'''

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
console_handler.setFormatter(console_formatter)

# 配置根日志记录器
logger = logging.getLogger()  # 获取根日志记录器
logger.setLevel(logging.DEBUG)
# logger.addHandler(file_handler)  # 临时禁用文件日志
if ENABLE_CONSOLE_LOGS:  # 仅在开发模式下添加控制台日志处理器
    logger.addHandler(console_handler)

# 输出初始日志，标记程序开始
logger.info("="*50)
logger.info("应用程序启动")
logger.info(f"操作系统: {platform.system()} {platform.release()}")
logger.info(f"Python版本: {platform.python_version()}")
logger.info("桌面日志文件输出已禁用")
logger.info("="*50)

def utc_to_china_timezone(utc_datetime):
    """
    将UTC时间转换为中国时区（UTC+8）时间
    
    Args:
        utc_datetime: UTC时区的datetime对象
        
    Returns:
        中国时区的datetime对象
    """
    china_timezone = timezone(timedelta(hours=8))
    # 如果输入的时间没有时区信息，假定它是UTC时间
    if utc_datetime.tzinfo is None:
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
    # 转换到中国时区
    return utc_datetime.astimezone(china_timezone)

class EncryptedFileHandler(logging.FileHandler):
    """加密日志处理器"""
    def __init__(self, filename, mode='a', encoding='utf-8', delay=False, key=None):
        """
        初始化加密日志处理器
        :param filename: 日志文件路径
        :param mode: 文件打开模式
        :param encoding: 文件编码
        :param delay: 是否延迟打开文件
        :param key: 加密密钥
        """
        super().__init__(filename, mode, encoding, delay)
        if key is None:
            # 生成一个固定的加密密钥
            salt = b"CursorProfessionalLog"  # 固定的盐值
            key_material = "CursorProEncryptionKey2024"  # 固定的密钥材料
            key = base64.urlsafe_b64encode(hashlib.pbkdf2_hmac(
                'sha256', 
                key_material.encode(), 
                salt, 
                100000  # 迭代次数
            ))
        self.fernet = Fernet(key)
        
        # 在日志文件开头写入加密标记
        if not os.path.exists(filename) or os.path.getsize(filename) == 0:
            with open(filename, 'wb') as f:
                f.write(b'ENCRYPTED_LOG_V1\n')

    def emit(self, record):
        """
        发出日志记录
        :param record: 日志记录
        """
        try:
            msg = self.format(record)
            # 添加时间戳和级别信息到消息中
            encrypted_msg = self.fernet.encrypt(msg.encode('utf-8'))
            # 将加密后的消息转换为base64并添加换行符
            encoded_msg = base64.b64encode(encrypted_msg).decode('utf-8') + '\n'
            self.stream.write(encoded_msg)
            self.flush()
        except Exception as e:
            self.handleError(record)

# 配置详细的日志记录
def setup_logging():
    """配置日志系统"""
    # 创建日志目录
    log_dir = os.path.join(os.path.expanduser("~"), "CursorProfessionalLogs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志文件名（包含日期和时间）
    debug_log_file = os.path.join(log_dir, f"cursor_professional_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.elog")
    info_log_file = os.path.join(log_dir, f"cursor_professional_info_{datetime.now().strftime('%Y%m%d_%H%M%S')}.elog")
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - [%(levelname)s] - %(name)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # DEBUG级别的加密日志处理器
    debug_handler = EncryptedFileHandler(debug_log_file, encoding='utf-8')
    debug_handler.setLevel(logging.DEBUG)
    debug_handler.setFormatter(formatter)
    
    # INFO及以上级别的加密日志处理器
    info_handler = EncryptedFileHandler(info_log_file, encoding='utf-8')
    info_handler.setLevel(logging.INFO)
    info_handler.setFormatter(formatter)
    
    # 控制台处理器（只记录INFO及以上级别的日志）
    if ENABLE_CONSOLE_LOGS:  # 仅在开发模式下添加控制台日志处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 添加处理器到根日志记录器
    root_logger.addHandler(debug_handler)
    root_logger.addHandler(info_handler)
    
    return info_log_file, debug_log_file

# 初始化日志系统
info_log_file, debug_log_file = setup_logging()
logger = logging.getLogger(__name__)

# 记录启动信息
logger.info("="*50)
logger.info("应用程序启动")
logger.info("="*50)
logger.info(f"操作系统: {platform.system()} {platform.release()}")
logger.info(f"Python版本: {sys.version}")
logger.info(f"工作目录: {os.getcwd()}")
logger.info(f"INFO日志文件: {info_log_file}")
logger.info(f"DEBUG日志文件: {debug_log_file}")
logger.info(f"CPU架构: {platform.machine()}")
logger.info(f"处理器: {platform.processor()}")
logger.info("-"*50)

# 全局异常处理
def global_exception_handler(exc_type, exc_value, exc_traceback):
    """全局异常处理器"""
    logger.critical("="*50)
    logger.critical("发生未捕获的异常")
    logger.critical("-"*50)
    logger.critical(f"异常类型: {exc_type.__name__}")
    logger.critical(f"异常信息: {str(exc_value)}")
    logger.critical("异常追踪:")
    for line in traceback.format_tb(exc_traceback):
        logger.critical(line.strip())
    logger.critical("="*50)
    
    # 在主线程中显示错误对话框
    try:
        if hasattr(sys, '_root'):
            root = sys._root()
            if root:
                logger.info("尝试在GUI中显示错误对话框")
                root.after(0, lambda: messagebox.showerror("错误", 
                    f"发生未预期的错误:\n{str(exc_value)}\n\n详细信息已记录到日志文件:\n{info_log_file}"))
                logger.info("错误对话框显示成功")
    except Exception as e:
        logger.error(f"显示错误对话框时出现异常: {str(e)}")
        logger.error(traceback.format_exc())

# 设置全局异常处理器
sys.excepthook = global_exception_handler

async def get_email_from_token(token):
    """
    使用token获取真实邮箱地址
    
    Args:
        token: 用户token
        
    Returns:
        str: 邮箱地址，如果获取失败返回None
    """
    try:
        # 解码token获取用户ID
        decoded = jwt.decode(token, options={"verify_signature": False})
        user_id = decoded['sub'].split('|')[1]
        
        # 格式化cookie
        test_cookie = f"{user_id}%3A%3A{token}"
        
        headers = {
            "Cookie": f"WorkosCursorSessionToken={test_cookie}",
            "Authorization": f"Bearer {token}"
        }
        
        # 创建SSL上下文
        ssl_context = None
        if platform.system() == "Darwin":  # macOS
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
        
        async with aiohttp.ClientSession() as session:
            url = "https://www.cursor.com/api/auth/me"
            async with session.get(url, headers=headers, ssl=ssl_context) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('email')
                else:
                    logger.error(f"获取邮箱失败，状态码: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"获取邮箱时发生错误: {e}")
        return None

class SidePanel(ttk.Frame):
    """可收缩的侧边栏面板"""
    def __init__(self, parent, width=500, app=None):  # 添加app参数
        super().__init__(parent)
        self.parent = parent
        self.width = width
        self.expanded = False
        self.original_window_width = None
        self.app = app  # 保存对主应用的引用
        
        # 设置面板样式和背景色
        self.configure(style="SidePanel.TFrame")
        
        # 修改收缩按钮位置 - 放在API验证区域上方
        self.toggle_button = ctk.CTkButton(
            parent,
            text="切换历史账号",
            width=85,
            height=28,  # 稍微增加高度
            fg_color=("#2b7aaa", "#2b7aaa"),
            hover_color=("#3A3A3A", "#444444"),  # 悬停时使用更亮的渐变效果
            text_color="#E5E5E5",  # 使用更亮的文字颜色
            font=("Microsoft YaHei UI", 12, "bold"),  # 加粗文字
            corner_radius=4,  # 增加圆角
            border_width=1,  # 添加边框
            border_color=("#444444", "#111111"),  # 上边缘亮色，下边缘暗色，创造渐变感
            command=self.toggle_panel
        )
        # 将按钮放在API验证区域右上方
        self.toggle_button.place(relx=1.0, y=140, anchor="ne", x=-27)  # 调整y值和x值以对齐API验证区域
        
        # 创建内容框架 - 使用与背景完全匹配的颜色和无圆角
        self.content_frame = ctk.CTkFrame(
            self, 
            fg_color="#111111",
            corner_radius=0,
            border_width=0
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
        
        # 创建内部内容容器 - 带有内边距
        self.inner_content = ctk.CTkFrame(
            self.content_frame,
            fg_color="#111111",
            corner_radius=0,
            border_width=0
        )
        self.inner_content.pack(fill=tk.BOTH, expand=True, padx=7, pady=7)
        
        # 在内容框架顶部添加收缩按钮
        self.close_button = ctk.CTkButton(
            self.inner_content,
            text="≪ 收起侧边栏",
            fg_color="#1A1A1A",
            hover_color="#2A2A2A",
            text_color="#CCCCCC",
            height=30,
            width=450,
            corner_radius=6,
            font=("Microsoft YaHei UI", 12),
            command=self.toggle_panel
        )
        self.close_button.pack(pady=(5, 15))
        
        # 创建标题和控制按钮的容器
        title_frame = ctk.CTkFrame(self.inner_content, fg_color="transparent")
        title_frame.pack(fill="x", pady=(5, 10))

        # 添加最近账号标题
        self.recent_accounts_title = ctk.CTkLabel(
            title_frame,
            text="只显示最近7天内添加的5个账号",
            text_color="#FFFFFF",
            font=("Microsoft YaHei UI", 14, "bold")
        )
        self.recent_accounts_title.pack(side="left", padx=(0, 10))

        # 添加检查所有账号状态按钮 - 更显眼的设计
        self.check_status_button = ctk.CTkButton(
            title_frame,
            text="检查所有账号状态",
            width=120,
            height=32,
            fg_color="#4CAF50",  # 绿色背景，更显眼
            hover_color="#45A049",  # 深绿色悬停效果
            text_color="#FFFFFF",  # 白色文字
            font=("Microsoft YaHei UI", 11, "bold"),  # 加粗字体
            corner_radius=6,
            command=self.start_delayed_status_check
        )
        self.check_status_button.pack(side="right", padx=(5, 0))
        
        # 添加最近账号列表容器（使用可滚动框架）
        self.recent_accounts_frame = ctk.CTkScrollableFrame(
            self.inner_content,
            fg_color="#1A1A1A",
            corner_radius=8,
            border_width=0,
            scrollbar_button_color="#444444",
            scrollbar_button_hover_color="#666666"
        )
        self.recent_accounts_frame.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)
        
        # 存储最近账号按钮的列表
        self.recent_account_buttons = []
        
        # 存储账号状态标签的字典 {token: status_label}
        self.account_status_labels = {}
        
        # 存储正在刷新状态的token集合
        self.refreshing_status = set()
        
        # 加载最近账号数据
        self.load_recent_accounts()
        
        # 初始状态：收起在右侧，使用无圆角设置
        # 不使用relheight=1，避免超出窗口客户区域
        self.place_configure(relx=1.0, y=0, width=self.width, height=0)

    def load_recent_accounts(self):
        """加载最近使用的账号列表"""
        logger.info("="*30)
        logger.info("开始加载侧边栏账号列表")
        try:
            # 清除现有的账号按钮
            for btn in self.recent_account_buttons:
                btn.destroy()
            self.recent_account_buttons = []
            
            # 清除账号状态标签字典
            self.account_status_labels.clear()
            self.refreshing_status.clear()

            # 检查app引用和当前API Key
            if not self.app or not self.app.api_key:
                # 如果没有API Key，显示提示信息
                logger.info("未验证API Key，显示提示信息")
                no_account_label = ctk.CTkLabel(
                    self.recent_accounts_frame,
                    text="请先验证API密钥",
                    text_color="#999999",
                    font=("Microsoft YaHei UI", 12)
                )
                no_account_label.pack(pady=15)
                self.recent_account_buttons.append(no_account_label)
                return

            # 创建详细的令牌读取状态信息
            import os
            from pathlib import Path
            import platform
            
            # 获取操作系统信息
            system = platform.system()
            logger.info(f"当前操作系统: {system}")
            
            # 获取可能的令牌文件路径
            if system == "Darwin":  # macOS
                home_dir = str(Path.home())
                tokens_path = os.path.join(home_dir, "Library", "Application Support", "CursorPro", "tokens.enc")
                alt_tokens_path = "/Library/Application Support/CursorPro/tokens.enc"
                # 添加更多可能的路径
                other_paths = [
                    os.path.join("/Users", os.getenv('USER', 'a1234'), "Library", "Application Support", "CursorPro", "tokens.enc"),
                    os.path.join(os.getcwd(), "tokens.enc")
                ]
            elif system == "Windows":
                tokens_path = os.path.join(os.getenv('LOCALAPPDATA'), "CursorPro", "tokens.enc")
                alt_tokens_path = None
                other_paths = []
            else:  # Linux
                home_dir = str(Path.home())
                tokens_path = os.path.join(home_dir, ".config", "CursorPro", "tokens.enc")
                alt_tokens_path = None
                other_paths = []
            
            # 收集状态信息并记录日志
            logger.info(f"当前用户: {os.getenv('USER')}")
            logger.info(f"Home路径: {Path.home()}")
            logger.info(f"工作目录: {os.getcwd()}")
            logger.info(f"主令牌路径: {tokens_path}")
            logger.info(f"主令牌文件存在: {os.path.exists(tokens_path)}")
            
            status_info = [
                f"操作系统: {system}",
                f"当前用户: {os.getenv('USER')}",
                f"Home路径: {Path.home()}",
                f"工作目录: {os.getcwd()}",
                f"主令牌路径: {tokens_path}",
                f"主令牌文件存在: {os.path.exists(tokens_path)}"
            ]
            
            if alt_tokens_path:
                logger.info(f"备用令牌路径: {alt_tokens_path}")
                logger.info(f"备用令牌文件存在: {os.path.exists(alt_tokens_path)}")
                status_info.append(f"备用令牌路径: {alt_tokens_path}")
                status_info.append(f"备用令牌文件存在: {os.path.exists(alt_tokens_path)}")
            
            # 检查其他可能的路径
            for path in other_paths:
                logger.info(f"其他路径: {path}")
                logger.info(f"文件存在: {os.path.exists(path)}")
                status_info.append(f"其他路径: {path}")
                status_info.append(f"文件存在: {os.path.exists(path)}")
            
            # 尝试手动打开文件
            file_can_open = False
            file_size = 0
            file_content_start = ""
            try:
                if os.path.exists(tokens_path):
                    with open(tokens_path, 'rb') as f:
                        content = f.read(20)  # 只读取前20字节
                        file_size = os.path.getsize(tokens_path)
                        file_can_open = True
                        file_content_start = " ".join([f"{b:02x}" for b in content])
                    logger.info(f"成功手动打开文件，大小: {file_size}字节")
                    logger.info(f"文件开头内容: {file_content_start}")
            except Exception as e:
                logger.error(f"手动打开文件失败: {str(e)}")
                status_info.append(f"手动打开文件失败: {str(e)}")
            
            status_info.append(f"文件可以打开: {file_can_open}")
            if file_can_open:
                status_info.append(f"文件大小: {file_size} 字节")
                status_info.append(f"文件开头内容: {file_content_start}")
            
            # 导入TokenStorage
            logger.info("尝试导入TokenStorage并读取令牌")
            from update_cursor_token_main import TokenStorage
            
            # 获取保存的token列表
            try:
                stored_tokens = TokenStorage.list_tokens()
                logger.info(f"成功读取令牌数: {len(stored_tokens)}")
                status_info.append(f"成功读取令牌数: {len(stored_tokens)}")
            except Exception as e:
                logger.error(f"通过TokenStorage读取令牌出错: {str(e)}")
                logger.error(traceback.format_exc())
                status_info.append(f"通过TokenStorage读取令牌出错: {str(e)}")
                stored_tokens = []
                
                # 如果使用TokenStorage失败，尝试手动读取
                logger.info("尝试手动解析令牌文件...")
                status_info.append("尝试手动解析令牌文件...")
                
                # 确定要读取的文件路径
                file_to_read = None
                if os.path.exists(tokens_path) and file_can_open:
                    file_to_read = tokens_path
                elif alt_tokens_path and os.path.exists(alt_tokens_path):
                    file_to_read = alt_tokens_path
                else:
                    # 查找其他可能的路径
                    for path in other_paths:
                        if os.path.exists(path):
                            file_to_read = path
                            break
                
                # 如果找到了可读取的文件，尝试解析
                if file_to_read:
                    logger.info(f"尝试解析文件: {file_to_read}")
                    status_info.append(f"尝试解析文件: {file_to_read}")
                    try:
                        # 导入所需的模块
                        from update_cursor_token_main import TokenStorage
                        
                        # 读取文件内容
                        with open(file_to_read, 'rb') as f:
                            file_content = f.read()
                        
                        # 尝试解析和解密
                        if len(file_content) > 4:
                            try:
                                # 这里使用TokenStorage的解析逻辑，但直接提供文件内容
                                # 注意：这只是示例，可能需要根据实际TokenStorage实现调整
                                manual_tokens = []
                                offset = 0
                                
                                while offset < len(file_content):
                                    # 读取盐值大小
                                    salt_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                                    offset += 4
                                    
                                    # 读取盐值
                                    if offset + salt_size > len(file_content):
                                        break
                                    stored_salt = file_content[offset:offset+salt_size]
                                    offset += salt_size
                                    
                                    # 读取加密数据大小
                                    if offset + 4 > len(file_content):
                                        break
                                    data_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                                    offset += 4
                                    
                                    # 读取加密数据
                                    if offset + data_size > len(file_content):
                                        break
                                    stored_encrypted_data = file_content[offset:offset+data_size]
                                    offset += data_size
                                    
                                    try:
                                        # 解密数据
                                        token_info = TokenStorage.decrypt_data(stored_encrypted_data, stored_salt)
                                        manual_tokens.append(token_info)
                                    except Exception as decrypt_error:
                                        logger.error(f"解密令牌数据失败: {decrypt_error}")
                                        status_info.append(f"解密令牌数据失败: {decrypt_error}")
                                
                                logger.info(f"手动解析成功，找到 {len(manual_tokens)} 条令牌")
                                status_info.append(f"手动解析成功，找到 {len(manual_tokens)} 条令牌")
                                stored_tokens = manual_tokens
                            except Exception as parse_error:
                                logger.error(f"手动解析文件结构失败: {parse_error}")
                                logger.error(traceback.format_exc())
                                status_info.append(f"手动解析文件结构失败: {parse_error}")
                    except Exception as read_error:
                        logger.error(f"读取文件失败: {read_error}")
                        logger.error(traceback.format_exc())
                        status_info.append(f"读取文件失败: {read_error}")
            
            # 显示令牌读取状态提示框
            import tkinter as tk
            from tkinter import messagebox
            
            status_text = "\n".join(status_info)
            
            # 临时禁用状态提示框和状态日志文件 - 仅保留控制台日志
            '''
            try:
                messagebox.showinfo("令牌读取状态", status_text)
                logger.info("成功显示状态提示框")
            except Exception as e:
                logger.error(f"显示状态提示框失败: {e}")
                logger.error(traceback.format_exc())
                # 将详细信息写入额外的日志文件
                status_log_path = os.path.join(desktop_path, "token_status.log")
                try:
                    with open(status_log_path, "w") as f:
                        f.write(status_text)
                    logger.info(f"状态信息已写入: {status_log_path}")
                except Exception as write_error:
                    logger.error(f"写入状态日志失败: {write_error}")
            '''
            logger.info("侧边栏弹窗和token_status.log已禁用")
            
            if not stored_tokens:
                # 如果没有保存的账号，显示提示信息
                logger.info("未找到保存的账号记录")
                no_account_label = ctk.CTkLabel(
                    self.recent_accounts_frame,
                    text="暂无保存的账号记录",
                    text_color="#999999",
                    font=("Microsoft YaHei UI", 12)
                )
                no_account_label.pack(pady=15)
                self.recent_account_buttons.append(no_account_label)
                return
            
            # 只过滤当前API Key的记录
            current_api_key = self.app.api_key
            api_records = [token for token in stored_tokens if token.get("access_code") == current_api_key]
            
            # 如果当前API Key没有记录
            if not api_records:
                no_account_label = ctk.CTkLabel(
                    self.recent_accounts_frame,
                    text="当前API密钥无历史记录",
                    text_color="#999999",
                    font=("Microsoft YaHei UI", 12)
                )
                no_account_label.pack(pady=15)
                self.recent_account_buttons.append(no_account_label)
                return
                
            # 按最近切换时间倒序排序（最近切换的在前面）
            def get_last_switch_time(token):
                """获取账号的最近切换时间，用于排序"""
                last_switch_time = token.get("last_switch_time", "")
                if last_switch_time:
                    return last_switch_time
                else:
                    # 如果没有切换时间，使用创建时间
                    return token.get("timestamp", "1970-01-01T00:00:00")

            sorted_tokens = sorted(
                api_records,
                key=get_last_switch_time,
                reverse=True  # 倒序，最近切换的在前面
            )

            # 添加"最近使用"标签排序调试信息
            logger.info(f"=== 最近使用标签排序调试 ===")
            for i, token in enumerate(sorted_tokens[:5]):  # 只显示前5个
                email = token.get("token_data", {}).get("email", "未知邮箱")
                last_switch_time = token.get("last_switch_time", "")
                timestamp = token.get("timestamp", "")
                logger.info(f"显示位置 {i}: {email}")
                logger.info(f"  最近切换时间: {last_switch_time}")
                logger.info(f"  创建时间: {timestamp}")
                if i == 0:
                    logger.info(f"  → 将显示'最近使用'标签")
            logger.info(f"=== 最近使用标签排序调试结束 ===")
            
            # 过滤最近168小时的记录
            now = datetime.now(timezone(timedelta(hours=8)))  # 使用中国时区
            recent_tokens = []
            older_tokens = []  # 新增：存储超过168小时的记录
            
            for token in sorted_tokens:
                try:
                    token_time = datetime.fromisoformat(token.get("timestamp", "1970-01-01T00:00:00"))
                    # 确保时区信息存在
                    if token_time.tzinfo is None:
                        token_time = token_time.replace(tzinfo=timezone(timedelta(hours=8)))  # 使用中国时区
                    # 检查是否在168小时内
                    if (now - token_time).total_seconds() <= 168 * 3600:
                        recent_tokens.append(token)
                    else:
                        older_tokens.append(token)  # 新增：收集超过168小时的记录
                except Exception as e:
                    logger.error(f"处理token时间时出错: {e}")
                    continue
            
            # 更改逻辑：如果没有最近168小时的记录但有更早的记录，则取最近的一条
            if not recent_tokens and older_tokens:
                # 只取一条最近的记录（已按时间倒序排序）
                oldest_token = older_tokens[0]
                oldest_token["is_older"] = True  # 标记为超过168小时的记录
                recent_tokens.append(oldest_token)
            
            # 如果完全没有任何记录（168小时内和更早的都没有）- 这种情况应该不会出现，因为前面已经检查过api_records是否为空
            if not recent_tokens:
                no_recent_label = ctk.CTkLabel(
                    self.recent_accounts_frame,
                    text="此API密钥有历史记录但无法读取",  # 更改提示文本，因为正常情况下不应该到这里
                    text_color="#FF5555",  # 使用错误颜色
                    font=("Microsoft YaHei UI", 12)
                )
                no_recent_label.pack(pady=15)
                self.recent_account_buttons.append(no_recent_label)
                return
            
            # 最多显示5个最近账号（包括可能添加的超过168小时#7天的记录）
            display_tokens = recent_tokens[:5]
            
            # 添加标题标签
            if all(token.get("is_older", False) for token in display_tokens):
                # 如果所有显示的记录都是超过168小时的
                history_label = ctk.CTkLabel(
                    self.recent_accounts_frame,
                    text=f"超过168小时的使用记录",
                    text_color="#CCCCCC",
                    font=("Microsoft YaHei UI", 11, "italic")
                )
            else:
                history_label = ctk.CTkLabel(
                    self.recent_accounts_frame,
                    text=f"建议切换推荐值最高的账号或者新号",
                    text_color="#CCCCCC",
                    font=("Microsoft YaHei UI", 11, "italic")
                )
            history_label.pack(pady=(3, 5))
            self.recent_account_buttons.append(history_label)
            
            account_frames = []  # 存储account_frame的列表，用于推荐值计算
            
            for token_info in display_tokens:
                # 创建账号容器
                account_frame = ctk.CTkFrame(
                    self.recent_accounts_frame,
                    fg_color="#222222",
                    corner_radius=6,
                    border_width=0
                )
                account_frame.pack(fill=tk.X, padx=8, pady=3)
                self.recent_account_buttons.append(account_frame)
                account_frames.append(account_frame)  # 收集到account_frames列表中
                
                # 记录在列表中的索引
                record_index = display_tokens.index(token_info)
                
                # 提取账号信息
                access_code = token_info.get("access_code", "未知")
                email = token_info.get("token_data", {}).get("email", "未知邮箱")
                timestamp = token_info.get("timestamp", "")
                is_older = token_info.get("is_older", False)  # 获取是否为超过168小时的记录
                
                # 存储时间信息用于后续计算推荐值
                last_switch_timestamp = token_info.get("last_switch_time", "")
                if last_switch_timestamp:
                    try:
                        last_switch_dt = datetime.fromisoformat(last_switch_timestamp)
                    except:
                        last_switch_dt = datetime.fromisoformat(token_info.get("timestamp", "1970-01-01T00:00:00"))
                else:
                    # 如果没有最近切换时间，使用timestamp
                    try:
                        last_switch_dt = datetime.fromisoformat(token_info.get("timestamp", "1970-01-01T00:00:00"))
                    except:
                        last_switch_dt = datetime.fromisoformat("1970-01-01T00:00:00")
                
                # 确保时区信息存在
                if last_switch_dt.tzinfo is None:
                    last_switch_dt = last_switch_dt.replace(tzinfo=timezone(timedelta(hours=8)))
                
                # 将时间信息临时存储到token_info中，用于后续排序计算
                token_info["_temp_last_switch_dt"] = last_switch_dt

                # 为第一条记录（最近使用的）添加标签
                if record_index == 0:
                    # 创建"最近使用"标签框（位置调整到推荐值的左边）
                    latest_tag_frame = ctk.CTkFrame(
                        account_frame,
                        fg_color="#007AFF",  # 使用蓝色背景
                        corner_radius=4,
                        height=18,
                        width=70,
                    )
                    latest_tag_frame.place(relx=1.0, y=6, anchor="ne", x=-80)  # 位置调整到推荐值左边，x=-80让它在推荐值左侧
                    
                    # 添加标签文本
                    latest_tag = ctk.CTkLabel(
                        latest_tag_frame,
                        text="最近使用",
                        text_color="#FFFFFF",
                        font=("Microsoft YaHei UI", 9, "bold"),
                        height=18,
                        width=70
                    )
                    latest_tag.place(relx=0.5, rely=0.5, anchor="center")
                
                # 格式化时间
                formatted_time = "未知时间"
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp)
                        formatted_time = dt.strftime("%Y-%m-%d %H:%M")
                    except Exception as time_err:
                        logger.error(f"格式化时间错误: {time_err}")
                        formatted_time = "时间格式错误"
                
                # 截断access_code，只显示前6位和后4位
                if len(access_code) > 10:
                    masked_code = f"{access_code[:6]}...{access_code[-4:]}"
                else:
                    masked_code = access_code
                
                # 账号信息
                if is_older:
                    account_info = ctk.CTkLabel(
                        account_frame,
                        text=f"使用记录 #{display_tokens.index(token_info)+1} (超过168小时)",
                        text_color="#FFFFFF",
                        font=("Microsoft YaHei UI", 11, "bold"),
                        anchor="w"
                    )
                else:
                    account_info = ctk.CTkLabel(
                        account_frame,
                        text=f"使用记录 #{display_tokens.index(token_info)+1}",
                        text_color="#FFFFFF",
                        font=("Microsoft YaHei UI", 11, "bold"),
                        anchor="w"
                    )
                account_info.pack(anchor="w", padx=8, pady=(6, 0))
                
                # 添加账号状态标签
                status_frame = ctk.CTkFrame(
                    account_frame,
                    fg_color="transparent"
                )
                status_frame.pack(fill="x", padx=8, pady=(3, 0))
                
                # 状态标签
                status_label = ctk.CTkLabel(
                    status_frame,
                    text="检测中...",
                    text_color="#FFA500",  # 橙色表示加载中
                    font=("Microsoft YaHei UI", 10, "bold"),
                    anchor="w"
                )
                status_label.pack(side="left", anchor="w")
                
                # 刷新按钮
                refresh_status_btn = ctk.CTkButton(
                    status_frame,
                    text="刷新状态",
                    width=60,
                    height=20,
                    fg_color="#2A2A2A",
                    hover_color="#3A3A3A",
                    text_color="#AAAAAA",
                    font=("Microsoft YaHei UI", 9),
                    corner_radius=3,
                    command=lambda t=token_info.get("token_data", {}).get("token", ""), sl=status_label, ti=token_info: 
                        threading.Thread(target=self.fetch_and_update_account_status, args=(t, sl, ti), daemon=True).start()
                )
                refresh_status_btn.pack(side="right", anchor="e")
                
                # 保存状态标签引用
                token = token_info.get("token_data", {}).get("token", "")
                if token:
                    self.account_status_labels[token] = status_label

                    # 设置初始占位符状态，不立即开始网络请求
                    status_label.configure(
                        text="待检查",
                        text_color="#888888"  # 灰色表示待检查状态
                    )
                
                # 显示邮箱 - 格式化邮箱显示
                formatted_email = self.app.format_email_display(email) if self.app else email
                
                # 检查邮箱是否为空或无效
                is_empty_email = not email or not email.strip() or email == "未知邮箱"
                
                if is_empty_email:
                    # 邮箱为空，显示加载中并异步获取
                    display_email = "加载中..."
                    email_label = ctk.CTkLabel(
                        account_frame,
                        text=f"账号: {display_email}",
                        text_color="#FFA500",  # 使用橙色表示加载中
                        font=("Microsoft YaHei UI", 10),
                        anchor="w"
                    )
                    email_label.pack(anchor="w", padx=8, pady=(3, 0))
                    
                    # 异步获取邮箱
                    token = token_info.get("token_data", {}).get("token", "")
                    if token:
                        # 启动异步任务获取邮箱
                        threading.Thread(
                            target=self.fetch_and_update_email,
                            args=(token, email_label, token_info),
                            daemon=True
                        ).start()
                else:
                    # 邮箱不为空，正常显示
                    display_email = formatted_email if formatted_email and formatted_email.strip() else "未知账号"
                    email_label = ctk.CTkLabel(
                        account_frame,
                        text=f"账号: {display_email}",
                        text_color="#AAAAAA",
                        font=("Microsoft YaHei UI", 10),
                        anchor="w"
                    )
                    email_label.pack(anchor="w", padx=8, pady=(3, 0))
                
                # 显示时间 - 修改为左右两栏显示
                time_frame = ctk.CTkFrame(
                    account_frame,
                    fg_color="transparent"
                )
                time_frame.pack(fill="x", padx=8, pady=(2, 0))
                
                # 格式化初次切换时间
                first_time = formatted_time
                
                # 格式化最近切换时间
                last_switch_timestamp = token_info.get("last_switch_time", "")
                if last_switch_timestamp:
                    try:
                        dt = datetime.fromisoformat(last_switch_timestamp)
                        last_switch_time = dt.strftime("%Y-%m-%d %H:%M")
                    except Exception as time_err:
                        logger.error(f"格式化最近切换时间错误: {time_err}")
                        last_switch_time = first_time  # 如果解析失败，使用初次时间
                else:
                    last_switch_time = first_time  # 如果没有最近切换时间，使用初次时间
                
                # 左侧：初次切换
                first_time_label = ctk.CTkLabel(
                    time_frame,
                    text=f"初次切换: {first_time}",
                    text_color="#AAAAAA",
                    font=("Microsoft YaHei UI", 9),
                    anchor="w"
                )
                first_time_label.pack(side="left", anchor="w")
                
                # 右侧：最近切换
                last_time_label = ctk.CTkLabel(
                    time_frame,
                    text=f"最近切换: {last_switch_time}",
                    text_color="#AAAAAA",
                    font=("Microsoft YaHei UI", 9),
                    anchor="e"
                )
                last_time_label.pack(side="right", anchor="e")
                
                # 如果是超过168小时的记录，显示提示信息
                if is_older:
                    older_label = ctk.CTkLabel(
                        account_frame,
                        text="此账号已切换超过168小时",
                        text_color="#FF9500",  # 使用警告色
                        font=("Microsoft YaHei UI", 10, "italic"),
                        anchor="w"
                    )
                    older_label.pack(anchor="w", padx=8, pady=(2, 0))
                
                # 按钮容器框架
                button_frame = ctk.CTkFrame(
                    account_frame,
                    fg_color="transparent"
                )
                button_frame.pack(fill="x", padx=8, pady=(6, 8))

                # 切换账号按钮
                switch_button = ctk.CTkButton(
                    button_frame,
                    text="切换此账号",
                    fg_color="#333333",
                    hover_color="#444444",
                    text_color="#FFFFFF",
                    height=22,
                    corner_radius=4,
                    font=("Microsoft YaHei UI", 10),
                    command=lambda record=token_info: self.switch_to_saved_record(record)
                )
                switch_button.pack(side="left", fill="x", expand=True, padx=(0, 4))

                # 删除按钮
                delete_button = ctk.CTkButton(
                    button_frame,
                    text="删除",
                    fg_color="#333333",
                    hover_color="#444444",
                    text_color="#FFFFFF",
                    height=22,
                    width=50,
                    corner_radius=4,
                    font=("Microsoft YaHei UI", 10),
                    command=lambda record=token_info: self.delete_account_record(record)
                )
                delete_button.pack(side="right")

                # 创建推荐值占位符标签（立即显示，不阻塞UI）
                recommend_placeholder = ctk.CTkLabel(
                    account_frame,
                    text="推荐值分析中...",
                    fg_color="#666666",
                    text_color="#FFFFFF",
                    font=("Microsoft YaHei UI", 10),
                    corner_radius=8,
                    width=80,
                    height=20
                )
                recommend_placeholder.place(relx=1.0, rely=0.0, anchor="ne", x=-8, y=6)

            # 异步计算推荐值，不阻塞UI加载
            try:
                # 延迟500ms后开始异步计算推荐值，让UI先完全加载
                if self.app and hasattr(self.app, 'root'):
                    self.app.root.after(500, lambda: self.async_calculate_recommend_values(display_tokens, account_frames))
            except Exception as recommend_err:
                logger.error(f"启动异步推荐值计算时出错: {recommend_err}")
        
        except Exception as e:
            logger.error(f"加载最近账号列表时出错: {e}")
            logger.error(traceback.format_exc())
            
            # 显示错误信息
            error_label = ctk.CTkLabel(
                self.recent_accounts_frame,
                text=f"加载账号失败: {str(e)}",
                text_color="#FF5555",
                font=("Microsoft YaHei UI", 11)
            )
            error_label.pack(pady=15)
            self.recent_account_buttons.append(error_label)
            
    async def get_account_status(self, token):
        """获取账号状态"""
        try:
            from tongji import CursorUsageTracker
            
            # 如果token不包含%3A%3A，说明是原始JWT token，需要处理
            if '%3A%3A' not in token:
                try:
                    import jwt
                    # 解析JWT token获取用户ID
                    decoded = jwt.decode(token, options={"verify_signature": False})
                    user_id = decoded['sub'].split('|')[1]
                    # 格式化为CursorUsageTracker需要的格式
                    processed_token = f"{user_id}%3A%3A{token}"
                    logger.debug(f"处理Token: {token[:20]}... -> {processed_token[:20]}...")
                except Exception as e:
                    logger.error(f"处理Token失败: {e}")
                    return "TOKEN_ERROR", "#F44336"  # 红色
            else:
                processed_token = token
            
            tracker = CursorUsageTracker(processed_token)
            subscription_data = await tracker.get_subscription_info()
            
            if subscription_data and not subscription_data.get('error'):
                subscription_type = subscription_data.get('subscription_type', 'unknown')
                is_trial = subscription_data.get('is_trial', False)
                trial_days = subscription_data.get('trial_days_remaining', 0)
                
                # 格式化显示文本
                if subscription_type == 'pro':
                    return "PRO", "#4CAF50"  # 绿色
                elif subscription_type == 'trial':
                    return f"PRO TRIAL ({trial_days}天)", "#FF9500"  # 橙色
                elif subscription_type == 'team':
                    return "TEAM", "#2196F3"  # 蓝色
                elif subscription_type == 'free':
                    return "已失效", "#9E9E9E"  # 灰色
                else:
                    return "UNKNOWN", "#9E9E9E"  # 灰色
            else:
                return "ERROR", "#F44336"  # 红色
        except Exception as e:
            logger.error(f"获取账号状态失败: {e}")
            return "ERROR", "#F44336"  # 红色

    def fetch_and_update_account_status(self, token, status_label, token_info):
        """在后台线程中获取账号状态并更新显示"""
        try:
            # 标记为正在刷新
            self.refreshing_status.add(token)
            
            # 在新的事件循环中运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                status_text, status_color = loop.run_until_complete(self.get_account_status(token))
                
                # 在主线程中更新UI
                def update_ui():
                    try:
                        if status_label and status_label.winfo_exists():
                            status_label.configure(
                                text=status_text,
                                text_color=status_color
                            )
                        # 移除刷新标记
                        self.refreshing_status.discard(token)
                        
                        # 检查是否所有账号状态都已更新完成，如果是则异步重新计算推荐值
                        if len(self.refreshing_status) == 0:
                            # 延迟执行推荐值计算，避免阻塞UI
                            if self.app and hasattr(self.app, 'root'):
                                self.app.root.after(200, self.trigger_recommend_value_recalculation)
                            
                    except Exception as ui_err:
                        logger.error(f"更新状态UI失败: {ui_err}")
                        self.refreshing_status.discard(token)
                
                # 使用after方法在主线程中执行
                if self.app and hasattr(self.app, 'root'):
                    self.app.root.after(0, update_ui)
                    
            except Exception as async_err:
                logger.error(f"异步获取账号状态失败: {async_err}")
                
                def update_ui_error():
                    try:
                        if status_label and status_label.winfo_exists():
                            status_label.configure(
                                text="获取失败",
                                text_color="#F44336"
                            )
                        self.refreshing_status.discard(token)
                        
                        # 检查是否所有账号状态都已更新完成，如果是则异步重新计算推荐值
                        if len(self.refreshing_status) == 0:
                            # 延迟执行推荐值计算，避免阻塞UI
                            if self.app and hasattr(self.app, 'root'):
                                self.app.root.after(200, self.trigger_recommend_value_recalculation)
                            
                    except Exception as ui_err:
                        logger.error(f"更新错误状态UI失败: {ui_err}")
                        self.refreshing_status.discard(token)
                
                if self.app and hasattr(self.app, 'root'):
                    self.app.root.after(0, update_ui_error)
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"获取账号状态时发生错误: {e}")
            self.refreshing_status.discard(token)
            
            def update_ui_error():
                try:
                    if status_label and status_label.winfo_exists():
                        status_label.configure(
                            text="网络错误",
                            text_color="#F44336"
                        )
                    # 移除刷新标记并检查是否需要重新计算推荐值
                    self.refreshing_status.discard(token)
                    if len(self.refreshing_status) == 0:
                        # 延迟执行推荐值计算，避免阻塞UI
                        if self.app and hasattr(self.app, 'root'):
                            self.app.root.after(200, self.trigger_recommend_value_recalculation)
                        
                except Exception as ui_err:
                    logger.error(f"更新网络错误状态UI失败: {ui_err}")
            
            if self.app and hasattr(self.app, 'root'):
                self.app.root.after(0, update_ui_error)

    def refresh_all_account_status(self):
        """刷新所有账号的状态 - 优化版本，不阻塞UI"""
        logger.info("开始刷新侧边栏所有账号状态（后台异步）")

        def start_background_refresh():
            """在后台线程中批量刷新所有账号状态"""
            logger.info("后台线程开始批量刷新账号状态")

            # 收集所有需要刷新的账号
            tokens_to_refresh = []
            for token, status_label in self.account_status_labels.items():
                if token not in self.refreshing_status:
                    tokens_to_refresh.append((token, status_label))

            logger.info(f"需要刷新的账号数量: {len(tokens_to_refresh)}")

            # 为每个账号启动独立的后台线程，但不阻塞UI
            for token, status_label in tokens_to_refresh:
                try:
                    # 在主线程中更新状态为检查中
                    def update_checking_status():
                        if status_label and status_label.winfo_exists():
                            status_label.configure(
                                text="检查中...",
                                text_color="#FFA500"
                            )

                    if self.app and hasattr(self.app, 'root'):
                        self.app.root.after(0, update_checking_status)

                    # 启动后台刷新线程
                    threading.Thread(
                        target=self.fetch_and_update_account_status,
                        args=(token, status_label, None),
                        daemon=True
                    ).start()

                    # 添加小延迟避免同时发起太多请求，但不阻塞UI
                    time.sleep(0.1)  # 减少延迟时间

                except Exception as e:
                    logger.error(f"启动账号状态刷新失败 {token}: {e}")

        # 在独立的后台线程中执行刷新逻辑
        threading.Thread(target=start_background_refresh, daemon=True).start()
        logger.info("账号状态刷新已在后台启动，不会阻塞UI")

    def start_delayed_status_check(self):
        """延迟启动账号状态检查，给用户选择权"""
        logger.info("用户点击'检查所有账号状态'按钮")

        # 更新按钮状态，提供视觉反馈
        if hasattr(self, 'check_status_button'):
            self.check_status_button.configure(
                text="检查中...",
                fg_color="#FF9800",  # 橙色表示正在处理
                state="disabled"  # 禁用按钮防止重复点击
            )

        # 统计当前显示的所有账号数量
        total_accounts = 0
        for token, status_label in self.account_status_labels.items():
            if status_label and status_label.winfo_exists():
                total_accounts += 1

        logger.info(f"开始检查当前显示的 {total_accounts} 个账号的状态")

        # 为当前显示的所有账号启动检查（不管当前状态如何）
        checked_count = 0
        for token, status_label in self.account_status_labels.items():
            if token not in self.refreshing_status:
                try:
                    # 检查标签是否存在
                    if status_label and status_label.winfo_exists():
                        # 启动后台检查
                        threading.Thread(
                            target=self.fetch_and_update_account_status,
                            args=(token, status_label, None),
                            daemon=True
                        ).start()

                        # 立即更新为检查中状态
                        status_label.configure(
                            text="检查中...",
                            text_color="#FFA500"
                        )

                        checked_count += 1
                        # 添加小延迟避免同时发起太多请求
                        time.sleep(0.1)

                except Exception as e:
                    logger.error(f"启动延迟状态检查失败 {token}: {e}")

        # 如果没有需要检查的账号，立即恢复按钮状态
        if checked_count == 0:
            logger.info("没有需要检查的账号")
            if hasattr(self, 'check_status_button'):
                self.check_status_button.configure(
                    text="检查所有账号状态",
                    fg_color="#4CAF50",
                    state="normal"
                )
        else:
            # 延迟恢复按钮状态，给检查过程一些时间
            def restore_button():
                if hasattr(self, 'check_status_button'):
                    self.check_status_button.configure(
                        text="检查所有账号状态",
                        fg_color="#4CAF50",
                        state="normal"
                    )

            # 根据账号数量调整恢复时间
            restore_delay = min(5000, checked_count * 1000)  # 最多5秒
            if self.app and hasattr(self.app, 'root'):
                self.app.root.after(restore_delay, restore_button)

    def auto_start_background_status_check(self):
        """侧边栏UI加载完成后自动开始后台状态检查，完全不阻塞UI"""
        logger.info("开始自动后台状态检查（不阻塞UI）")

        def background_check_worker():
            """在独立线程中执行状态检查，完全不影响UI响应"""
            try:
                logger.info("后台线程开始自动状态检查")

                # 收集需要检查的账号
                accounts_to_check = []
                for token, status_label in self.account_status_labels.items():
                    if token not in self.refreshing_status:
                        try:
                            if status_label and status_label.winfo_exists():
                                current_text = status_label.cget("text")
                                if current_text == "待检查":
                                    accounts_to_check.append((token, status_label))
                        except Exception as e:
                            logger.error(f"检查账号状态标签失败 {token}: {e}")

                logger.info(f"找到 {len(accounts_to_check)} 个需要自动检查的账号")

                if not accounts_to_check:
                    logger.info("没有需要自动检查的账号")
                    return

                # 为每个账号启动独立的检查线程
                for i, (token, status_label) in enumerate(accounts_to_check):
                    try:
                        # 在主线程中更新状态为检查中（使用after确保线程安全）
                        def update_checking_status(label=status_label):
                            try:
                                if label and label.winfo_exists():
                                    label.configure(
                                        text="检查中...",
                                        text_color="#FFA500"
                                    )
                            except Exception as e:
                                logger.error(f"更新检查中状态失败: {e}")

                        if self.app and hasattr(self.app, 'root'):
                            self.app.root.after(0, update_checking_status)

                        # 启动独立的状态检查线程
                        threading.Thread(
                            target=self.fetch_and_update_account_status,
                            args=(token, status_label, None),
                            daemon=True,
                            name=f"AutoCheck-{i+1}"
                        ).start()

                        # 添加小延迟避免同时发起太多请求，但不阻塞UI
                        time.sleep(0.2)  # 200ms间隔

                    except Exception as e:
                        logger.error(f"启动自动状态检查失败 {token}: {e}")

                logger.info(f"已启动 {len(accounts_to_check)} 个账号的自动后台状态检查")

            except Exception as e:
                logger.error(f"自动后台状态检查出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

        # 在独立的后台线程中执行，完全不阻塞UI
        threading.Thread(
            target=background_check_worker,
            daemon=True,
            name="AutoStatusChecker"
        ).start()

        logger.info("自动状态检查已在后台启动，不会影响UI响应")

    def async_calculate_recommend_values(self, display_tokens, account_frames):
        """异步计算推荐值，不阻塞UI"""
        def background_calculate():
            """在后台线程中计算推荐值"""
            try:
                logger.info("开始异步计算推荐值")

                # 在后台线程中调用推荐值计算
                threading.Thread(
                    target=self.calculate_and_display_recommend_values_async,
                    args=(display_tokens, account_frames),
                    daemon=True,
                    name="RecommendValueCalculator"
                ).start()

            except Exception as e:
                logger.error(f"启动推荐值计算线程失败: {e}")

        # 在独立线程中执行，完全不阻塞UI
        threading.Thread(target=background_calculate, daemon=True).start()

    def trigger_recommend_value_recalculation(self):
        """触发推荐值重新计算"""
        try:
            # 重新收集当前显示的tokens和account_frames
            display_tokens = []
            account_frames = []

            # 从当前侧边栏中收集信息
            for widget in self.recent_accounts_frame.winfo_children():
                if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'CTkFrame':
                    # 这是一个账号容器
                    account_frames.append(widget)

            # 重新读取token存储获取最新数据
            try:
                from update_cursor_token_main import TokenStorage
                tokens_path = TokenStorage.get_tokens_path()
                
                if tokens_path.exists():
                    with open(tokens_path, 'rb') as f:
                        file_content = f.read()
                    
                    stored_tokens = []
                    offset = 0
                    
                    while offset < len(file_content):
                        salt_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                        offset += 4
                        stored_salt = file_content[offset:offset+salt_size]
                        offset += salt_size
                        data_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                        offset += 4
                        stored_encrypted_data = file_content[offset:offset+data_size]
                        offset += data_size
                        
                        try:
                            token_data = TokenStorage.decrypt_data(stored_encrypted_data, stored_salt)
                            stored_tokens.append(token_data)
                        except:
                            continue
                    
                    # 过滤当前API Key的记录
                    current_api_key = self.app.api_key if self.app else None
                    if current_api_key:
                        api_records = [token for token in stored_tokens if token.get("access_code") == current_api_key]
                        
                        # 按时间排序，取最近的记录
                        from datetime import datetime, timezone, timedelta
                        now = datetime.now(timezone(timedelta(hours=8)))
                        recent_tokens = []
                        
                        for token in api_records:
                            try:
                                timestamp = token.get("timestamp", "1970-01-01T00:00:00")
                                token_time = datetime.fromisoformat(timestamp)
                                if token_time.tzinfo is None:
                                    token_time = token_time.replace(tzinfo=timezone(timedelta(hours=8)))
                                
                                time_diff = (now - token_time).total_seconds() / 3600
                                if time_diff <= 168:
                                    recent_tokens.append(token)
                                    
                            except Exception:
                                continue
                        
                        display_tokens = sorted(recent_tokens, key=lambda x: x.get("timestamp", ""), reverse=True)
                        
                        # 确保账号框架数量匹配
                        min_count = min(len(display_tokens), len(account_frames))
                        display_tokens = display_tokens[:min_count]
                        account_frames = account_frames[:min_count]
                        
                        # 添加时间信息
                        for token_info in display_tokens:
                            last_switch_timestamp = token_info.get("last_switch_time", "")
                            if last_switch_timestamp:
                                try:
                                    last_switch_dt = datetime.fromisoformat(last_switch_timestamp)
                                except:
                                    last_switch_dt = datetime.fromisoformat(token_info.get("timestamp", "1970-01-01T00:00:00"))
                            else:
                                try:
                                    last_switch_dt = datetime.fromisoformat(token_info.get("timestamp", "1970-01-01T00:00:00"))
                                except:
                                    last_switch_dt = datetime.fromisoformat("1970-01-01T00:00:00")
                            
                            if last_switch_dt.tzinfo is None:
                                last_switch_dt = last_switch_dt.replace(tzinfo=timezone(timedelta(hours=8)))
                            
                            token_info["_temp_last_switch_dt"] = last_switch_dt
                        
                        # 重新计算推荐值
                        if display_tokens and account_frames:
                            logger.info("状态刷新完成，重新计算推荐值")
                            self.calculate_and_display_recommend_values(display_tokens, account_frames)
                        
            except Exception as storage_err:
                logger.error(f"重新读取token存储失败: {storage_err}")
                
        except Exception as e:
            logger.error(f"触发推荐值重新计算失败: {e}")
            logger.error(traceback.format_exc())

    def fetch_and_update_email(self, token, email_label, token_info):
        """
        在后台线程中获取邮箱并更新显示
        
        Args:
            token: 用户token
            email_label: 邮箱显示标签
            token_info: token信息字典
        """
        try:
            # 在新的事件循环中运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                email = loop.run_until_complete(get_email_from_token(token))
                
                if email:
                    logger.info(f"成功获取邮箱: {email}")
                    
                    # 更新本地文件中的邮箱信息
                    self.update_token_email_in_storage(token_info, email)
                    
                    # 在主线程中更新UI
                    def update_ui():
                        try:
                            # 格式化邮箱显示
                            formatted_email = self.app.format_email_display(email) if self.app else email
                            email_label.configure(
                                text=f"账号: {formatted_email}",
                                text_color="#AAAAAA"  # 恢复正常颜色
                            )
                        except Exception as e:
                            logger.error(f"更新UI时出错: {e}")
                    
                    # 使用after方法在主线程中执行UI更新
                    self.after(0, update_ui)
                else:
                    logger.error("获取邮箱失败")
                    # 更新显示为获取失败
                    def update_ui_error():
                        try:
                            email_label.configure(
                                text="账号: 获取失败",
                                text_color="#FF5555"  # 使用红色表示错误
                            )
                        except Exception as e:
                            logger.error(f"更新错误UI时出错: {e}")
                    
                    self.after(0, update_ui_error)
                    
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"获取邮箱过程中出错: {e}")
            logger.error(traceback.format_exc())
            
            # 更新显示为获取失败
            def update_ui_error():
                try:
                    email_label.configure(
                        text="账号: 获取失败",
                        text_color="#FF5555"  # 使用红色表示错误
                    )
                except Exception as e:
                    logger.error(f"更新错误UI时出错: {e}")
            
            self.after(0, update_ui_error)
    
    def calculate_and_display_recommend_values_async(self, display_tokens, account_frames):
        """
        异步版本：基于所有账号的相对时间排序计算推荐值，不阻塞UI

        Args:
            display_tokens: 显示的token列表
            account_frames: 对应的账号框架列表
        """
        try:
            logger.info("开始异步计算推荐值（后台线程）")

            # 首先获取所有账号的状态，过滤掉free账号
            valid_tokens_for_recommendation = []
            valid_account_frames = []
            token_status_map = {}

            # 异步获取所有账号状态
            async def get_all_statuses():
                tasks = []
                for token_info in enumerate(display_tokens):
                    if isinstance(token_info, tuple):
                        _, token_info = token_info
                    token = token_info.get("token_data", {}).get("token", "")
                    if token:
                        tasks.append(self.get_account_status(token))
                    else:
                        # 创建一个返回错误状态的协程
                        async def error_status():
                            return ("ERROR", "#F44336")
                        tasks.append(error_status())
                return await asyncio.gather(*tasks)

            # 在新事件循环中获取状态
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                statuses = loop.run_until_complete(get_all_statuses())

                # 处理状态结果
                for i, (token_info, (status_text, status_color)) in enumerate(zip(display_tokens, statuses)):
                    token = token_info.get("token_data", {}).get("token", "")
                    token_status_map[token] = (status_text, status_color)

                    # 如果不是free账号，加入推荐值计算
                    if status_text != "已失效":
                        valid_tokens_for_recommendation.append(token_info)
                        valid_account_frames.append(account_frames[i])

            finally:
                loop.close()

            # 在主线程中更新UI
            def update_recommend_values_ui():
                try:
                    self._update_recommend_values_display(
                        display_tokens, account_frames,
                        valid_tokens_for_recommendation, token_status_map
                    )
                except Exception as ui_err:
                    logger.error(f"更新推荐值UI失败: {ui_err}")

            # 使用after方法在主线程中执行UI更新
            if self.app and hasattr(self.app, 'root'):
                self.app.root.after(0, update_recommend_values_ui)

        except Exception as e:
            logger.error(f"异步计算推荐值时出错: {e}")
            logger.error(traceback.format_exc())

    def calculate_and_display_recommend_values(self, display_tokens, account_frames):
        """
        基于所有账号的相对时间排序计算推荐值（同步版本，保留用于兼容）

        Args:
            display_tokens: 显示的token列表
            account_frames: 对应的账号框架列表
        """
        try:
            # 首先获取所有账号的状态，过滤掉free账号
            valid_tokens_for_recommendation = []
            valid_account_frames = []
            token_status_map = {}

            # 异步获取所有账号状态
            async def get_all_statuses():
                tasks = []
                for token_info in enumerate(display_tokens):
                    if isinstance(token_info, tuple):
                        _, token_info = token_info
                    token = token_info.get("token_data", {}).get("token", "")
                    if token:
                        tasks.append(self.get_account_status(token))
                    else:
                        # 创建一个返回错误状态的协程
                        async def error_status():
                            return ("ERROR", "#F44336")
                        tasks.append(error_status())
                return await asyncio.gather(*tasks)

            # 在新事件循环中获取状态
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                statuses = loop.run_until_complete(get_all_statuses())

                # 处理状态结果
                for i, (token_info, (status_text, status_color)) in enumerate(zip(display_tokens, statuses)):
                    token = token_info.get("token_data", {}).get("token", "")
                    token_status_map[token] = (status_text, status_color)

                    # 如果不是free账号，加入推荐值计算
                    if status_text != "已失效":
                        valid_tokens_for_recommendation.append(token_info)
                        valid_account_frames.append(account_frames[i])

            finally:
                loop.close()

            # 直接更新显示
            self._update_recommend_values_display(
                display_tokens, account_frames,
                valid_tokens_for_recommendation, token_status_map
            )

        except Exception as e:
            logger.error(f"计算推荐值时出错: {e}")
            logger.error(traceback.format_exc())

    def _update_recommend_values_display(self, display_tokens, account_frames, valid_tokens_for_recommendation, token_status_map):
        """
        更新推荐值显示的核心逻辑

        Args:
            display_tokens: 显示的token列表
            account_frames: 对应的账号框架列表
            valid_tokens_for_recommendation: 有效的推荐token列表
            token_status_map: token状态映射
        """
        try:
            # 按最近切换时间排序（越久未使用的排在前面）- 只计算非free账号
            sorted_by_time = sorted(
                valid_tokens_for_recommendation,
                key=lambda x: x.get("_temp_last_switch_dt", datetime.fromtimestamp(0, tz=timezone(timedelta(hours=8)))),
                reverse=False  # 升序，最久未使用的在前面
            )

            total_valid_accounts = len(sorted_by_time)

            # 添加排序调试信息
            logger.info(f"=== 推荐值排序调试 ===")
            logger.info(f"有效账号总数: {total_valid_accounts}")
            for i, token_info in enumerate(sorted_by_time):
                email = token_info.get("token_data", {}).get("email", "未知邮箱")
                switch_time = token_info.get("_temp_last_switch_dt")
                logger.info(f"排序位置 {i}: {email} - 切换时间: {switch_time}")
            logger.info(f"=== 排序调试结束 ===")

            # 为每个账号分配推荐值或已失效标签
            for i, token_info in enumerate(display_tokens):
                try:
                    token = token_info.get("token_data", {}).get("token", "")
                    status_text, status_color = token_status_map.get(token, ("UNKNOWN", "#9E9E9E"))

                    account_frame = account_frames[i]
                    if not account_frame or not hasattr(account_frame, 'winfo_exists') or not account_frame.winfo_exists():
                        continue

                    # 先移除现有的推荐值标签（如果存在）
                    for child in account_frame.winfo_children():
                        if hasattr(child, 'cget') and hasattr(child, 'place_info'):
                            try:
                                text = child.cget('text')
                                if text and ('推荐' in text or '已失效' in text or '分析中' in text):
                                    child.destroy()
                            except:
                                pass

                    # 如果是free账号，显示已失效标签
                    if status_text == "已失效":
                        expired_tag = ctk.CTkLabel(
                            account_frame,
                            text="已失效",
                            fg_color="#666666",
                            text_color="#FFFFFF",
                            font=("Microsoft YaHei UI", 10, "bold"),
                            corner_radius=8,
                            width=60,
                            height=20
                        )
                        expired_tag.place(relx=1.0, rely=0.0, anchor="ne", x=-8, y=6)
                    else:
                        # 非free账号，计算推荐值
                        if token_info in valid_tokens_for_recommendation:
                            # 在排序列表中找到当前账号的位置
                            time_rank = sorted_by_time.index(token_info)

                            # 计算相对推荐值：最久未使用的账号推荐值最高
                            if total_valid_accounts == 1:
                                recommend_value = 50  # 只有一个账号时固定中等推荐值
                                logger.info(f"  单账号情况，固定推荐值: {recommend_value}%")
                            else:
                                # 使用非线性分布，让差异更明显
                                relative_position = time_rank / (total_valid_accounts - 1)  # 0到1之间

                                # 最久未使用(relative_position=0)→推荐值95%
                                # 最近使用(relative_position=1)→推荐值10%
                                recommend_value = int(95 - (relative_position * 85)) + 5

                                # 添加相对位置计算调试信息
                                logger.info(f"  排序位置: {time_rank}/{total_valid_accounts-1}")
                                logger.info(f"  相对位置: {relative_position:.3f}")
                                logger.info(f"  计算公式: 95 - ({relative_position:.3f} × 85) + 5 = {recommend_value}")

                                # 时间惩罚和奖励机制
                                current_time = datetime.now(timezone(timedelta(hours=8)))
                                last_switch_dt = token_info.get("_temp_last_switch_dt")
                                if last_switch_dt:
                                    time_diff_minutes = (current_time - last_switch_dt).total_seconds() / 60
                                    time_diff_hours = time_diff_minutes / 60

                                    # 添加详细的调试日志
                                    token_email = token_info.get("token_data", {}).get("email", "未知邮箱")
                                    logger.info(f"推荐值计算 - 账号: {token_email}")
                                    logger.info(f"  当前时间: {current_time}")
                                    logger.info(f"  最近切换时间: {last_switch_dt}")
                                    logger.info(f"  时间差: {time_diff_hours:.2f}小时 ({time_diff_minutes:.2f}分钟)")
                                    logger.info(f"  基础推荐值: {recommend_value}")

                                    if time_diff_hours >= 24:  # 24小时以上，强制100%推荐
                                        recommend_value = 100
                                        logger.info(f"  超过24小时奖励: {recommend_value}% (账号已刷新)")
                                    elif time_diff_minutes < 240:  # 4小时内才有惩罚
                                        # 根据时间差异分配不同的推荐值，都在15%以内但有区别
                                        if time_diff_minutes < 30:  # 30分钟内
                                            recommend_value = 5  # 最低推荐值
                                            logger.info(f"  应用30分钟内惩罚: {recommend_value}%")
                                        elif time_diff_minutes < 60:  # 1小时内
                                            recommend_value = 8  # 稍高一点
                                            logger.info(f"  应用1小时内惩罚: {recommend_value}%")
                                        elif time_diff_minutes < 120:  # 2小时内
                                            recommend_value = 11  # 再高一点
                                            logger.info(f"  应用2小时内惩罚: {recommend_value}%")
                                        else:  # 2-4小时内
                                            recommend_value = 15  # 最高但仍在15%以内
                                            logger.info(f"  应用4小时内惩罚: {recommend_value}%")
                                    else:
                                        logger.info(f"  无时间惩罚，保持基础推荐值: {recommend_value}%")

                            # 确定颜色
                            recommend_color = "#8B0000" if recommend_value >= 80 else "#CC6600" if recommend_value >= 50 else "#2F5233"

                            recommend_tag = ctk.CTkLabel(
                                account_frame,
                                text=f"推荐{recommend_value}%",
                                fg_color=recommend_color,
                                text_color="#FFFFFF",
                                font=("Microsoft YaHei UI", 10, "bold"),
                                corner_radius=8,
                                width=60,
                                height=20
                            )
                            recommend_tag.place(relx=1.0, rely=0.0, anchor="ne", x=-8, y=6)

                    # 清理临时数据
                    if "_temp_last_switch_dt" in token_info:
                        del token_info["_temp_last_switch_dt"]

                except Exception as token_err:
                    logger.error(f"处理单个token推荐值时出错: {token_err}")
                    continue

        except Exception as e:
            logger.error(f"更新推荐值显示时出错: {e}")
            logger.error(traceback.format_exc())

    
    def update_token_email_in_storage(self, token_info, new_email):
        """
        更新本地存储中的邮箱信息
        
        Args:
            token_info: 原始token信息
            new_email: 新的邮箱地址
        """
        try:
            # 导入TokenStorage
            from update_cursor_token_main import TokenStorage
            
            # 获取存储文件路径
            tokens_path = TokenStorage.get_tokens_path()
            
            if not tokens_path.exists():
                logger.warning("Token存储文件不存在，无法更新邮箱")
                return
            
            # 读取现有数据
            with open(tokens_path, 'rb') as f:
                file_content = f.read()
            
            # 解析所有token记录
            stored_tokens = []
            offset = 0
            
            while offset < len(file_content):
                # 读取盐值大小
                salt_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                offset += 4
                
                # 读取盐值
                stored_salt = file_content[offset:offset+salt_size]
                offset += salt_size
                
                # 读取加密数据大小
                data_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                offset += 4
                
                # 读取加密数据
                stored_encrypted_data = file_content[offset:offset+data_size]
                offset += data_size
                
                try:
                    # 解密数据
                    token_data = TokenStorage.decrypt_data(stored_encrypted_data, stored_salt)
                    stored_tokens.append(token_data)
                except Exception as e:
                    logger.error(f"解密令牌数据失败: {e}")
                    continue
            
            # 查找并更新匹配的记录
            target_record_id = token_info.get("record_id", "")
            updated = False
            
            for token_data in stored_tokens:
                if token_data.get("record_id") == target_record_id:
                    # 更新邮箱
                    token_data["token_data"]["email"] = new_email
                    updated = True
                    logger.info(f"更新记录 {target_record_id} 的邮箱为: {new_email}")
                    break
            
            if updated:
                # 重新写入文件
                with open(tokens_path, 'wb') as f:
                    for token_data in stored_tokens:
                        # 重新加密数据
                        enc_data, enc_salt = TokenStorage.encrypt_data(token_data)
                        
                        # 写入盐值大小
                        f.write(len(enc_salt).to_bytes(4, byteorder='big'))
                        # 写入盐值
                        f.write(enc_salt)
                        # 写入加密数据大小
                        f.write(len(enc_data).to_bytes(4, byteorder='big'))
                        # 写入加密数据
                        f.write(enc_data)
                
                logger.info("邮箱信息已成功更新到本地存储")
            else:
                logger.warning(f"未找到匹配的记录ID: {target_record_id}")
                
        except Exception as e:
            logger.error(f"更新本地存储邮箱时出错: {e}")
            logger.error(traceback.format_exc())

    def switch_to_saved_record(self, token_info):
        """使用保存的记录直接切换账号"""
        try:
            logger.info(f"准备使用保存的历史记录切换账号...")
            
            # 检查app引用
            if not self.app:
                messagebox.showerror("错误", "系统异常：未找到主应用引用")
                return
                
            # 获取token数据
            if "token_data" not in token_info:
                messagebox.showerror("错误", "无法获取保存的账号数据")
                return
            
            # 导入TokenData
            from update_cursor_token_main import TokenData
            
            # 创建TokenData实例
            token_data = TokenData.from_dict(token_info["token_data"])
            
            # 验证API Key并执行账号切换
            messagebox.showinfo("操作确认", "即将使用历史记录中的账号进行切换，请点击确定继续")
            
            # 更新上次切换时间
            try:
                from update_cursor_token_main import TokenStorage
                access_code = token_info.get("access_code")
                record_id = token_info.get("record_id")
                if access_code and record_id:
                    TokenStorage.update_last_switch_time(access_code, record_id)
                    logger.info("已更新上次切换时间")
                else:
                    logger.warning("缺少access_code或record_id，无法更新切换时间")
            except Exception as update_err:
                logger.error(f"更新上次切换时间失败: {update_err}")
                # 不影响主流程，继续执行
            
            # 调用主应用的账号切换函数，传入已有的token数据，标记为来自侧边栏
            self.app.update_action(saved_token=token_data, from_sidebar=True)
            
            # 切换成功后刷新侧边栏显示
            # 延迟刷新，让TokenStorage有时间保存新的切换时间
            self.app.root.after(1000, self.load_recent_accounts)

            # 换号后自动检查账号状态，就像打开侧边栏时一样
            logger.info("账号切换成功，侧边栏将重新加载，并在2秒后自动开始后台状态检查")
            if self.app and hasattr(self.app, 'root'):
                self.app.root.after(2000, self.auto_start_background_status_check)
            
        except Exception as e:
            logger.error(f"使用保存账号切换时出错: {e}")
            logger.error(traceback.format_exc())
            messagebox.showerror("错误", f"账号切换失败: {str(e)}")

    def delete_account_record(self, token_info):
        """删除单个账号记录"""
        try:
            import tkinter.messagebox as messagebox
            
            # 获取账号信息用于确认对话框
            email = token_info.get("token_data", {}).get("email", "未知邮箱")
            record_id = token_info.get("record_id", "未知ID")
            
            # 确认删除
            result = messagebox.askyesno(
                "确认删除账号",
                f"确定要删除此账号记录吗？\n\n"
                f"邮箱: {email}\n"
                f"记录ID: {record_id}\n\n"
                "⚠️ 此操作不可逆！删除后无法恢复！",
                icon="warning"
            )
            
            if not result:
                return
            
            logger.info(f"开始删除账号记录: {record_id}")
            
            # 导入TokenStorage类（从账号删除器.py）
            from 账号删除器 import TokenStorage
            
            # 加载所有token数据
            all_tokens = TokenStorage.load_all_tokens()
            if not all_tokens:
                logger.warning("没有找到任何token数据")
                messagebox.showinfo("提示", "没有找到任何账号数据")
                return
            
            # 找到要删除的记录
            target_index = None
            for i, token in enumerate(all_tokens):
                if (token.get("record_id") == record_id and 
                    token.get("access_code") == token_info.get("access_code")):
                    target_index = i
                    break
            
            if target_index is None:
                logger.warning(f"未找到匹配的记录: {record_id}")
                messagebox.showwarning("警告", "未找到要删除的记录")
                return
            
            # 删除记录
            deleted_account = all_tokens.pop(target_index)
            
            # 重新分配索引
            for i, account in enumerate(all_tokens):
                account['_index'] = i
            
            # 保存更新后的数据
            if TokenStorage.save_tokens(all_tokens):
                logger.info(f"成功删除账号记录: {record_id}")
                messagebox.showinfo("删除成功", f"已成功删除账号：{email}")
                
                # 刷新侧边栏列表
                self.load_recent_accounts()
            else:
                logger.error("保存文件时出错，删除操作失败")
                messagebox.showerror("错误", "保存文件时出错，删除操作失败！")
                
        except Exception as e:
            logger.error(f"删除账号记录时出错: {e}")
            logger.error(traceback.format_exc())
            messagebox.showerror("错误", f"删除账号记录时出错:\n{str(e)}")
            
    def toggle_panel(self):
        """切换面板显示状态"""
        current_geometry = self.parent.geometry()
        current_width = int(current_geometry.split('x')[0])
        current_height = int(current_geometry.split('x')[1].split('+')[0])
        
        # 获取主框架当前位置和大小
        main_frame = self.parent.children['!ctkframe']
        main_frame_width = main_frame.winfo_width()
        
        if self.expanded:
            # 收起面板
            self.place_configure(relx=1.0, x=0)
            self.toggle_button.configure(text="切换历史记录")
            # 恢复原始窗口大小
            if self.original_window_width:
                new_geometry = f"{self.original_window_width}x{current_height}"
                self.parent.geometry(new_geometry)
                # 恢复主框架原始位置和边距
                main_frame.pack(fill="both", expand=True, padx=7, pady=5)
        else:
            # 保存当前窗口宽度
            if not self.original_window_width:
                self.original_window_width = current_width
            
            # 展开面板，增加窗口宽度
            new_width = self.original_window_width + self.width
            new_geometry = f"{new_width}x{current_height}"
            self.parent.geometry(new_geometry)
            
            # 等待窗口几何更新完成
            self.parent.update_idletasks()
            
            # 获取主框架的实际客户区域高度，避免包含窗口装饰
            main_frame_height = main_frame.winfo_height()
            main_frame_y = main_frame.winfo_y()
            
            # 计算侧边栏的合适高度，确保不超出窗口客户区域
            # 使用主框架的位置和高度作为参考
            panel_y = main_frame_y
            panel_height = main_frame_height
            
            # 恢复相对定位，但使用正确的高度
            self.place_configure(relx=1.0, x=-self.width, y=panel_y, height=panel_height)
            self.toggle_button.configure(text="关闭拓展窗口")
            
            # 调整主框架位置和大小 - 预留更多空间给侧边栏
            main_frame.pack_forget()
            main_frame.pack(fill="both", expand=True, padx=(7, self.width + 10), pady=5)
            
            # 展开侧边栏时立即刷新最近账号列表（不等待网络请求）
            self.load_recent_accounts()

            # UI加载完成后自动触发一次状态检查，但完全不阻塞UI
            logger.info("侧边栏UI已展开，将在1秒后自动开始后台状态检查")
            if self.app and hasattr(self.app, 'root'):
                self.app.root.after(1000, self.auto_start_background_status_check)
            
            # 确保侧边栏在最上层
            self.lift()
            
        self.expanded = not self.expanded

class OptimizedFishButton(ctk.CTkFrame):
    """性能优化的带有鱼游动效果的按钮"""
    def __init__(self, master, width=330, height=36, text="切换账号", command=None, **kwargs):
        super().__init__(master, width=width, height=height, fg_color="transparent", **kwargs)
        
        # 基本参数
        self.width = width
        self.height = height
        self.command = command
        self.is_running = False
        self.text = text  # 保存按钮文本
        self.master = master  # 保存父容器引用
        
        # 鱼的参数
        self.fish_length = 20  # 鱼的长度（占边框周长的比例）
        self.fish_position = 0  # 鱼头的位置（从0到1的值，表示在边框周长上的位置）
        self.fish_color = "#FDCB6E"  # 鱼的基本颜色（淡黄色）
        self.fish_speed = 0.005  # 鱼的游动速度
        self.animation_interval = 10  # 动画间隔(ms)
        
        # 边框线段优化 - 减少点的数量来提高性能
        self.segment_count = 80  # 边框段数
        
        # 创建Canvas作为边框
        self.border_canvas = tk.Canvas(
            self, 
            width=width, 
            height=height, 
            bg="#1A1A1A",  # 与按钮背景色相同
            highlightthickness=0,
            bd=0
        )
        self.border_canvas.pack(fill=tk.BOTH, expand=True)
        
        # 创建内部按钮
        button_width = width - 6  # 边框宽度为3px
        button_height = height - 6
        self.button = ctk.CTkButton(
            self.border_canvas,
            text=text,
            width=button_width,
            height=button_height,
            corner_radius=0,  # 方形按钮
            fg_color="#1A1A1A",  # 深色背景
            hover_color="#252525",  # 悬停颜色
            text_color="#FFFFFF",  # 白色文字
            font=("Microsoft YaHei UI", 13),
            command=self.on_click,
            border_width=0  # 不需要内部边框
        )
        
        # 放置按钮在Canvas中央
        self.button_window = self.border_canvas.create_window(
            width/2, height/2, 
            window=self.button, 
            width=button_width, 
            height=button_height
        )
        
        # 计算边框路径的点
        self.border_segments = self.calculate_border_segments()
        
        # 存储线段的引用以便更新
        self.line_ids = []
        self.create_border_lines()
        
        # 绑定事件
        self.border_canvas.bind("<Button-1>", self.on_click)
        self.bind("<Enter>", self._on_mouse_enter)
        self.bind("<Leave>", self._on_mouse_leave)
        self.bind("<Configure>", self._on_resize)  # 添加大小变化事件绑定
        
        # 启动动画
        self.start_animation()
        
        # 设置5秒后停止动画的定时器
        self.after(10000, self.stop_animation_with_color)
    
    def _on_resize(self, event=None):
        """处理大小变化事件"""
        # 获取当前实际宽度 - 可能来自事件或父容器
        new_width = event.width if event and event.width > 1 else self.winfo_width()
        new_height = event.height if event and event.height > 1 else self.height
        
        # 如果宽度不合理，尝试从父容器获取
        if new_width <= 1 and hasattr(self, 'master'):
            master_width = self.master.winfo_width()
            if master_width > 20:  # 检查父容器宽度是否合理
                new_width = master_width - 20  # 预留边距
        
        # 只在尺寸确实变化时更新
        if new_width > 1 and (self.width != new_width or self.height != new_height):
            # 更新尺寸
            self.width = new_width
            self.height = new_height
            
            # 更新Canvas尺寸
            self.border_canvas.config(width=self.width, height=self.height)
            
            # 更新内部按钮尺寸和位置
            button_width = max(10, self.width - 6)  # 确保按钮有最小宽度
            button_height = self.height - 6
            self.button.configure(width=button_width, height=button_height)
            self.border_canvas.coords(self.button_window, self.width/2, self.height/2)
            self.border_canvas.itemconfig(self.button_window, width=button_width, height=button_height)
            
            # 重新计算边框段并更新
            self.border_segments = self.calculate_border_segments()
            
            # 删除旧线段
            for line_id in self.line_ids:
                self.border_canvas.delete(line_id)
            
            # 创建新线段
            self.line_ids = []
            self.create_border_lines()
            
            # 更新边框颜色
            self.update_border_colors()
    
    def _on_mouse_enter(self, event=None):
        """鼠标进入时加速"""
        if self.is_running:
            self.fish_speed = 0.02
    
    def _on_mouse_leave(self, event=None):
        """鼠标离开时恢复正常速度"""
        if self.is_running:
            self.fish_speed = 0.005
    
    def calculate_border_segments(self):
        """计算边框分段 - 使用更少的点，并确保在各种宽度下都能正常工作"""
        # 创建边框四周的段
        segments = []
        w, h = self.width, self.height
        
        # 计算合适的段数 - 根据周长动态调整
        perimeter = 2 * (w + h)
        self.segment_count = max(40, min(100, int(perimeter / 5)))  # 根据周长动态调整段数，但保持在合理范围
        
        # 将周长分为等距离的点
        points_per_segment = perimeter / self.segment_count
        
        # 上边
        top_points = max(4, int(w / points_per_segment))  # 确保至少有4个点
        for i in range(top_points):
            x = i * (w / top_points)
            segments.append(((x, 0), (x + w/top_points, 0)))
        
        # 右边
        right_points = max(4, int(h / points_per_segment))  # 确保至少有4个点
        for i in range(right_points):
            y = i * (h / right_points)
            segments.append(((w, y), (w, y + h/right_points)))
        
        # 下边
        bottom_points = max(4, int(w / points_per_segment))  # 确保至少有4个点
        for i in range(bottom_points):
            x = w - i * (w / bottom_points)
            segments.append(((x, h), (x - w/bottom_points, h)))
        
        # 左边
        left_points = max(4, int(h / points_per_segment))  # 确保至少有4个点
        for i in range(left_points):
            y = h - i * (h / left_points)
            segments.append(((0, y), (0, y - h/left_points)))
            
        return segments
    
    def create_border_lines(self):
        """创建初始边框线段"""
        for segment in self.border_segments:
            (x1, y1), (x2, y2) = segment
            line_id = self.border_canvas.create_line(
                x1, y1, x2, y2, 
                fill="#1A1A1A",  # 初始颜色为背景色
                width=3,  # 3px 宽边框
                joinstyle=tk.ROUND  # 圆角连接
            )
            self.line_ids.append(line_id)
    
    def get_segment_color(self, segment_idx):
        """计算特定段的颜色"""
        total_segments = len(self.border_segments)
        
        # 计算鱼头的位置
        head_segment = int(self.fish_position * total_segments) % total_segments
        
        # 计算鱼的长度对应的段数
        fish_segments = int(total_segments * self.fish_length / 100)
        
        # 计算当前段与鱼头的距离
        distance = (head_segment - segment_idx) % total_segments
        
        # 如果不在鱼的范围内，返回背景色
        if distance > fish_segments:
            return "#1A1A1A"  # 背景色
        
        # 计算颜色强度（鱼头强，鱼尾弱）
        intensity = 1.0 - (distance / fish_segments) ** 1.5
        
        # 转换基础颜色到HSV空间
        r, g, b = 253/255, 203/255, 110/255  # 淡黄色
        h, s, v = colorsys.rgb_to_hsv(r, g, b)
        
        # 调整HSV中的V（亮度）和S（饱和度）
        adjusted_v = max(0.3, v * intensity)
        adjusted_s = max(0.2, s * intensity)
        
        # 转回RGB并生成颜色代码
        r, g, b = colorsys.hsv_to_rgb(h, adjusted_s, adjusted_v)
        color = f"#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}"
        
        return color
    
    def update_border_colors(self):
        """更新边框颜色 - 只更改颜色，不重新创建线条"""
        for i, line_id in enumerate(self.line_ids):
            color = self.get_segment_color(i)
            self.border_canvas.itemconfig(line_id, fill=color)
    
    def animate(self):
        """动画函数"""
        if not self.is_running:
            return
        
        # 更新鱼的位置
        self.fish_position = (self.fish_position + self.fish_speed) % 1.0
        
        # 更新边框颜色
        self.update_border_colors()
        
        # 计划下一帧
        self.after(self.animation_interval, self.animate)
    
    def start_animation(self):
        """开始动画"""
        if not self.is_running:
            self.is_running = True
            self.animate()
    
    def stop_animation(self):
        """停止动画"""
        self.is_running = False
    
    def stop_animation_with_color(self):
        """停止动画并设置整个边框为黄色"""
        self.is_running = False
        # 将所有边框线段设置为黄色
        for line_id in self.line_ids:
            self.border_canvas.itemconfig(line_id, fill="#FDCB6E")
    
    def update_text(self, text):
        """更新按钮文本"""
        self.text = text
        self.button.configure(text=text)
    
    def on_click(self, event=None):
        """点击事件"""
        if self.command:
            self.command()

class ModernResetApp:
    from update_cursor_token_main import Config
    
    # 默认版本号常量
    DEFAULT_VERSION = "2.6灰度测试"
    
    # 添加类变量用于跟踪连接失败次数
    cursor_connect_fail_count = 0
    cursor_connect_paused = False
    
    
    def save_cursor_workspaces(self):
        """保存当前Cursor打开的工作区信息"""
        try:
            logger.info("正在保存当前Cursor工作区信息...")
            restart_manager = CursorRestartManager()
            
            # 保存当前工作区信息
            success = restart_manager.save_active_workspaces()
            
            if success:
                workspaces = restart_manager.active_workspaces
                logger.info(f"成功保存 {len(workspaces)} 个工作区信息")
                for ws in workspaces:
                    logger.info(f"- {ws}")
                # 将工作区信息缓存到应用程序的类属性中，以便后续使用
                self.saved_workspaces = restart_manager.active_workspaces
                return True
            else:
                logger.warning("未找到活动工作区")
                self.saved_workspaces = []
                return False
        except Exception as e:
            logger.error(f"保存工作区信息时出错: {e}")
            logger.error(traceback.format_exc())
            self.saved_workspaces = []
            return False
    
    def start_cursor_with_saved_workspaces(self):
        """启动Cursor并恢复之前保存的工作区"""
        try:
            logger.info("正在启动Cursor并恢复工作区...")
            restart_manager = CursorRestartManager()
            
            # 如果有之前保存的工作区信息，则使用它
            if hasattr(self, 'saved_workspaces') and self.saved_workspaces:
                restart_manager.active_workspaces = self.saved_workspaces
                logger.info(f"使用之前保存的 {len(self.saved_workspaces)} 个工作区信息")
            
            # 启动Cursor并打开工作区
            result = restart_manager.start_cursor_with_workspaces()
            
            if result:
                logger.info("Cursor已成功启动并尝试恢复工作区")
                return True
            else:
                logger.error("Cursor启动失败")
                return False
        except Exception as e:
            logger.error(f"启动Cursor时出错: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def __init__(self, api_key=None):
        logger.info("="*50)
        logger.info("初始化 ModernResetApp")
        logger.info("="*50)
        
        # 加载配置
        self.load_config()
        logger.info(f"当前应用版本: {self.config.get('current_version', self.DEFAULT_VERSION)}")
        
        # 公告相关
        self.announcement_url = "https://apikey-9lz.pages.dev/announcements.json"
        logger.info(f"公告URL: {self.announcement_url}")
        # 添加备用公告URL（添加时间戳避免缓存）
        self.backup_announcement_urls = [
            f"{self.announcement_url}?t={int(time.time())}",
            "https://gitee.com/wayix/gonggao/raw/master/announcements.json"
        ]
        logger.info(f"备用公告URL: {self.backup_announcement_urls}")
        self.announcement_check_interval = 300  # 5分钟检查一次
        logger.debug(f"公告检查间隔: {self.announcement_check_interval}秒")
        self.announcement_timer = None
        self.download_url = None
        self.announcement_clickable = False  # 初始化公告可点击状态为False
        
        # 添加缓存变量用于窗口高度计算
        self.cached_required_height = 714  # 初始默认高度（从710增加到714）
        self.cached_announcement_text = ""  # 初始空文本
        self.last_height_check_time = 0  # 添加时间戳变量，用于限制检查频率
        
        # 优雅的配色方案
        logger.debug("初始化界面配色方案...")
        self.bg_color = "#000000"              # 纯净黑色背景
        self.main_container_color = "#000000"   # 主容器采用纯净黑色
        self.section_color = "#111111"          # 模块背景采用高级深灰
        self.entry_bg = "#1A1A1A"               # 输入区域背景色
        self.entry_text = "#CCCCCC"             # 输入文字采用优雅浅灰
        self.placeholder = "#666666"            # 占位符采用高级中灰
        self.accent_color = "#007AFF"           # 主要强调色采用科技蓝
        self.secondary_accent = "#FF9500"       # 次要强调色采用温暖橙
        self.success_color = "#28CD41"          # 成功状态采用生机绿
        self.font_color_light = "#CCCCCC"       # 主要文字采用优雅浅灰
        self.font_color_dim = "#999999"         # 次要文字采用高级中灰
        self.button_hover = "#0063CC"           # 按钮悬停采用深邃蓝
        self.button_color = self.accent_color   # 按钮颜色，与主要强调色一致
        logger.debug("界面配色方案初始化完成")

        # 字体设计
        logger.debug("初始化字体设置...")
        self.title_font = ("Microsoft YaHei UI", 28)  # 标题字体
        self.normal_font = ("Microsoft YaHei UI", 13)  # 正文字体
        self.small_font = ("Microsoft YaHei UI", 12)   # 辅助文字
        self.button_font = ("Microsoft YaHei UI", 13)  # 按钮字体
        self.status_value_font = ("Microsoft YaHei UI", 15)  # 状态值字体
        logger.debug("字体设置初始化完成")

        # 初始化自动刷新定时器
        self.auto_refresh_timer = None
        logger.debug("自动刷新定时器已初始化")

        # 初始化管理器
        logger.info("初始化各个管理器...")
        self.api_key = api_key
        self.account_checker = None
        self.token_manager = None
        self.cursor_manager = CursorManager()
        self.cursor_auth_manager = CursorAuthManager()
        logger.info("管理器初始化完成")
        
        # 初始化GUI
        logger.info("开始初始化GUI...")
        ctk.set_appearance_mode("dark")
        self.root = ctk.CTk()
        self.root.title("cursor重置工具专业版")
        # 设置最小窗口尺寸以确保按钮不会因为窗口过小而显示不全
        self.root.minsize(375, 714)
        self.root.geometry("395x714")  # 稍微增加默认宽度（从710增加到714）
        self.root.configure(fg_color=self.bg_color)
        
        # 允许窗口调整大小，以便用户可以根据需要调整
        self.root.resizable(True, True)
        
        # 主框架使用weight配置，以便能够随窗口大小调整
        self.main_frame = ctk.CTkFrame(self.root, fg_color=self.main_container_color)
        self.main_frame.pack(fill="both", expand=True, padx=7, pady=5)
        
        # 为主窗口绑定Configure事件，在窗口大小变化时重新计算布局
        self.root.bind("<Configure>", self._on_window_configure)
        logger.debug("主框架已创建")

        # 创建GUI组件
        logger.debug("开始创建GUI组件...")
        self.create_header()
        self.create_api_section()
        self.create_account_section()
        self.create_action_section()
        logger.debug("GUI组件创建完成")
        
        # 立即检查权限
        logger.info("开始检查系统权限...")
        self.check_permissions()
        logger.info("系统权限检查完成")
        
        # 如果有保存的API Key，自动填充并验证
        if hasattr(self, 'config') and self.config.get('save_api_key', True) and self.config.get('api_key'):
            logger.info("检测到已保存的API Key，开始自动填充和验证...")
            api_key = self.config['api_key']
            self.api_entry.insert(0, api_key)
            threading.Thread(target=lambda: self.verify_api_key(api_key)).start()
            logger.info("API Key自动填充和验证已启动")
            
        # 初始化账号信息并启动自动刷新
        logger.info("初始化账号信息...")
        self.initialize_account()
        logger.info("ModernResetApp初始化完成")
        logger.info("="*50)

        # 创建主窗口后添加样式
        self.style = ttk.Style()
        self.style.configure("SidePanel.TFrame", background=self.bg_color)  # 使用相同的背景色
        
        # 创建侧边栏，传递self引用
        self.side_panel = SidePanel(self.root, app=self)
        
        # 主框架保持原有位置
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=7, pady=5)

    def _on_window_configure(self, event=None):
        """处理窗口大小变化事件"""
        if event and event.widget == self.root:
            # 更新按钮尺寸
            if hasattr(self, 'reset_button'):
                self.reset_button.event_generate("<Configure>")
            
            # 无限制状态显示框已重构，不再需要动态更新换行宽度
            
            # 添加时间戳检查，限制每秒最多执行5次（每200毫秒最多一次）
            current_time = int(time.time() * 1000)  # 转换为毫秒
            if current_time - self.last_height_check_time >= 400:  # 确保至少间隔200毫秒
                self.last_height_check_time = current_time
                
                # 检查是否需要调整窗口高度
                current_height = event.height
                required_height = self.calculate_required_window_height()
                
                # 如果当前高度小于所需高度，调整窗口
                if current_height < required_height:
                    self.root.geometry(f"{event.width}x{required_height}")
                    logger.debug(f"窗口高度已调整: {required_height}px")

    def check_permissions(self):
        """检查并请求必要的权限"""
        try:
            import subprocess
            import platform
            import os
            from pathlib import Path
            
            logger.info("="*50)
            logger.info("开始系统权限检查")
            logger.info("-"*50)
            
            # 检查是否是 macOS
            current_system = platform.system()
            logger.info(f"当前系统: {current_system}")
            
            if current_system != 'Darwin':
                logger.info("非 macOS 系统，跳过权限检查")
                return True
            
            # 检查 Cursor 是否在运行
            logger.info("检查 Cursor 是否在运行...")
            if self.check_cursor_running():
                logger.warning("Cursor 正在运行，但将继续执行程序")
                # 移除了警告弹窗，继续执行
            
            logger.info("检查 Cursor.app 是否存在...")
            cursor_app = '/Applications/Cursor.app'
            if not os.path.exists(cursor_app):
                logger.warning("未找到 Cursor.app")
                logger.warning(f"预期路径: {cursor_app}")
                messagebox.showwarning("提示", "未在应用程序文件夹中找到 Cursor.app，请检测是否安装在默认文件夹")
                return False
            else:
                logger.info("已找到 Cursor.app")
                try:
                    app_size = os.path.getsize(cursor_app)
                    logger.debug(f"Cursor.app 大小: {app_size} 字节")
                except Exception as e:
                    logger.warning(f"无法获取 Cursor.app 大小: {e}")
            
            # 检查 App 管理权限
            logger.info("检查 App 管理权限...")
            if not self.check_app_management_permission():
                logger.warning("需要 App 管理权限")
                messagebox.showinfo(
                    "需要权限",
                    "请在系统设置中授予以下权限：\n\n"
                    "1. 在'隐私与安全性'中允许应用程序自动化\n"
                    "2. 在'App管理'中允许管理其他应用\n\n"
                    "授权后请重新启动应用。"
                )
                try:
                    subprocess.run([
                        'open',
                        'x-apple.systempreferences:com.apple.preference.security?Privacy_Automation'
                    ])
                except Exception as e:
                    logger.error(f"打开系统设置失败: {e}")
                return False
                
            # 检查自动化权限
            logger.info("开始检查自动化权限...")
            check_cmd = """
            osascript -e '
                tell application "System Events"
                    try
                        get every process
                        return true
                    on error
                        return false
                    end try
                end tell
            '
            """
            
            logger.debug("执行自动化权限检查命令...")
            result = subprocess.run(['osascript', '-e', check_cmd], 
                                 capture_output=True, 
                                 text=True,
                                 encoding='utf-8')
            
            logger.info(f"自动化权限检查结果: {result.stdout.strip()}")
            if result.returncode != 0:
                logger.warning(f"自动化权限检查失败: {result.stderr.strip()}")
            
            if "false" in result.stdout.lower():
                logger.warning("需要自动化权限")
                messagebox.showinfo(
                    "需要权限",
                    "请在系统设置中授予以下权限：\n\n"
                    "1. 在'隐私与安全性'中允许应用程序自动化\n"
                    "2. 在'App管理'中允许管理其他应用\n\n"
                    "授权后请重新启动应用。"
                )
                try:
                    subprocess.run([
                        'open',
                        'x-apple.systempreferences:com.apple.preference.security?Privacy_Automation'
                    ])
                except Exception as e:
                    logger.error(f"打开系统设置失败: {e}")
                return False
            
            logger.info("已获得自动化权限")
            
            # 检查文件权限
            logger.info("检查文件权限...")
            cursor_paths = {
                "main.js": Path(cursor_app) / "Contents" / "Resources" / "app" / "out" / "main.js",
                "storage.json": Path.home() / "Library" / "Application Support" / "Cursor" / "User" / "globalStorage" / "storage.json",
                "state.vscdb": Path.home() / "Library" / "Application Support" / "Cursor" / "User" / "globalStorage" / "state.vscdb"
            }
            
            for name, path in cursor_paths.items():
                logger.info(f"检查 {name} 权限...")
                results = self.check_file_permissions(path)
                if not results["exists"]:
                    logger.error(f"{name} 不存在")
                    continue
                    
                if not results["readable"]:
                    logger.error(f"{name} 不可读")
                    return False
                    
                if not results["writable"]:
                    logger.error(f"{name} 不可写")
                    return False
                    
                if results["needs_app_management"] and not results["has_app_management"]:
                    logger.error(f"{name} 需要 App 管理权限")
                    return False
                    
                logger.info(f"{name} 权限检查通过")
            
            logger.info("所有权限检查通过")
            return True
                
        except Exception as e:
            logger.error(f"检查权限时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return False
            
    def check_cursor_running(self):
        """检查 Cursor 是否在运行"""
        try:
            result = os.popen("pgrep -f Cursor.app").read()
            return bool(result.strip())
        except Exception as e:
            logger.error(f"检查 Cursor 运行状态时出错: {e}")
            return True  # 如果检查出错，假设正在运行，以确保安全
            
    def check_app_management_permission(self):
        """检查是否有 App 管理权限"""
        try:
            test_path = Path("/Applications/._permission_test")
            try:
                test_path.touch()
                test_file_exists = test_path.exists()
                if test_file_exists:
                    test_path.unlink()
                return test_file_exists
            except PermissionError:
                return False
        except Exception as e:
            logger.error(f"检查 App 管理权限时出错: {e}")
            return False
            
    def check_file_permissions(self, file_path: Path):
        """检查文件权限"""
        results = {
            "exists": False,
            "readable": False,
            "writable": False,
            "has_extended_attrs": False,
            "extended_attrs": [],
            "needs_app_management": False,
            "has_app_management": False,
            "error": None
        }
        
        try:
            results["exists"] = file_path.exists()
            if not results["exists"]:
                results["error"] = "文件不存在"
                return results
                
            # 检查读权限
            try:
                with open(file_path, 'r') as _:
                    results["readable"] = True
            except Exception as e:
                results["error"] = f"无法读取文件: {str(e)}"
            
            # 检查扩展属性
            try:
                result = subprocess.run(['xattr', str(file_path)], 
                                     capture_output=True, 
                                     text=True)
                results["extended_attrs"] = result.stdout.strip().split('\n') if result.stdout.strip() else []
                results["has_extended_attrs"] = bool(results["extended_attrs"])
            except Exception as e:
                logger.warning(f"检查扩展属性时出错: {e}")
            
            # 如果是应用程序目录，需要 App 管理权限
            if "/Applications/" in str(file_path):
                results["needs_app_management"] = True
                results["has_app_management"] = self.check_app_management_permission()
                if not results["has_app_management"]:
                    results["error"] = "需要 App 管理权限"
                    results["writable"] = False
                    return results
            
            # 检查写权限
            try:
                parent_dir = file_path.parent
                test_file = parent_dir / f"test_write_{os.getpid()}.tmp"
                try:
                    test_file.touch()
                    test_file.unlink()
                    results["writable"] = True
                except PermissionError:
                    results["writable"] = False
                    if not results["error"]:
                        results["error"] = "无写入权限"
            except Exception as e:
                results["error"] = f"无法写入目录: {str(e)}"
            
            return results
            
        except Exception as e:
            results["error"] = f"权限检查时发生错误: {str(e)}"
            return results

    def create_header(self):
        logger.info("="*30)
        logger.info("创建应用头部和公告区域")
        
        # 创建公告区域
        self.announcement_frame = ctk.CTkFrame(
            self.main_frame,
            fg_color="#1A1A1A",
            height=40,
            cursor="hand2"  # 鼠标悬停时显示手型
        )
        self.announcement_frame.pack(fill="x", pady=(5, 0), padx=12)
        logger.info("公告框架已创建")
        
        self.announcement_label = ctk.CTkLabel(
            self.announcement_frame,
            text="加载公告中...",
            font=("Microsoft YaHei UI", 12),
            text_color="#CCCCCC",
            wraplength=340,  # 允许文本换行
            cursor="hand2"  # 鼠标悬停时显示手型
        )
        self.announcement_label.pack(pady=8)
        logger.info("公告标签已创建，初始文本: '加载公告中...'")
        
        # 绑定点击事件
        self.announcement_frame.bind("<Button-1>", self.on_announcement_click)
        self.announcement_label.bind("<Button-1>", self.on_announcement_click)
        logger.info("公告区域点击事件已绑定")

        # 原有的header
        header = ctk.CTkLabel(
            self.main_frame,
            text="Cursor Professional",
            font=self.title_font,
            text_color="#CCCCCC"  # 优雅浅灰
        )
        header.pack(pady=(15, 15))  # 增加上下间距
        logger.info("应用标题已创建")
        logger.info("头部创建完成")

    def on_announcement_click(self, event):
        """处理公告点击事件""" 
        logger.info("="*30)
        logger.info("公告区域被点击")
        
        # 获取当前版本和云端版本
        current_version = self.config.get('current_version', self.DEFAULT_VERSION)
        
        # 获取当前公告文本和颜色
        current_text = self.announcement_label.cget("text")
        text_color = self.announcement_label.cget("text_color")
        logger.info(f"当前公告文本: {current_text}")
        logger.info(f"当前文本颜色: {text_color}")
        
        # 检查是否是版本更新提示
        if "发现新版本" in current_text and self.download_url:
            logger.info("检测到版本更新提示，打开下载链接")
            logger.info(f"下载URL: {self.download_url}")
            import webbrowser
            webbrowser.open(self.download_url)
            return
            
        # 检查是否是可点击的普通消息
        if hasattr(self, 'announcement_clickable') and self.announcement_clickable and hasattr(self, 'message_url') and self.message_url:
            logger.info("检测到可点击消息，打开消息链接")
            logger.info(f"消息URL: {self.message_url}")
            import webbrowser
            webbrowser.open(self.message_url)
            return
            
        logger.info("公告不可点击或没有关联的URL")

    def create_api_section(self):
        # API Key 区块
        api_frame = ctk.CTkFrame(self.main_frame, fg_color=self.section_color)
        api_frame.pack(fill="x", pady=12, padx=12)

        # API Key 标题
        title_label = ctk.CTkLabel(
            api_frame,
            text="API Key 验证",
            font=("Microsoft YaHei UI", 16, "bold"),
            text_color="#CCCCCC"  # 优雅浅灰
        )
        title_label.pack(pady=(11, 8))

        api_container = ctk.CTkFrame(api_frame, fg_color="transparent")
        api_container.pack(padx=15, pady=(0, 12))

        api_entry = ctk.CTkEntry(
            api_container,
            width=220,
            height=40,
            placeholder_text="请输入API密钥",
            fg_color=self.entry_bg,
            border_color=self.accent_color,
            placeholder_text_color=self.placeholder,
            text_color=self.entry_text,
            font=self.normal_font
        )
        api_entry.pack(side="left", padx=(0, 10))
        
        # 创建右键菜单
        self.api_context_menu = tk.Menu(api_entry, tearoff=0, bg="#1E1E1E", fg="#CCCCCC", activebackground="#333333", activeforeground="#FFFFFF")
        self.api_context_menu.add_command(label="复制", command=self.copy_api_key)
        self.api_context_menu.add_command(label="粘贴", command=self.paste_api_key)
        
        # 绑定右键点击事件 - 兼容Windows和macOS
        api_entry.bind("<Button-3>", self.show_api_context_menu)  # Windows右键
        api_entry.bind("<Button-2>", self.show_api_context_menu)  # macOS右键或中键
        api_entry.bind("<Control-Button-1>", self.show_api_context_menu)  # macOS Control+左键

        verify_button = ctk.CTkButton(
            api_container,
            text="验证",
            width=80,
            height=40,
            fg_color=self.accent_color,
            hover_color=self.button_hover,
            text_color=self.font_color_light,
            font=("Microsoft YaHei UI", 13),
            corner_radius=6,
            command=self.verify_button_click
        )
        verify_button.pack(side="left")

        status_container = ctk.CTkFrame(api_frame, fg_color="transparent")
        status_container.pack(fill="x", padx=15, pady=(7, 7))

        indicators = [
            ("总账号数", "0", self.success_color),
            ("已使用", "0", self.secondary_accent),
            ("剩余", "0", self.accent_color),
            # ("每日限额", "0/0", "#666666"), # 移除每日限额
            ("剩余天数", "未知", "#666666"),
            ("过期时间", "未知", "#666666"),
            ("状态", "未验证", "#666666")
        ]
        
        indicator_container = ctk.CTkFrame(status_container, fg_color="transparent")
        indicator_container.pack(fill="x", expand=True)
        
        # 修改网格布局以适应6个指标
        indicator_container.grid_columnconfigure((0, 1, 2), weight=1)
        indicator_container.grid_rowconfigure((0, 1), weight=1)
        
        self.indicators = []
        
        for idx, (title, value, color) in enumerate(indicators):
            # 计算行和列的位置
            row = idx // 3
            col = idx % 3
            
            indicator = ctk.CTkFrame(indicator_container, fg_color="#0A0A0A")
            indicator.grid(row=row, column=col, sticky="nsew", padx=5, pady=2)
            
            ctk.CTkLabel(
                indicator,
                text=title,
                text_color=self.font_color_light,
                font=("Microsoft YaHei UI", 12)
            ).pack(pady=(8, 0))
            
            value_label = ctk.CTkLabel(
                indicator,
                text=value,
                text_color=color,
                font=self.status_value_font
            )
            value_label.pack(pady=(0, 6))
            
            self.indicators.append((title, value_label))

        # 保存Entry和指标引用
        self.api_entry = api_entry

    def create_account_section(self):
        account_frame = ctk.CTkFrame(self.main_frame, fg_color=self.section_color)
        account_frame.pack(fill="x", pady=12, padx=12)
        
        # 账号信息和按钮容器
        account_header = ctk.CTkFrame(account_frame, fg_color="transparent")
        account_header.pack(fill="x", pady=(15, 10), padx=15)
        
        # 账号信息容器（左侧）
        account_info = ctk.CTkFrame(account_header, fg_color="transparent")
        account_info.pack(side="left", fill="x", expand=True)
        
        # 账号类型标签（第一行）
        type_container = ctk.CTkFrame(account_info, fg_color="transparent")
        type_container.pack(fill="x")
        
        ctk.CTkLabel(
            type_container,
            text="当前cursor账号:",
            text_color="#888888",  # 浅灰色
            font=("Microsoft YaHei UI", 12)
        ).pack(side="left")
        
        # 账号类型标签
        self.account_type_label = ctk.CTkLabel(
            type_container,
            text="",
            text_color="#666666",  # 更暗的灰色
            font=("Microsoft YaHei UI", 11)  # 更小的字号
        )
        self.account_type_label.pack(side="left", padx=(8, 0))
        
        # 邮箱标签（第二行）
        email_container = ctk.CTkFrame(account_info, fg_color="transparent")
        email_container.pack(side="left", fill="x", expand=True, pady=(4, 0))  # 添加上边距

        self.email_label = ctk.CTkLabel(
            email_container,
            text="未登录",
            text_color="#AAAAAA",  # 使用较浅的灰色
            font=("Microsoft YaHei UI", 11)  # 较小的字号
        )
        self.email_label.pack(side="left")

        # 添加自动刷新失败提示标签
        self.refresh_fail_label = ctk.CTkLabel(
            email_container,
            text="",
            text_color="#FF3B30",  # 红色
            font=("Microsoft YaHei UI", 11, "italic")  # 使用斜体
        )
        self.refresh_fail_label.pack(side="left", padx=(10, 0))

        # 右侧按钮容器
        button_container = ctk.CTkFrame(account_header, fg_color="transparent")
        button_container.pack(side="right", padx=(10, 0))

        # 设置按钮
        settings_button = ctk.CTkButton(
            button_container,
            text="设置",
            width=65,
            height=22,
            corner_radius=4,
            fg_color="#2A2A2A",
            hover_color="#3A3A3A",
            text_color="#AAAAAA",
            font=("Microsoft YaHei UI", 11),
            command=self.show_settings,
            border_width=1,
            border_color="#333333"
        )
        settings_button.pack(side="top")

        # 刷新按钮
        refresh_button = ctk.CTkButton(
            button_container,
            text="刷新用量",
            width=65,
            height=22,
            corner_radius=4,
            fg_color="#2A2A2A",
            hover_color="#3A3A3A",
            text_color="#AAAAAA",
            font=("Microsoft YaHei UI", 11),
            command=lambda: self.refresh_account_info(manual_refresh=True),
            border_width=1,
            border_color="#333333"
        )
        refresh_button.pack(side="bottom", pady=(6, 0))

        # 切换账号按钮 - 设计成方形专业按钮
        self.reset_button = OptimizedFishButton(
            account_frame,  # 父容器
            width=account_frame.winfo_width() - 20 if account_frame.winfo_width() > 0 else 330,  # 动态设置宽度，预留边距
            height=36,
            text="切换账号",
            command=self.update_action
        )
        self.reset_button.pack(side="bottom", pady=(5, 15), padx=10, fill="x")  # 添加fill="x"使按钮水平填充
        
        # 在窗口加载完成后更新按钮尺寸
        self.root.after(100, lambda: self.update_button_size(account_frame))

        metrics = [
            ("当前cursor账号pro试用剩余天数", 0, "0/0 天"),
            ("高级访问用量", 0, "0/0 次"),
            ("4omini用量", 0, "0/0 次")
        ]
        
        # 保存进度条容器引用
        self.metric_containers = []
        
        for title, value, text in metrics:
            container = ctk.CTkFrame(account_frame, fg_color="transparent")
            container.pack(fill="x", padx=15, pady=(3, 10))  # 减小底部间距
            
            header = ctk.CTkFrame(container, fg_color="transparent")
            header.pack(fill="x")
            
            # 为"高级访问用量"添加备注说明
            if title == "高级访问用量":
                title_frame = ctk.CTkFrame(header, fg_color="transparent")
                title_frame.pack(side="left")
                
                ctk.CTkLabel(
                    title_frame,
                    text=title,
                    font=("Microsoft YaHei UI", 13),  # 改用微软雅黑，调小字号
                    text_color="#CCCCCC"  # 改为浅灰色
                ).pack(side="left")
                
                # 在标题右侧添加备注
                ctk.CTkLabel(
                    title_frame,
                    text=" (用一个加一个/新号为0)",
                    text_color="#777777",  # 更浅的灰色
                    font=("Microsoft YaHei UI", 10)  # 更小的字号
                ).pack(side="left", padx=(5, 0))
            else:
                ctk.CTkLabel(
                    header,
                    text=title,
                    font=("Microsoft YaHei UI", 13),  # 改用微软雅黑，调小字号
                    text_color="#CCCCCC"  # 改为浅灰色
                ).pack(side="left")
            
            text_label = ctk.CTkLabel(
                header,
                text=text,
                text_color="#999999",  # 改为中灰色
                font=("Microsoft YaHei UI", 12)  # 改用微软雅黑，调小字号
            )
            text_label.pack(side="right")
            
            progress = ctk.CTkProgressBar(
                container,
                height=8,
                corner_radius=6,
                fg_color="#0A0A0A",
                progress_color=self.accent_color,
                border_color="#333333"  # 改为深灰色边框
            )
            progress.pack(fill="x", pady=(8, 9))
            progress.set(value)
            
            self.metric_containers.append((title, progress, text_label, container))
        
        # 添加无限制状态显示框 (参考公告栏实现) - 初始隐藏
        self.unlimited_status_frame = ctk.CTkFrame(
            account_frame,
            fg_color="#0D4A1F",  # 深绿色背景
            height=40,  # 参考公告栏的初始高度
            corner_radius=6
        )
        self.unlimited_status_frame.pack(fill="x", pady=(5, 0), padx=12)

        # 使用与公告栏相同的方式创建标签
        self.unlimited_status_label = ctk.CTkLabel(
            self.unlimited_status_frame,
            text="已取消次数计费，改为无限制使用，正确使用方法：右侧账号来回切换即可实现无限使用3.7等模型。举例：右侧有五个账号，今天被限制了，睡一觉起来，轮着切一圈，又能用一天。右侧无账号或全不可用再点最下方切换",
            font=("Microsoft YaHei UI", 12),  # 与公告栏相同的字体大小
            text_color="#4CAF50",  # 绿色文字
            wraplength=320,  # 与公告栏相同的换行宽度
            justify="left"  # 左对齐
        )
        self.unlimited_status_label.pack(pady=8)  # 与公告栏相同的内边距

        # 初始状态下隐藏无限制提示
        self.unlimited_status_frame.pack_forget()

    def create_action_section(self):
        """移除原有的重置按钮区域，因为已经移到账号信息区域了"""
        pass

    def verify_button_click(self):
        """验证按钮点击处理"""
        # 在主线程中获取API key
        api_key = self.api_entry.get().strip()
        if not api_key:
            # 清空配置文件中的API key
            self.config['api_key'] = ''
            self.save_config()
            messagebox.showerror("错误", "请输入API密钥")
            return
        # 启动验证线程
        threading.Thread(target=lambda: self.verify_api_key(api_key)).start()

    def verify_api_key(self, api_key):
        """验证API密钥"""
        logger.info("=== 开始验证API密钥 ===")
        logger.info(f"API Key前缀: {api_key[:5]}...")
        
        # 先重置所有状态为初始状态
        def reset_status():
            # 重置配额显示
            logger.info("重置状态显示为验证中...")
            self.update_indicator("总账号数", "0")
            self.update_indicator("已使用", "0")
            self.update_indicator("剩余", "0")
            # 重置状态显示
            self.update_indicator("状态", "验证中...", "#FFA500")  # 使用橙色表示处理中
            self.update_indicator("剩余天数", "验证中...", "#FFA500")
            self.update_indicator("过期时间", "验证中...", "#FFA500")
        self.root.after(0, reset_status)
        
        try:
            # 如果开启了保存API Key，无论验证是否成功都保存
            if self.config.get('save_api_key', True):
                logger.info("保存API Key到配置文件")
                self.config['api_key'] = api_key
                self.save_config()
            
            self.api_key = api_key
            
            # 基本检查：API Key是否包含非ASCII字符或空格
            if not all(ord(c) < 128 for c in api_key) or ' ' in api_key:
                invalid_char_type = "非ASCII字符" if not all(ord(c) < 128 for c in api_key) else "空格"
                logger.error(f"API Key包含非法字符: {invalid_char_type}")
                def show_invalid_char_error():
                    self.update_indicator("状态", "验证失败", "#FF3B30")
                    messagebox.showerror("API Key错误", 
                        "API Key包含非法字符（如中文、空格或表情符号）。\n\n"
                        "请确保复制完整的API Key，不要包含任何多余字符。")
                self.root.after(0, show_invalid_char_error)
                return
            
            # 优先使用上次成功的服务器URL
            from update_cursor_token_main import Config
            server_url = Config.LAST_SUCCESSFUL_URL if Config.LAST_SUCCESSFUL_URL else "https://api2.naoy.me"
            backup_url = "https://api2.naoy.me" if server_url == "https://api2.naoy.me" else "https://api2.naoy.me"
            
            try:
                logger.info(f"尝试使用服务器: {server_url}...")
                self.account_checker = AccountChecker(server_url, api_key)
                # 测试连接
                auth_status = self.account_checker.check_auth_status()
                if not auth_status.is_valid:
                    raise Exception(f"服务器 {server_url} 连接失败")
                logger.info(f"服务器 {server_url} 连接成功")
                # 记录成功的服务器URL
                Config.LAST_SUCCESSFUL_URL = server_url
                logger.info(f"记录成功连接的服务器URL: {Config.LAST_SUCCESSFUL_URL}")
            except Exception as e:
                logger.warning(f"服务器 {server_url} 连接失败: {str(e)}，尝试使用备用服务器 {backup_url}")
                self.account_checker = AccountChecker(backup_url, api_key)
                logger.info(f"已创建备用服务器 {backup_url} AccountChecker实例")
                # 测试备用服务器连接
                auth_status = self.account_checker.check_auth_status()
                if auth_status.is_valid:
                    # 如果备用服务器连接成功，记录它
                    Config.LAST_SUCCESSFUL_URL = backup_url
                    logger.info(f"备用服务器连接成功，记录URL: {Config.LAST_SUCCESSFUL_URL}")
            
            # 获取认证状态
            logger.info("开始检查认证状态...")
            auth_status = self.account_checker.check_auth_status()
            logger.info(f"认证状态: {auth_status.is_valid}, 消息: {auth_status.message}")
            
            def update_verification_ui():
                logger.info("开始更新验证UI...")
                if not auth_status.is_valid:
                    logger.warning("API Key无效")
                    
                    # 不论什么原因导致验证失败，都尝试获取配额信息以显示更多详细信息
                    logger.info("尝试获取配额信息以显示过期时间和使用情况...")
                    try:
                        quota = self.account_checker.get_quota_info()
                        if quota:
                            logger.info(f"验证失败情况下获取配额信息成功: 总账号数={quota.total}, 已使用={quota.used}, 剩余={quota.remaining}")
                            logger.info(f"过期时间: {quota.expires_at}")
                            
                            # 更新配额信息显示
                            self.update_indicator("总账号数", str(quota.total))
                            self.update_indicator("已使用", str(quota.used))
                            self.update_indicator("剩余", str(quota.remaining))
                            
                            # 更新过期时间信息
                            try:
                                self.update_quota_display(quota, auth_status)
                                logger.info("验证失败情况下更新配额显示成功")
                                
                                # 如果是API密钥过期问题，更新状态显示
                                if "过期" in auth_status.message or hasattr(quota, 'expires_at') and quota.expires_at:
                                    try:
                                        expires_date = datetime.fromisoformat(quota.expires_at.replace('Z', '+00:00'))
                                        now = datetime.now(timezone.utc)
                                        if expires_date < now:
                                            self.update_indicator("状态", "已过期", "#FF9500")  # 使用橙色表示过期状态
                                            logger.warning("API Key已过期")
                                            messagebox.showwarning("API Key已过期", "当前API Key已过期，请更换有效的API Key。")
                                            return
                                    except Exception as e:
                                        logger.error(f"检查过期时间出错: {e}")
                                
                                # 检查是否是配额用完的情况
                                if "配额已用完" in auth_status.message or quota.remaining <= 0:
                                    self.update_indicator("状态", "账号已用完", "#FF9500")  # 使用橙色表示可恢复的错误
                                    logger.warning(f"API Key账号切换次数已用完: {auth_status.message}")
                                    return
                                
                            except Exception as e:
                                logger.error(f"验证失败情况下更新配额显示失败: {e}")
                                self.update_indicator("剩余天数", "获取失败", "#FF9500")
                                self.update_indicator("过期时间", "获取失败", "#FF9500")
                        else:
                            # 如果获取配额信息也失败，则显示验证失败
                            logger.warning("验证失败且无法获取配额信息")
                            self.update_indicator("总账号数", "0")
                            self.update_indicator("已使用", "0")
                            self.update_indicator("剩余", "0")
                            self.update_indicator("剩余天数", "无效")
                            self.update_indicator("过期时间", "无效")
                            self.update_indicator("状态", "验证失败", "#FF3B30")  # 红色表示严重错误
                            messagebox.showerror("错误", auth_status.message)
                    except Exception as e:
                        logger.error(f"验证失败情况下获取配额信息发生异常: {e}")
                        self.update_indicator("总账号数", "0")
                        self.update_indicator("已使用", "0")
                        self.update_indicator("剩余", "0")
                        self.update_indicator("剩余天数", "无效")
                        self.update_indicator("过期时间", "无效")
                        self.update_indicator("状态", "验证失败", "#FF3B30")
                        messagebox.showerror("错误", auth_status.message)
                    return

                # 获取配额信息
                logger.info("API Key有效，开始获取配额信息...")
                quota = self.account_checker.get_quota_info()
                if quota:
                    logger.info(f"配额信息获取成功: 总账号数={quota.total}, 已使用={quota.used}, 剩余={quota.remaining}")
                    logger.info(f"过期时间: {quota.expires_at}")
                    try:
                        self.update_quota_display(quota, auth_status)
                        self.update_indicator("状态", "验证成功", self.success_color)
                        logger.info("验证成功，UI已更新")
                        
                        # API验证成功后自动展开侧边栏，并在UI加载完成后自动检查状态
                        if not self.side_panel.expanded:
                            logger.info("API验证成功，自动展开侧边栏")
                            self.side_panel.toggle_panel()
                        else:
                            # 如果侧边栏已经展开，刷新账号列表并自动检查状态
                            logger.info("API验证成功，刷新侧边栏账号列表")
                            self.side_panel.load_recent_accounts()
                            # 延迟1.5秒后自动开始状态检查，让UI先完全加载
                            self.root.after(1500, self.side_panel.auto_start_background_status_check)
                    except Exception as e:
                        logger.error(f"更新配额显示时发生错误: {str(e)}")
                        logger.error(traceback.format_exc())
                        self.update_indicator("状态", "显示错误", "#FF3B30")
                        messagebox.showerror("错误", f"更新显示时发生错误: {str(e)}")
                else:
                    # 验证失败时重置所有显示
                    logger.warning("配额信息获取失败")
                    self.update_indicator("总账号数", "0")
                    self.update_indicator("已使用", "0")
                    self.update_indicator("剩余", "0")
                    self.update_indicator("剩余天数", "未知")
                    self.update_indicator("过期时间", "未知")
                    self.update_indicator("状态", "验证失败", "#FF3B30")
                    messagebox.showerror("错误", "API密钥验证失败")
            
            # 安全地在主线程中执行GUI更新
            self.root.after(0, update_verification_ui)
            
        except Exception as e:
            logger.error(f"验证过程中发生未捕获的异常: {str(e)}")
            logger.error(traceback.format_exc())
            
            def show_error():
                self.update_indicator("状态", "验证错误", "#FF3B30")
                self.update_indicator("剩余天数", "错误")
                self.update_indicator("过期时间", "错误")
                messagebox.showerror("错误", f"验证过程中发生错误: {str(e)}")
            
            self.root.after(0, show_error)

    def update_quota_display(self, quota, auth_status=None):
        """更新配额显示"""
        logger.info("=== 开始更新配额显示 ===")
        
        # 更新基本配额信息
        self.update_indicator("总账号数", str(quota.total))
        self.update_indicator("已使用", str(quota.used))
        self.update_indicator("剩余", str(quota.remaining))
        
        # 添加有效期和过期时间显示
        try:
            # 打印调试信息
            logger.info(f"quota.expires_at: {quota.expires_at}")
            
            # 如果auth_status存在则记录，否则跳过
            if auth_status:
                logger.info(f"auth_status.expires_at: {auth_status.expires_at}")
                # 优先使用quota中的expires_at
                expires_at = quota.expires_at or auth_status.expires_at
            else:
                logger.info("auth_status不存在，仅使用quota.expires_at")
                expires_at = quota.expires_at
            
            logger.info(f"最终使用的expires_at: {expires_at}")
            
            if not expires_at:
                logger.info("状态: 获取失败")
                self.update_indicator("剩余天数", "获取失败", "#FF3B30")
                self.update_indicator("过期时间", "获取失败", "#FF3B30")
                self.update_indicator("状态", "获取失败", "#FF3B30")
                return
            
            # 将UTC时间转换为中国时区
            try:
                # 处理不同格式的时间字符串
                if expires_at.endswith('Z'):
                    expires_at = expires_at[:-1] + '+00:00'
                
                logger.info(f"处理后的expires_at: {expires_at}")
                
                # 尝试解析时间字符串
                try:
                    expires_date = datetime.fromisoformat(expires_at)
                    logger.info(f"成功解析时间字符串: {expires_date}")
                except ValueError as e:
                    logger.error(f"无法解析时间字符串: {e}")
                    # 尝试其他格式
                    if '+' in expires_at:
                        # 尝试去掉时区信息
                        expires_date = datetime.fromisoformat(expires_at.split('+')[0])
                        logger.info(f"使用替代方法解析时间: {expires_date}")
                    else:
                        raise
                
                # 确保时区信息存在
                if expires_date.tzinfo is None:
                    logger.info("时间对象没有时区信息，添加UTC时区")
                    expires_date = expires_date.replace(tzinfo=timezone.utc)
                
                now = datetime.now(timezone.utc)
                logger.info(f"当前UTC时间: {now}")
                
                # 转换为中国时区
                china_expires_date = utc_to_china_timezone(expires_date)
                china_now = utc_to_china_timezone(now)
                
                logger.info(f"中国时区过期时间: {china_expires_date}")
                logger.info(f"中国时区当前时间: {china_now}")
                
                # 检查是否是2999年（永久有效）
                if expires_date.year == 2999:
                    logger.info("状态: 永久有效")
                    self.update_indicator("剩余天数", "永久", self.success_color)
                    self.update_indicator("过期时间", "永久有效", self.success_color)
                    self.update_indicator("状态", "正常", self.success_color)
                    return
                
                diff_days = (expires_date - now).days
                diff_hours = ((expires_date - now).seconds // 3600)
                
                logger.info(f"UTC过期时间: {expires_date}")
                logger.info(f"中国时区过期时间: {china_expires_date.strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info(f"UTC当前时间: {now}")
                logger.info(f"中国时区当前时间: {china_now.strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info(f"相差天数: {diff_days}")
                logger.info(f"相差小时: {diff_hours}")
                
                # 更新过期时间显示
                if diff_days < 0:
                    logger.info("状态: 已过期")
                    self.update_indicator("剩余天数", "已过期", "#FF3B30")
                    self.update_indicator("过期时间", f"{china_expires_date.strftime('%Y-%m-%d %H:%M:%S')} (已过期)", "#FF3B30")
                    self.update_indicator("状态", "已过期", "#FF3B30")
                elif diff_days == 0:
                    logger.info(f"状态: 即将过期 ({diff_hours}小时)")
                    self.update_indicator("剩余天数", f"{diff_hours}小时", "#FF9500")
                    self.update_indicator("过期时间", f"{china_expires_date.strftime('%Y-%m-%d %H:%M:%S')}", "#FF9500")
                    self.update_indicator("状态", "即将过期", "#FF9500")
                else:
                    logger.info(f"状态: 正常 ({diff_days}天)")
                    # 检查是否有额外的小时或分钟
                    diff_seconds = (expires_date - now).seconds
                    if diff_seconds > 0:
                        # 有额外时间，显示天数+
                        self.update_indicator("剩余天数", f"{diff_days}天+", self.success_color)
                    else:
                        # 没有额外时间，只显示天数
                        self.update_indicator("剩余天数", f"{diff_days}天", self.success_color)
                    self.update_indicator("过期时间", f"{china_expires_date.strftime('%Y-%m-%d %H:%M:%S')}", self.success_color)
                    self.update_indicator("状态", "正常", self.success_color)
            except Exception as e:
                logger.error(f"处理时间时发生错误: {str(e)}")
                logger.error(traceback.format_exc())
                self.update_indicator("剩余天数", "解析错误", "#FF3B30")
                self.update_indicator("过期时间", "解析错误", "#FF3B30")
                self.update_indicator("状态", "解析错误", "#FF3B30")
        except Exception as e:
            logger.error(f"更新配额显示时发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            self.update_indicator("剩余天数", "错误", "#FF3B30")
            self.update_indicator("过期时间", "错误", "#FF3B30")
            self.update_indicator("状态", "错误", "#FF3B30")

    def update_indicator(self, title, value, color=None):
        """更新指标显示，可选择更新颜色"""
        for indicator in self.indicators:
            if indicator[0] == title:
                indicator[1].configure(text=value)
                if color:  # 如果提供了颜色参数，就更新颜色
                    indicator[1].configure(text_color=color)
                break

    def update_progress(self, title, value, text):
        """更新进度条和文本"""
        for container in self.metric_containers:
            if container[0] == title:
                container[1].set(value)  # 更新进度条
                container[2].configure(text=text)  # 更新文本
                break
    
    def hide_usage_metrics(self):
        """隐藏高级访问用量和4omini用量的进度条"""
        for title, progress, text_label, container in self.metric_containers:
            if title in ["高级访问用量", "4omini用量"]:
                container.pack_forget()
    
    def show_usage_metrics(self):
        """显示高级访问用量和4omini用量的进度条"""
        for title, progress, text_label, container in self.metric_containers:
            if title in ["高级访问用量", "4omini用量"]:
                container.pack(fill="x", padx=15, pady=(3, 10))
    
    def show_unlimited_status(self):
        """显示无限制状态提示"""
        self.unlimited_status_frame.pack(fill="x", pady=(5, 0), padx=12)
        # 显示后更新窗口高度以适应内容
        self.root.after(100, self.force_update_height_for_unlimited_status)

    def hide_unlimited_status(self):
        """隐藏无限制状态提示"""
        self.unlimited_status_frame.pack_forget()
        # 隐藏后更新窗口高度
        self.root.after(100, self.force_update_height_for_unlimited_status)

    def force_update_height_for_unlimited_status(self):
        """强制重新计算并更新窗口高度，适用于无限制状态显示变化"""
        logger.info("强制重新计算窗口高度（无限制状态变化）")
        # 强制更新无限制状态标签布局
        if hasattr(self, 'unlimited_status_label'):
            self.unlimited_status_label.update()
        if hasattr(self, 'unlimited_status_frame'):
            self.unlimited_status_frame.update()
        # 清除高度缓存，强制重新计算
        self.cached_required_height = 0
        if hasattr(self, 'cached_content_key'):
            self.cached_content_key = ""
        # 更新窗口高度
        self.update_window_height()

    def update_action(self, saved_token=None, from_sidebar=False):
        """切换账号（正式版）"""
        if not self.api_key:
            messagebox.showerror("错误", "请先验证API密钥")
            return

        # 如果是来自侧边栏的切换，直接执行切换操作，不显示确认对话框
        if from_sidebar:
            self._proceed_with_update(None, saved_token)
        else:
            # 显示二次确认弹窗
            self._show_switch_confirmation_dialog(saved_token)
    
    def _show_switch_confirmation_dialog(self, saved_token=None):
        """显示切换账号确认对话框"""
        confirm_dialog = ctk.CTkToplevel(self.root)
        confirm_dialog.title("切换账号确认")
        confirm_dialog.geometry("480x280")
        confirm_dialog.transient(self.root)
        confirm_dialog.resizable(False, False)
        confirm_dialog.grab_set()  # 模态对话框
        
        # 居中显示
        confirm_dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + (self.root.winfo_width() - 480) // 2,
            self.root.winfo_rooty() + (self.root.winfo_height() - 280) // 2
        ))
        
        # 主框架
        main_frame = ctk.CTkFrame(confirm_dialog, fg_color="#2A2A2A")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 提示文字容器
        message_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        message_frame.pack(pady=(20, 30))
        
        # 第一行文字
        line1_frame = ctk.CTkFrame(message_frame, fg_color="transparent")
        line1_frame.pack()
        
        ctk.CTkLabel(
            line1_frame,
            text="如",
            font=("Microsoft YaHei UI", 13),
            text_color="#CCCCCC"
        ).pack(side="left")
        
        ctk.CTkLabel(
            line1_frame,
            text="侧边栏",
            font=("Microsoft YaHei UI", 13),
            text_color="#FF4444"  # 红色
        ).pack(side="left")
        
        ctk.CTkLabel(
            line1_frame,
            text="有可切换历史账号，建议优先切换历史账号，否则浪费账号。",
            font=("Microsoft YaHei UI", 13),
            text_color="#CCCCCC"
        ).pack(side="left")
        
        # 第二行文字
        ctk.CTkLabel(
            message_frame,
            text="如需继续切换，请点击继续切换。",
            font=("Microsoft YaHei UI", 13),
            text_color="#CCCCCC"
        ).pack(pady=(5, 15))
        
        # 第三行文字（红色暗一点）
        ctk.CTkLabel(
            message_frame,
            text="之前提示限制的账号，一定时间后切回去还可使用。",
            font=("Microsoft YaHei UI", 13),
            text_color="#CC6666"  # 暗红色
        ).pack(pady=(0, 3))
        
        # 第四行文字（红色暗一点）
        ctk.CTkLabel(
            message_frame,
            text="可以通几个账号过来回切换实现无限制使用3.7，2.5pro等模型。",
            font=("Microsoft YaHei UI", 13),
            text_color="#CC6666"  # 暗红色
        ).pack()
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=(0, 20))
        
        # 取消切换按钮（小一点）
        cancel_button = ctk.CTkButton(
            button_frame,
            text="取消切换",
            width=90,
            height=30,
            font=("Microsoft YaHei UI", 11),
            fg_color="#666666",
            hover_color="#777777",
            command=confirm_dialog.destroy
        )
        cancel_button.pack(side="left", padx=(30, 10))
        
        # 继续切换按钮（大一点）
        continue_button = ctk.CTkButton(
            button_frame,
            text="继续切换",
            width=160,
            height=42,
            font=("Microsoft YaHei UI", 16, "bold"),
            fg_color="#4CAF50",
            hover_color="#45a049",
            command=lambda: self._proceed_with_update(confirm_dialog, saved_token)
        )
        continue_button.pack(side="right", padx=(10, 30))
        
        # 设置焦点到继续按钮
        continue_button.focus()
        
    def _proceed_with_update(self, confirm_dialog, saved_token=None):
        """确认后继续执行切换操作"""
        # 关闭确认对话框（如果存在）
        if confirm_dialog is not None:
            confirm_dialog.destroy()

        # 确保Config.CURSOR_PATH在所有步骤开始前就被正确设置
        try:
            from update_cursor_token_main import Config as TokenConfig
            if not TokenConfig.CURSOR_PATH and hasattr(self, 'config'):
                cursor_path_from_config = self.config.get('cursor_path')
                if cursor_path_from_config:
                    TokenConfig.CURSOR_PATH = cursor_path_from_config
                    logger.info(f"在账号切换开始前设置Cursor路径: {TokenConfig.CURSOR_PATH}")
                else:
                    logger.warning("主程序配置中没有Cursor路径")
        except Exception as path_error:
            logger.error(f"设置Cursor路径时出错: {path_error}")

        # --- 开始：添加每日限额检查 ---
        try:
            logger.info("检查每日使用限额...")
            # 确保 account_checker 已初始化 (API Key已验证)
            if hasattr(self, 'account_checker') and self.account_checker is not None:
                quota = self.account_checker.get_quota_info()
                if quota:
                    # 首先检查API Key是否已过期 - 无论是本地账号还是新账号，过期的API Key都不允许使用
                    if quota.expires_at:
                        try:
                            expires_date = datetime.fromisoformat(quota.expires_at.replace('Z', '+00:00'))
                            now = datetime.now(timezone.utc)
                            
                            if expires_date < now:
                                # API Key已过期，阻止切换操作
                                error_message = "当前API Key已过期，无法使用账号切换功能。\n\n请使用有效的API Key后重试。"
                                logger.error(error_message)
                                messagebox.showerror("API Key已过期", error_message)
                                return # 终止切换流程
                        except (ValueError, TypeError) as e:
                            # 日期格式解析错误，记录日志但继续检查其他条件
                            logger.error(f"解析API Key过期时间出错: {e}")
                    
                    # 检查是否是本地账号切换
                    if saved_token is None:
                        # 非本地账号切换，需要检查剩余总配额
                        # 检查剩余总配额
                        if quota.remaining <= 0:
                            # 剩余配额为0，提示用户并终止操作
                            limit_message = f"当前API Key的可切换账号已用完\n\n总共可切换: {quota.total}个账号\n已使用: {quota.used}个\n\n请更换API Key或联系客服续费。"
                            logger.warning(limit_message)
                            messagebox.showerror("账号用尽", limit_message)
                            # 直接返回，终止切换流程
                            return # 终止切换流程
                            
                        # 检查每日限额
                        if quota.today_usage >= quota.daily_usage_limit:
                            # 修改提示信息，加入今日已获取的数量
                            limit_message = f"已到达每日获取账号 【{quota.daily_usage_limit}】 个上限，今日已获取 【{quota.today_usage}】 个账号，如有疑问请联系客服。"
                            logger.warning(limit_message)
                            return # 终止切换流程
                        else:
                            logger.info(f"每日限额检查通过：已使用 {quota.today_usage} / {quota.daily_usage_limit}")
                    else:
                        # 本地账号切换，不需要再次检查API Key是否过期，因为已经在前面统一检查过了
                        logger.info("使用本地账号切换，跳过配额检查")
                else:
                    # 无法获取配额，可能是网络问题或 API key 刚失效
                    logger.warning("无法获取配额信息以检查每日限额。将继续尝试切换（后续步骤会再次验证）。")
            else:
                # 如果 account_checker 未初始化，说明 API Key 未验证或验证失败
                logger.warning("API Key 未验证或验证失败，无法检查每日限额。将继续尝试切换（后续步骤会再次验证）。")
        except Exception as e:
            # 即使检查出错，也尝试继续执行，但给出警告
            logger.error(f"检查每日限额时发生错误: {e}", exc_info=True)

        original_reset_button_text = "切换账号" # 保存原始按钮文本
        try:
            # 禁用按钮并更改文字
            try:
                # 使用正确的方法设置按钮文本和状态
                self.reset_button.configure(fg_color="#888888")  # 灰色表示禁用
                self.reset_button.update_text("操作中...")
                self.reset_button._state = "disabled"
                # 强制刷新按钮
                self.reset_button.update()
            except Exception as btn_err:
                logger.error(f"更新按钮状态失败: {btn_err}")

            # --- 原有的切换逻辑 ---
            def execute_update():
                nonlocal original_reset_button_text # Ensure we can access the outer scope variable
                try:
                    # 添加详细的系统信息日志
                    logger.info("=== 开始执行账号切换操作 ===")
                    logger.info(f"操作系统: {platform.system()} {platform.release()}")
                    logger.info(f"Python版本: {sys.version}")
                    logger.info(f"工作目录: {os.getcwd()}")
                    
                    # 确保日志目录存在
                    log_dir = os.path.join(os.path.expanduser("~"), "CursorProfessionalLogs")
                    os.makedirs(log_dir, exist_ok=True)
                    error_log = os.path.join(os.path.expanduser("~"), "cursor_error.log")
                    
                    # 记录日志配置信息
                    logger.info("=== 日志配置信息 ===")
                    logger.info(f"日志目录: {log_dir}")
                    logger.info(f"错误日志: {error_log}")
                    
                    # 第一步：获取Cursor应用路径并检查文件
                    logger.info("\n=== 步骤1: 检查Cursor文件 ===")
                    try:
                        pkg_path, main_path = FilePathManager.get_cursor_app_paths()
                        logger.info(f"Cursor包路径: {pkg_path}")
                        logger.info(f"Cursor主文件路径: {main_path}")

                        if not os.path.exists(pkg_path):
                            error_msg = f"包文件不存在: {pkg_path}"
                            logger.error(error_msg)
                            raise FileNotFoundError(error_msg)

                        if not os.path.exists(main_path):
                            error_msg = f"主文件不存在: {main_path}"
                            logger.error(error_msg)
                            raise FileNotFoundError(error_msg)

                        if not Utils.check_files_exist(pkg_path, main_path):
                            error_msg = "Cursor文件检查失败"
                            logger.error(error_msg)
                            raise FileNotFoundError(error_msg)

                    except Exception as e:
                        logger.error(f"获取Cursor路径时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"获取Cursor路径失败: {e}\n")
                            f.write(traceback.format_exc())
                        error_msg = f"获取Cursor路径失败: {e}"
                        self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", msg))
                        return

                    # 在关闭Cursor前保存工作区信息
                    logger.info("\n=== 步骤2: 保存Cursor工作区信息 ===")
                    try:
                        logger.info("正在保存Cursor工作区信息...")
                        self.root.after(0, lambda: self.update_status("正在保存工作区信息..."))
                        save_result = self.save_cursor_workspaces()
                        if save_result:
                            logger.info("成功保存Cursor工作区信息")
                        else:
                            logger.warning("保存Cursor工作区信息失败或无需保存")
                    except Exception as e:
                        logger.error(f"保存Cursor工作区信息时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 记录但不中断流程
                        logger.warning("继续执行后续步骤...")

                    # 第三步：安全退出Cursor
                    logger.info("\n=== 步骤3: 终止Cursor进程 ===")
                    try:
                        logger.info("正在终止Cursor进程...")
                        self.root.after(0, lambda: self.update_status("正在终止Cursor进程..."))
                        
                        # 尝试多次终止进程
                        max_attempts = 3
                        success = False
                        
                        for attempt in range(max_attempts):
                            try:
                                logger.info(f"尝试终止进程 (尝试 {attempt + 1}/{max_attempts})...")
                                exit_result = self.cursor_manager.exit_cursor()
                                
                                if exit_result:
                                    logger.info("成功终止Cursor进程")
                                    success = True
                                    break
                                else:
                                    logger.warning(f"第 {attempt + 1} 次尝试未能完全终止进程")
                                    if attempt < max_attempts - 1:
                                        logger.info("等待2秒后重试...")
                                        time.sleep(2)
                            except Exception as e:
                                logger.error(f"第 {attempt + 1} 次尝试时出错: {e}")
                                if attempt < max_attempts - 1:
                                    logger.info("等待2秒后重试...")
                                    time.sleep(2)
                                else:
                                    raise
                        
                        if not success:
                            error_msg = "无法终止Cursor应用程序实例"
                            logger.error(error_msg)
                            raise RuntimeError(error_msg)
                            
                    except Exception as e:
                        logger.error(f"终止Cursor进程时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"终止Cursor进程失败: {e}\n")
                            f.write(traceback.format_exc())
                        self.root.after(0, lambda: messagebox.showerror("错误", f"终止Cursor进程失败: {e}"))
                        return

                    # 第四步：检查权限
                    logger.info("\n=== 步骤4: 检查权限 ===")
                    try:
                        logger.info("正在检查系统权限...")
                        self.root.after(0, lambda: self.update_status("正在检查系统权限..."))
                        if not self.check_permissions():
                            error_msg = "权限检查未通过，请打开系统设置-隐私与安全性-app管理-允许授权"
                            logger.error(error_msg)
                            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                            return
                    except Exception as e:
                        logger.error(f"权限检查时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        self.root.after(0, lambda: messagebox.showerror("错误", f"权限检查失败: {e}"))
                        return

                    # 第五步：验证API key并获取配额信息
                    logger.info("\n=== 步骤5: 验证API密钥 ===")
                    try:
                        logger.info("正在验证API密钥...")
                        self.root.after(0, lambda: self.update_status("正在验证API密钥..."))
                        quota = self.account_checker.get_quota_info()
                        if not quota:
                            error_msg = "API密钥验证失败"
                            logger.error(error_msg)
                            raise ValueError(error_msg)
                        
                        # 验证配额是否用尽，仅在非本地账号切换时检查
                        if quota.remaining <= 0 and saved_token is None:
                            error_msg = f"当前API Key的可切换账号已用完\n\n总共可切换: {quota.total}个账号\n已使用: {quota.used}个\n\n请更换API Key或联系客服续费。"
                            logger.error(error_msg)
                            self.root.after(0, lambda: messagebox.showerror("账号用尽", error_msg))
                            return
                        elif quota.remaining <= 0 and saved_token is not None:
                            # 使用本地账号且配额用尽时，只记录日志但不中断流程
                            logger.warning(f"当前API Key配额已用尽，但正在使用本地账号，允许继续操作")
                        
                        logger.info(f"API密钥验证成功，配额信息: {quota}")
                        # 更新配额显示
                        self.root.after(0, lambda: self.update_quota_display(quota))
                    except Exception as e:
                        logger.error(f"验证API密钥时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"验证API密钥失败: {e}\n")
                            f.write(traceback.format_exc())
                        self.root.after(0, lambda: messagebox.showerror("错误", f"验证API密钥失败: {e}"))
                        return

                    # 第六步：获取或使用Token数据
                    logger.info("\n=== 步骤6: 获取Token数据 ===")
                    try:
                        logger.info("正在处理Token数据...")
                        self.root.after(0, lambda: self.update_status("正在处理Token数据..."))
                        
                        if saved_token:
                            logger.info("使用已保存的Token数据")
                            token_data = saved_token
                        else:
                            logger.info("从服务器获取新的Token数据")
                            token_data = TokenManager.fetch_token_data(self.api_key)
                            
                        if not token_data:
                            # 获取保存的错误信息
                            error_msg = "获取Token数据失败"
                            if hasattr(Config, 'LAST_ERROR_MESSAGE') and Config.LAST_ERROR_MESSAGE:
                                error_msg = f"获取账号失败: {Config.LAST_ERROR_MESSAGE}"
                            logger.error(error_msg)
                            raise ValueError(error_msg)
                        
                        logger.info("Token数据处理成功")
                    except Exception as e:
                        logger.error(f"处理Token数据时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"处理Token数据失败: {e}\n")
                            f.write(traceback.format_exc())
                        self.root.after(0, lambda err=f"处理Token数据失败: {str(e)}": messagebox.showerror("错误", err))
                        return

                    # 第七步：重置机器码
                    logger.info("\n=== 步骤7: 重置机器码 ===")
                    try:
                        logger.info("正在重置机器码...")
                        self.root.after(0, lambda: self.update_status("正在重置机器码..."))
                        success = self.cursor_manager.reset_cursor_id(token_data)
                        logger.info(f"重置机器码结果: {success}")
                        
                        if not success:
                            error_msg = "重置机器码失败"
                            logger.error(error_msg)
                            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                            raise RuntimeError(error_msg)
                    except Exception as e:
                        logger.error(f"重置机器码时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"重置机器码失败: {e}\n")
                            f.write(traceback.format_exc())
                        self.root.after(0, lambda: messagebox.showerror("错误", f"重置机器码失败: {e}"))
                        return

                    # 第八步：修改 main.js 文件
                    logger.info("\n=== 步骤8: 修改 main.js 文件 ===")
                    try:
                        logger.info("正在修改 main.js 文件...")
                        self.root.after(0, lambda: self.update_status("正在修改 main.js 文件..."))
                        
                        # 获取 main.js 文件路径
                        try:
                            _, main_js_path = FilePathManager.get_cursor_app_paths()
                            
                            if main_js_path.exists():
                                # 直接调用 CursorPatcher 的静态方法修改 main.js
                                patch_success = CursorPatcher.patch_main_js(main_js_path)
                                
                                if patch_success:
                                    logger.info("main.js 文件修改成功")
                                else:
                                    logger.warning("main.js 文件修改失败，但将继续执行后续步骤")
                            else:
                                logger.warning(f"main.js 文件不存在: {main_js_path}，跳过修改")
                        except Exception as e:
                            logger.warning(f"获取 main.js 路径失败: {e}，跳过修改")
                    except Exception as e:
                        logger.error(f"修改 main.js 文件时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"修改 main.js 文件失败: {e}\n")
                            f.write(traceback.format_exc())
                        logger.warning("修改 main.js 文件失败，但将继续执行后续步骤")

                    # 第九步：修改邮箱显示逻辑（仅UI层）
                    logger.info("\n=== 步骤9: 修改邮箱显示逻辑（仅UI层） ===")
                    try:
                        logger.info("正在修改邮箱显示逻辑（仅UI层）...")
                        self.root.after(0, lambda: self.update_status("正在修改邮箱显示逻辑..."))
                        
                        # 更新product.json（绕过完整性校验）
                        from update_cursor_token_main import update_product_json, modify_settings_email_display
                        product_result = update_product_json()
                        
                        # 修改邮箱显示逻辑（仅UI层）
                        email_result = modify_settings_email_display()

                        if product_result and email_result:
                            logger.info("邮箱显示修改完成！（仅UI层）")
                        elif product_result or email_result:
                            logger.warning("邮箱显示修改部分完成")
                        else:
                            logger.warning("邮箱显示修改失败，但将继续执行后续步骤")
                            
                    except Exception as e:
                        logger.error(f"修改邮箱显示逻辑时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"修改邮箱显示逻辑失败: {e}\n")
                            f.write(traceback.format_exc())
                        logger.warning("修改邮箱显示逻辑失败，但将继续执行后续步骤")

                    # 第十步：更新认证信息
                    logger.info("\n=== 步骤10: 更新认证信息 ===")
                    try:
                        logger.info("正在同步身份凭证...")
                        self.root.after(0, lambda: self.update_status("正在同步身份凭证..."))
                        
                        # (修改点) 在更新认证信息之前，先删除 server_config
                        logger.info("正在删除旧服务器配置...")
                        if not self.cursor_auth_manager.delete_auth_key('server_config'):
                            # 仅记录警告，不中断流程
                            logger.warning("删除旧服务器配置失败，但这通常不影响主要功能")
                        else:
                            logger.info("成功删除旧服务器配置")
                            
                        # 继续更新认证信息
                        success = self.cursor_auth_manager.update_auth(
                            email=token_data.email,
                            access_token=token_data.token,
                            refresh_token=token_data.token  # 使用相同的token作为刷新凭证
                        )
                        logger.info(f"同步身份凭证结果: {success}")
                        
                        if not success:
                            error_msg = "身份凭证同步失败"
                            logger.error(error_msg)
                            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                            raise RuntimeError(error_msg)
                        
                        # 步骤10.5：清除Gmail邮箱信息（认证信息更新成功后）
                        logger.info("\n=== 步骤10.5: 清除Gmail邮箱信息 ===")
                        try:
                            logger.info("正在清除数据库中的Gmail邮箱信息...")
                            from update_cursor_token_main import clear_email_from_database
                            clear_result = clear_email_from_database()
                            if clear_result:
                                logger.info("✅ 成功清除Gmail邮箱信息")
                            else:
                                logger.warning("⚠️ 清除Gmail邮箱信息失败，但不影响主要功能")
                        except Exception as clear_error:
                            logger.error(f"清除Gmail邮箱信息时出错: {clear_error}")
                            logger.error(f"错误详情: {traceback.format_exc()}")
                            # 记录错误但不中断流程
                            logger.warning("清除Gmail邮箱信息失败，但将继续执行后续步骤")

                        # 步骤10.6：禁用Cursor自动更新
                        logger.info("\n=== 步骤10.6: 禁用Cursor自动更新 ===")
                        try:
                            logger.info("正在禁用Cursor自动更新...")
                            self.root.after(0, lambda: self.update_status("正在禁用Cursor自动更新..."))

                            # 导入自动更新管理器
                            from cursor_auto_update_manager import disable_cursor_auto_update, get_cursor_auto_update_status

                            # 先检查当前状态
                            logger.info("检查当前自动更新状态...")
                            status_info = get_cursor_auto_update_status()
                            logger.info(f"当前状态: {status_info.get('message', '未知')}")
                            logger.info(f"设置文件路径: {status_info.get('settings_path', '未知')}")

                            # 如果已经禁用，跳过操作
                            if status_info.get('status') == 'disabled':
                                logger.info("✅ Cursor自动更新已经被禁用，无需重复操作")
                            else:
                                # 执行禁用操作
                                logger.info("开始执行禁用操作...")
                                disable_result = disable_cursor_auto_update()
                                if disable_result:
                                    logger.info("✅ 成功禁用Cursor自动更新")
                                    # 再次检查状态确认
                                    final_status = get_cursor_auto_update_status()
                                    if final_status.get('status') == 'disabled':
                                        logger.info("✅ 禁用操作已确认生效")
                                    else:
                                        logger.warning("⚠️ 禁用操作可能未完全生效")
                                else:
                                    logger.warning("⚠️ 禁用Cursor自动更新失败，但不影响主要功能")

                        except Exception as disable_error:
                            logger.error(f"禁用Cursor自动更新时出错: {disable_error}")
                            logger.error(f"错误详情: {traceback.format_exc()}")
                            # 记录错误但不中断流程
                            logger.warning("禁用Cursor自动更新失败，但将继续执行后续步骤")

                        # 步骤10.7：启用隐私模式
                        logger.info("\n=== 步骤10.7: 启用隐私模式 ===")
                        try:
                            logger.info("正在为新账号启用隐私模式...")
                            self.root.after(0, lambda: self.update_status("正在启用隐私模式..."))

                            # 导入隐私模式管理器
                            from cursor_privacy_manager import enable_privacy_mode_sync

                            # 使用换号时的token启用隐私模式
                            privacy_result = enable_privacy_mode_sync(token_data.token)

                            if privacy_result.get("success"):
                                logger.info("✅ 成功启用隐私模式")
                                user_id = privacy_result.get("user_id", "未知")
                                privacy_enabled = privacy_result.get("privacy_enabled", False)
                                code_training_opt_out = privacy_result.get("code_training_opt_out", False)

                                logger.info(f"用户ID: {user_id}")
                                logger.info(f"隐私模式: {'启用' if privacy_enabled else '禁用'}")
                                logger.info(f"代码训练退出: {'是' if code_training_opt_out else '否'}")

                                # 检查状态变化
                                before = privacy_result.get("before", "")
                                after = privacy_result.get("after", "")
                                if before != after and before != "unknown":
                                    logger.info(f"隐私模式状态变化: {before} → {after}")
                                else:
                                    logger.info(f"隐私模式状态: {after}")
                            else:
                                error_msg = privacy_result.get("error", "未知错误")
                                logger.warning(f"⚠️ 启用隐私模式失败: {error_msg}")
                                logger.warning("隐私模式设置失败不会影响账号切换的主要功能")

                        except Exception as privacy_error:
                            logger.error(f"启用隐私模式时出错: {privacy_error}")
                            logger.error(f"错误详情: {traceback.format_exc()}")
                            # 记录错误但不中断流程
                            logger.warning("启用隐私模式失败，但将继续执行后续步骤")
                    except Exception as e:
                        logger.error(f"更新认证信息时出错: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 写入错误日志
                        with open(error_log, 'a', encoding='utf-8') as f:
                            f.write(f"\n=== {datetime.now()} ===\n")
                            f.write(f"更新认证信息失败: {e}\n")
                            f.write(traceback.format_exc())
                        self.root.after(0, lambda: messagebox.showerror("错误", f"更新认证信息失败: {e}"))
                        return

                    # 完成 (后续步骤保持不变)
                    if success:
                        logger.info("\n=== 操作完成 ===")
                        logger.info("系统同步完成")
                        self.root.after(0, lambda: self.update_status("身份凭证迁移完成", color=self.success_color))
                        
                        # 立即启动Cursor并恢复工作区 (移到消息框之前)
                        logger.info("准备启动Cursor并恢复工作区...")
                        try:
                            restart_result = self.start_cursor_with_saved_workspaces()
                            if not restart_result:
                                # 仅记录日志，不阻塞后续流程
                                logger.warning("Cursor启动失败，请手动启动Cursor")
                        except Exception as start_err:
                            logger.error(f"启动Cursor时发生异常: {start_err}")
                            logger.error(traceback.format_exc())
                            # 同样只记录日志
                        
                        # 创建一个函数，仅显示消息和执行后续刷新
                        def show_success_message_and_refresh():
                            # 先显示成功消息
                            # 格式化邮箱显示
                            formatted_email = self.format_email_display(token_data.email)
                            messagebox.showinfo("迁移成功", 
                                f"身份凭证迁移完成\n\n"
                                f"目标账户: {formatted_email}\n"
                                f"设备标识符已重新生成\n" 
                                f"授权凭证已同步更新\n\n"
                                f"Cursor正在后台启动并恢复工作区...", # 修改提示文本
                                parent=self.root  # 添加parent参数，确保弹窗显示在主窗口上方
                            )
                            # 移除原来的启动调用
                            
                            # 刷新身份凭证状态
                            try:
                                logger.info("正在刷新身份凭证状态...")
                                self.initialize_account()
                            except Exception as e:
                                logger.error(f"刷新身份凭证状态时出错: {e}")
                                logger.error(f"错误详情: {traceback.format_exc()}")
                                self.root.after(0, lambda: messagebox.showerror("错误", f"刷新身份凭证状态失败: {e}"))
                            
                            # 更新切换时间（如果是使用新获取的token）
                            try:
                                if not saved_token and hasattr(self, 'api_key') and self.api_key:
                                    logger.info("正在更新新账号的切换时间...")
                                    from update_cursor_token_main import TokenStorage
                                    # 获取最新保存的token记录
                                    tokens = TokenStorage.list_tokens()
                                    if tokens:
                                        # 找到当前API Key的最新记录
                                        current_api_tokens = [t for t in tokens if t.get("access_code") == self.api_key]
                                        if current_api_tokens:
                                            # 按时间戳排序，获取最新的记录
                                            latest_token = sorted(current_api_tokens,
                                                                key=lambda x: x.get("timestamp", "1970-01-01T00:00:00"),
                                                                reverse=True)[0]
                                            record_id = latest_token.get("record_id")
                                            if record_id:
                                                TokenStorage.update_last_switch_time(self.api_key, record_id)
                                                logger.info("已更新新账号的切换时间")
                                            else:
                                                logger.warning("未找到record_id，无法更新切换时间")
                                        else:
                                            logger.warning("未找到当前API Key的token记录")
                                    else:
                                        logger.warning("未找到任何token记录")
                            except Exception as update_time_err:
                                logger.error(f"更新切换时间失败: {update_time_err}")
                                # 不影响主流程，继续执行

                            # 刷新侧边栏（如果侧边栏已展开）
                            try:
                                if hasattr(self, 'side_panel') and self.side_panel and self.side_panel.expanded:
                                    logger.info("正在刷新侧边栏账号列表...")
                                    self.side_panel.load_recent_accounts()
                                    logger.info("侧边栏刷新完成")
                                    # 换号后自动检查账号状态，就像打开侧边栏时一样
                                    logger.info("主界面换号成功，将在2秒后自动开始侧边栏后台状态检查")
                                    self.root.after(2000, self.side_panel.auto_start_background_status_check)
                            except Exception as e:
                                logger.error(f"刷新侧边栏时出错: {e}")
                                # 侧边栏刷新失败不影响主流程，只记录日志
                        
                            # 同步授权配额
                            try:
                                if self.api_key:
                                    logger.info("正在同步授权配额...")
                                    quota = self.account_checker.get_quota_info()
                                    if quota:
                                        logger.info(f"同步授权配额成功: {quota}")
                                        self.root.after(0, lambda: self.update_quota_display(quota))
                                    else:
                                        # 不再弹窗，只记录日志
                                        logger.warning("获取授权配额失败 (在成功消息后)")
                            except Exception as e:
                                logger.error(f"同步授权配额时出错: {e}")
                                logger.error(f"错误详情: {traceback.format_exc()}")
                                # 不再弹窗，只记录日志
                                logger.warning(f"同步授权配额失败 (在成功消息后): {e}")

                        # 调度显示消息和刷新的函数
                        self.root.after(0, show_success_message_and_refresh)

                except Exception as e:
                    logger.critical(f"执行更新过程中发生未捕获的异常: {e}")
                    logger.critical(f"错误详情: {traceback.format_exc()}")
                    # 写入错误日志
                    with open(error_log, 'a', encoding='utf-8') as f:
                        f.write(f"\n=== {datetime.now()} ===\n")
                        f.write(f"执行过程中发生未预期的错误: {e}\n")
                        f.write(traceback.format_exc())
                    self.root.after(0, lambda: messagebox.showerror("系统错误", f"执行过程中发生未预期的错误: {e}"))
                finally:
                    # 恢复按钮状态 - 移到这里，在后台线程的 finally 中执行
                    logger.info("在 execute_update 的 finally 中尝试恢复按钮状态...")
                    # 使用 root.after 将 UI 更新调度回主线程
                    self.root.after(0, lambda: self._safe_reset_button(original_reset_button_text))

            # 在新线程中执行更新操作
            logger.info("启动新线程执行更新操作")
            threading.Thread(target=execute_update).start()

        except Exception as e:
            logger.critical(f"系统异常: {e}")
            logger.critical(f"错误详情: {traceback.format_exc()}")
            # 写入错误日志
            error_log = os.path.join(os.path.expanduser("~"), "cursor_error.log")
            with open(error_log, 'a', encoding='utf-8') as f:
                f.write(f"\n=== {datetime.now()} ===\n")
                f.write(f"系统异常: {e}\n")
                f.write(traceback.format_exc())
            messagebox.showerror("系统异常", f"执行过程中遇到意外情况: {str(e)}")
            messagebox.showwarning("警告", "系统遇到严重错误,请重启应用后重试")
            # --- 按钮状态恢复已移至 execute_update 的 finally 块 ---

        finally:
            # --- 主线程的 finally 块现在不包含按钮状态恢复逻辑 --- 
            pass # Placeholder if other finally logic is needed here

    # --- 新增辅助方法来安全地重置按钮 ---
    def _safe_reset_button(self, original_text):
        try:
            self.reset_button.configure(fg_color=self.button_color)
            self.reset_button.update_text(original_text)
            self.reset_button._state = "normal"
            self.reset_button.update()
            logger.info(f"按钮状态已恢复为 '{original_text}'")
        except Exception as btn_reset_err:
            logger.error(f"安全重置按钮状态失败: {btn_reset_err}", exc_info=True)

    def update_status(self, message: str, color: str = None):
        """更新系统状态指示器"""
        self.update_indicator("系统状态", message, color or self.font_color_light)

    async def get_account_data(self, tracker):
        """异步获取账户授权与使用数据"""
        try:
            usage_data = await tracker.get_premium_usage()
            subscription_data = await tracker.get_subscription_info()
            
            # 检查返回的数据是否包含错误信息
            if isinstance(usage_data, dict) and "error" in usage_data:
                raise Exception(f"获取使用情况失败: {usage_data['error']}")
            
            if isinstance(subscription_data, dict) and "error" in subscription_data:
                raise Exception(f"获取订阅信息失败: {subscription_data['error']}")
                
            return usage_data, subscription_data
        except Exception as e:
            raise Exception(f"账户数据同步失败: {str(e)}")

    def load_current_account(self):
        """同步当前会话的身份凭证"""
        # 获取当前线程是否是手动刷新
        is_manual_refresh = threading.current_thread().name.startswith('manual_refresh_')
        
        # 如果已暂停连接，则直接返回
        if self.cursor_connect_paused and not is_manual_refresh:
            return
            
        try:
            logger.info("=== 开始同步当前会话身份凭证 ===")
            # 获取当前token
            current_token = CursorUsageTracker.get_cursor_token()
            if not current_token:
                # 重置连接失败计数
                self.cursor_connect_fail_count = 0
                logger.warning("未找到Cursor token，可能Cursor未安装或未登录")
                # 显示手动刷新失败提示
                if is_manual_refresh:
                    self.root.after(0, lambda: self.refresh_fail_label.configure(text="(手动刷新:未找到令牌)"))
                return
                
            logger.info("开始获取账户数据...")

            # 创建追踪器实例
            tracker = CursorUsageTracker(current_token)

            # 获取usage和subscription数据
            usage_data, subscription_data = asyncio.run(self.get_account_data(tracker))

            # 获取邮箱 - 优先从API获取，如果失败则从数据库获取
            email = None
            try:
                # 首先尝试通过API获取邮箱
                user_info_result = asyncio.run(tracker.get_user_info())
                if user_info_result and user_info_result.get("success"):
                    email = user_info_result.get("email", "")
                    logger.info(f"通过API成功获取邮箱: {email}")
                else:
                    logger.warning("API获取邮箱失败，尝试从数据库获取")
                    # 如果API失败，尝试从数据库获取
                    email = CursorUsageTracker.get_cursor_email()
                    if email:
                        logger.info(f"从数据库成功获取邮箱: {email}")
                    else:
                        logger.warning("数据库也未找到邮箱信息")
            except Exception as e:
                logger.error(f"获取邮箱时出错: {e}")
                # 如果API出错，尝试从数据库获取
                email = CursorUsageTracker.get_cursor_email()
                if email:
                    logger.info(f"API出错后从数据库获取邮箱: {email}")
                else:
                    logger.warning("API和数据库都无法获取邮箱信息")
            
            def update_account_ui():
                # 更新账号显示
                if subscription_data:
                    account_type = subscription_data['subscription_type'].upper()
                    # 如果账号类型为TRIAL，则显示为PRO TRIAL
                    if account_type == "TRIAL":
                        account_type = "PRO TRIAL"
                    if email and email.strip():
                        # 格式化邮箱显示：将@icloud替换为@gmail
                        formatted_email = self.format_email_display(email)
                        # 确保格式化后的邮箱不为空
                        display_email = formatted_email if formatted_email and formatted_email.strip() else "未知账号"
                        self.account_type_label.configure(text=f"· {account_type}")
                        self.email_label.configure(text=display_email)
                    else:
                        self.account_type_label.configure(text=f"· {account_type}")
                        self.email_label.configure(text="等待授权验证")
                
                # 如果是手动刷新，清除失败提示
                if is_manual_refresh:
                    self.refresh_fail_label.configure(text="")

                # 更新资源使用数据
                if usage_data:
                    premium = usage_data['premium']
                    gpt4omini = usage_data['gpt4omini']
                    
                    # 检查是否为无限制状态
                    is_unlimited = (premium['limit'] == "无限制" or premium['limit'] == "∞")
                    
                    if is_unlimited:
                        # 隐藏使用量进度条，显示无限制状态
                        self.hide_usage_metrics()
                        self.show_unlimited_status()
                    else:
                        # 显示使用量进度条，隐藏无限制状态
                        self.hide_unlimited_status()
                        self.show_usage_metrics()
                        
                        # 更新高级功能使用指标
                        self.update_progress(
                            "高级访问用量", 
                            premium['current'] / premium['limit'] if isinstance(premium['limit'], (int, float)) and premium['limit'] != 0 else 0,
                            f"{premium['current']}/{premium['limit']} 次"
                        )
                        
                        self.update_progress(
                            "4omini用量",
                            gpt4omini['current'] / gpt4omini['limit'] if isinstance(gpt4omini['limit'], (int, float)) and gpt4omini['limit'] != 0 else 0,
                            f"{gpt4omini['current']}/{gpt4omini['limit']} 次"
                        )

                    # 评估试用期状态
                    if subscription_data.get('is_trial'):
                        days = subscription_data.get('trial_days_remaining', 0)
                        self.update_progress(
                            "当前cursor账号pro试用剩余天数",
                            days / 14,  # 标准评估周期
                            f"{days}/14 天"
                        )
            
            # 安全地在主线程中执行GUI更新
            self.root.after(0, update_account_ui)

        except Exception as e:
            error_msg = str(e)  # 在闭包外捕获错误信息
            
            # 如果是手动刷新，显示手动刷新失败提示
            if is_manual_refresh:
                self.root.after(0, lambda: self.refresh_fail_label.configure(text=f"(手动刷新)"))
                logger.warning(f"手动刷新失败: {error_msg}")
                return
            
            # 以下是自动刷新的失败处理逻辑
            # 增加失败计数
            self.cursor_connect_fail_count += 1
            logger.warning(f"Cursor连接失败计数: {self.cursor_connect_fail_count}/10")
            
            def show_error():
                # 只有当失败次数达到10次才显示错误
                if self.cursor_connect_fail_count >= 10:
                    # 不再更新状态指示器
                    # self.update_indicator("状态", "连接受限", "#FF3B30")
                    
                    # 添加自动刷新失败提示
                    self.root.after(0, lambda: self.refresh_fail_label.configure(text="(自动刷新次数)"))
                    
                    # 不再显示任何弹窗，只设置暂停连接标记
                    self.cursor_connect_paused = True
                    
                    # 重置计数器
                    self.cursor_connect_fail_count = 0
                    
            # 仅在达到阈值时更新状态指示器
            if self.cursor_connect_fail_count >= 10:
                self.root.after(0, show_error)

    def run_async_update_stats(self, tracker):
        """异步执行统计数据更新任务"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.update_usage_stats(tracker))
        finally:
            loop.close()

    def refresh_account_info(self, manual_refresh=False):
        """同步账户状态
        
        Args:
            manual_refresh: 是否为手动刷新，True为用户手动点击刷新，False为自动定时刷新
        """
        # 只有当是手动刷新时，才重置暂停状态
        if self.cursor_connect_paused and manual_refresh:
            self.cursor_connect_paused = False
            self.cursor_connect_fail_count = 0
            # 清除自动刷新失败提示
            self.refresh_fail_label.configure(text="")
            logger.info("用户手动刷新，重置连接状态，允许重新连接Cursor服务器")
        # 如果是自动刷新且连接已暂停，则直接返回不执行刷新    
        elif self.cursor_connect_paused and not manual_refresh:
            logger.info("自动刷新时发现连接已暂停，跳过刷新")
            return
        
        # 为手动刷新创建特定命名的线程，以便识别
        thread_name = f"manual_refresh_{int(time.time())}" if manual_refresh else "auto_refresh"
        thread = threading.Thread(target=self.load_current_account, name=thread_name)
        thread.start()

    def load_config(self):
        """加载配置"""
        logger.info("="*50)
        logger.info("开始加载配置")
        logger.info("-"*50)
        
        try:
            import json
            import os
            import platform
            from pathlib import Path
            
            # 根据不同操作系统选择配置文件存储路径
            system_name = platform.system()
            logger.debug(f"当前操作系统: {system_name}")
            
            if system_name == "Windows":
                config_dir = Path(os.getenv('LOCALAPPDATA')) / 'CursorPro'
            elif system_name == "Darwin":  # macOS
                config_dir = Path.home() / 'Library' / 'Application Support' / 'CursorPro'
            else:  # Linux
                config_dir = Path.home() / '.config' / 'CursorPro'
                
            config_path = config_dir / 'settings.dat'
            
            logger.info(f"配置文件路径: {config_path}")
            
            # 读取配置
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logger.info(f"已读取配置: {self.config}")
                
                # 检查版本是否需要更新
                config_version = self.config.get('current_version', '')
                if config_version != self.DEFAULT_VERSION:
                    logger.info(f"检测到版本不匹配：配置文件版本 {config_version}，当前软件版本 {self.DEFAULT_VERSION}")
                    
                    # 检查当前版本是否为灰度测试版本
                    if "灰度测试" in self.DEFAULT_VERSION:
                        logger.info(f"当前为灰度测试版本，不更新配置文件中的版本号")
                    else:
                        # 更新版本号
                        self.config['current_version'] = self.DEFAULT_VERSION
                        logger.info(f"已更新配置中的版本号为 {self.DEFAULT_VERSION}")
                        # 保存更新后的配置
                        self.save_config()
                        logger.info("版本更新后的配置已保存")
            else:
                # 创建默认配置
                logger.info("配置文件不存在，创建默认配置")
                self.config = {
                    'current_version': self.DEFAULT_VERSION,
                    'save_api_key': True,
                    'auto_refresh': True,
                    'refresh_interval': {
                        'min': 60, 
                        'max': 80
                    }
                }
                
            # 确保自动刷新设置存在
            if 'auto_refresh' not in self.config:
                self.config['auto_refresh'] = True
            # 确保API Key保存设置存在
            if 'save_api_key' not in self.config:
                self.config['save_api_key'] = True
                
            # 确保cursor_path设置存在
            if 'cursor_path' not in self.config:
                self.config['cursor_path'] = None

            # 从配置中初始化Config.CURSOR_PATH
            from update_cursor_token_main import Config as TokenConfig
            TokenConfig.CURSOR_PATH = self.config.get('cursor_path')
                
            # 对于Mac系统，检查默认位置是否存在Cursor.app
            if system_name == "Darwin" and (not TokenConfig.CURSOR_PATH or not self.verify_cursor_path(TokenConfig.CURSOR_PATH)):
                # 检查默认位置
                default_paths = [
                    Path("/Applications/Cursor.app"),
                    Path.home() / "Applications" / "Cursor.app"
                ]
                
                for default_path in default_paths:
                    if self.verify_cursor_path(str(default_path)):
                        logger.info(f"在默认路径发现有效Cursor安装: {default_path}")
                        TokenConfig.CURSOR_PATH = str(default_path)
                        self.config['cursor_path'] = str(default_path)
                        self.save_config()
                        break
                        
            logger.info(f"Config.CURSOR_PATH = {TokenConfig.CURSOR_PATH}")
            return True
            
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            logger.error(traceback.format_exc())
            self.config = {
                'current_version': self.DEFAULT_VERSION,
                'save_api_key': True,
                'auto_refresh': True,
                'refresh_interval': {
                    'min': 60, 
                    'max': 80
                }
            }
            return False

    def show_mac_permission_guide(self):
        """显示Mac系统权限指引对话框"""
        logger.info("="*50)
        logger.info("显示Mac系统权限指引对话框")
        logger.info("-"*50)
        
        try:
            import platform
            import subprocess
            
            # 确认是否为Mac系统
            if platform.system() != "Darwin":
                logger.info("非Mac系统，跳过显示权限指引")
                return
                
            # 更精确地检测是否为Intel或Apple Silicon芯片
            try:
                # 使用sysctl命令获取CPU信息
                result = subprocess.run(['sysctl', '-n', 'machdep.cpu.brand_string'], 
                                     capture_output=True, 
                                     text=True)
                cpu_info = result.stdout.strip()
                logger.info(f"CPU信息: {cpu_info}")
                
                # 检查CPU型号中是否包含Intel或AMD
                is_intel = "Intel" in cpu_info or "AMD" in cpu_info
                
                # 如果sysctl命令失败，使用platform.machine()
                if not cpu_info:
                    logger.warning("通过sysctl获取CPU信息失败，使用platform.machine()")
                    machine = platform.machine()
                    # arm64表示Apple Silicon，其他（如x86_64）表示Intel/AMD
                    is_intel = machine != "arm64"
                    logger.info(f"通过platform.machine()判断: {machine}, 是否Intel/AMD: {is_intel}")
            except Exception as e:
                logger.warning(f"获取CPU信息失败: {e}，使用备选方法")
                # 备选判断方法
                machine = platform.machine()
                is_intel = machine != "arm64"
                logger.info(f"备选方法判断处理器架构: {machine}, 是否Intel/AMD: {is_intel}")
            
            processor_info = "Intel/AMD" if is_intel else "Apple Silicon"
            logger.info(f"检测到Mac {processor_info}芯片")
            
            # 创建对话框窗口
            guide_window = ctk.CTkToplevel(self.root)
            guide_window.title("Mac权限设置指南")
            guide_window.geometry("600x450")
            guide_window.resizable(False, False)
            guide_window.configure(fg_color="#111111")
            guide_window.lift()  # 将窗口置于前台
            guide_window.attributes("-topmost", True)  # 保持在最前
            guide_window.focus_force()  # 强制获取焦点
            
            # 创建标题
            title_label = ctk.CTkLabel(
                guide_window,
                text="首次使用设置指南",
                font=("Microsoft YaHei UI", 24, "bold"),
                text_color="#FFFFFF"
            )
            title_label.pack(pady=(20, 5))
            
            # 创建子标题
            subtitle_label = ctk.CTkLabel(
                guide_window,
                text="请完成以下步骤以确保程序正常运行",
                font=("Microsoft YaHei UI", 14),
                text_color="#CCCCCC"
            )
            subtitle_label.pack(pady=(0, 20))
            
            # 创建内容框架
            content_frame = ctk.CTkFrame(guide_window, fg_color="#1A1A1A")
            content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
            
            # 创建步骤内容
            step1_label = ctk.CTkLabel(
                content_frame,
                text="步骤 1: 将应用程序移动到「应用程序」文件夹",
                font=("Microsoft YaHei UI", 16, "bold"),
                text_color="#007AFF",
                anchor="w"
            )
            step1_label.pack(fill="x", padx=20, pady=(20, 5))
            
            step1_desc = ctk.CTkLabel(
                content_frame,
                text="• 打开访达(Finder)\n• 将此程序拖拽到应用程序文件夹中",
                font=("Microsoft YaHei UI", 14),
                text_color="#FFFFFF",
                anchor="w",
                justify="left"
            )
            step1_desc.pack(fill="x", padx=20, pady=(0, 15))
            
            step2_label = ctk.CTkLabel(
                content_frame,
                text="步骤 2: 授予必要权限",
                font=("Microsoft YaHei UI", 16, "bold"),
                text_color="#007AFF",
                anchor="w"
            )
            step2_label.pack(fill="x", padx=20, pady=(10, 5))
            
            # 针对Intel/AMD和Apple Silicon芯片显示不同的说明
            if is_intel:
                step2_desc_text = (
                    "• 打开「系统设置」>「隐私与安全性」\n"
                    "• 点击「完全磁盘访问权限」，添加并勾选此应用程序\n"
                    "• 如果找不到对应选项，请在搜索框中输入相关关键词"
                )
            else:
                step2_desc_text = (
                    "• 打开「系统设置」>「隐私与安全性」\n"
                    "• 点击「App管理」，添加并勾选此应用程序\n"
                    "• 如果找不到对应选项，请在搜索框中输入相关关键词"
                )
                
            step2_desc = ctk.CTkLabel(
                content_frame,
                text=step2_desc_text,
                font=("Microsoft YaHei UI", 14),
                text_color="#FFFFFF",
                anchor="w",
                justify="left"
            )
            step2_desc.pack(fill="x", padx=20, pady=(0, 15))
            
            # 创建按钮容器
            button_frame = ctk.CTkFrame(guide_window, fg_color="#111111")
            button_frame.pack(fill="x", padx=20, pady=(0, 20))
            
            # 定义按钮点击函数
            def on_continue_click():
                logger.info("用户点击「知道了，不再提醒」按钮")
                guide_window.destroy()
                # 更新配置
                self.config['mac_permission_guide_shown'] = True
                self.save_config()
                
            def on_remind_later_click():
                logger.info("用户点击「下次继续提醒」按钮")
                guide_window.destroy()
                # 不更新配置，下次启动时仍会显示
                
            def open_system_preferences():
                logger.info("打开系统设置...")
                try:
                    if is_intel:
                        # 尝试打开完全磁盘访问权限设置（兼容不同系统版本）
                        try:
                            # 对于较新的macOS版本
                            subprocess.run(['open', 'x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles'])
                        except Exception:
                            # 对于较旧的macOS版本，直接打开安全性与隐私设置
                            subprocess.run(['open', '/System/Library/PreferencePanes/Security.prefPane'])
                    else:
                        # 对于Apple Silicon，打开App管理设置
                        try:
                            subprocess.run(['open', 'x-apple.systempreferences:com.apple.preference.security?Privacy_AppBundles'])
                        except Exception:
                            # 备选方案：打开通用安全性设置
                            subprocess.run(['open', 'x-apple.systempreferences:com.apple.preference.security'])
                except Exception as e:
                    logger.error(f"打开系统设置失败: {e}")
                    # 最后的备选方案：直接打开系统偏好设置/系统设置
                    try:
                        subprocess.run(['open', '/System/Applications/System Settings.app'])
                    except Exception:
                        try:
                            subprocess.run(['open', '/System/Applications/System Preferences.app'])
                        except Exception as e2:
                            logger.error(f"打开系统设置的所有尝试均失败: {e2}")
            
            # 创建打开系统设置按钮
            open_settings_button = ctk.CTkButton(
                button_frame,
                text="打开系统设置",
                width=150,
                height=35,
                corner_radius=6,
                fg_color="#007AFF",
                hover_color="#0063CC",
                text_color="#FFFFFF",
                font=("Microsoft YaHei UI", 14),
                command=open_system_preferences
            )
            open_settings_button.pack(side="left", padx=(0, 10))
            
            # 创建下次继续提醒按钮
            remind_button = ctk.CTkButton(
                button_frame,
                text="下次继续提醒",
                width=150,
                height=35,
                corner_radius=6,
                fg_color="#333333",
                hover_color="#444444",
                text_color="#FFFFFF",
                font=("Microsoft YaHei UI", 14),
                command=on_remind_later_click
            )
            remind_button.pack(side="left", padx=(10, 10))
            
            # 创建知道了按钮
            continue_button = ctk.CTkButton(
                button_frame,
                text="知道了，不再提醒",
                width=180,
                height=35,
                corner_radius=6,
                fg_color="#28CD41",
                hover_color="#219A32",
                text_color="#FFFFFF",
                font=("Microsoft YaHei UI", 14),
                command=on_continue_click
            )
            continue_button.pack(side="left", padx=(10, 0))
            
            # 居中放置按钮框架
            button_frame.pack_configure(anchor="center")
            
            logger.info("Mac系统权限指引对话框显示成功")
            
        except Exception as e:
            logger.error("显示Mac系统权限指引对话框时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error("错误追踪:")
            logger.error(traceback.format_exc())
            logger.error("="*50)

    def save_config(self):
        """保存配置"""
        logger.info("="*50)
        logger.info("开始保存配置")
        logger.info("-"*50)
        
        try:
            import json
            import os
            import platform
            from pathlib import Path
            
            # 根据不同操作系统选择配置文件存储路径
            system_name = platform.system()
            logger.debug(f"当前操作系统: {system_name}")
            
            if system_name == "Windows":
                config_dir = Path(os.getenv('LOCALAPPDATA')) / 'CursorPro'
            elif system_name == "Darwin":  # macOS
                config_dir = Path.home() / 'Library' / 'Application Support' / 'CursorPro'
            else:  # Linux
                config_dir = Path.home() / '.config' / 'CursorPro'
                
            config_dir.mkdir(exist_ok=True)
            config_path = config_dir / 'settings.dat'
            
            logger.info(f"保存配置到: {config_path}")
            logger.debug(f"配置内容: {self.config}")
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            logger.info("配置保存成功")
            logger.info("="*50)
            
        except Exception as e:
            logger.error("保存配置时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error("错误追踪:")
            logger.error(traceback.format_exc())
            logger.error("="*50)
            self.show_error(f"保存配置失败: {str(e)}")

    def show_settings(self):
        """显示设置窗口"""
        logger.info("="*50)
        logger.info("打开设置窗口")
        logger.info("-"*50)
        
        try:
            settings_window = ctk.CTkToplevel(self.root)
            settings_window.title("设置")
            settings_window.geometry("300x450")  # 增加窗口高度以适应版本显示
            settings_window.configure(fg_color=self.bg_color)
            settings_window.resizable(False, False)
            logger.debug("设置窗口基本属性已配置")
            
            # 确保窗口在主窗口中心
            settings_window.transient(self.root)
            settings_window.grab_set()
            logger.debug("窗口位置已调整")
            
            main_frame = ctk.CTkFrame(settings_window, fg_color=self.section_color)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)
            
            # 添加当前版本显示
            current_version = self.config.get('current_version', self.DEFAULT_VERSION)
            # 如果当前软件版本包含灰度测试字样，优先显示实际软件版本而非配置中的版本
            if "灰度测试" in self.DEFAULT_VERSION:
                current_version = self.DEFAULT_VERSION
                
            version_frame = ctk.CTkFrame(main_frame, fg_color="#1A1A1A", corner_radius=6)
            version_frame.pack(fill="x", padx=15, pady=(15, 20))
            
            version_label = ctk.CTkLabel(
                version_frame,
                text=f"当前版本: {current_version}",
                font=("Microsoft YaHei UI", 13),
                text_color="#CCCCCC"
            )
            version_label.pack(pady=10)
            
            # API Key保存设置
            logger.debug("创建API Key保存设置选项...")
            api_save_var = tk.BooleanVar(value=self.config.get('save_api_key', True))
            api_save_check = ctk.CTkCheckBox(
                main_frame,
                text="保存API Key",
                variable=api_save_var,
                font=self.normal_font,
                text_color=self.font_color_light,
                fg_color=self.accent_color,
                hover_color=self.button_hover,
                border_color="#444444"
            )
            api_save_check.pack(padx=15, pady=(0, 15), anchor="w")
            
            # 自动刷新设置
            logger.debug("创建自动刷新设置选项...")
            auto_refresh_var = tk.BooleanVar(value=self.config.get('auto_refresh', True))
            auto_refresh_check = ctk.CTkCheckBox(
                main_frame,
                text="启用自动刷新",
                variable=auto_refresh_var,
                font=self.normal_font,
                text_color=self.font_color_light,
                fg_color=self.accent_color,
                hover_color=self.button_hover,
                border_color="#444444"
            )
            auto_refresh_check.pack(padx=15, pady=(0, 15), anchor="w")
            
            # 刷新时间设置
            logger.debug("创建刷新时间设置选项...")
            refresh_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
            refresh_frame.pack(fill="x", padx=15, pady=(0, 15))
            
            ctk.CTkLabel(
                refresh_frame,
                text="自动刷新随机时间范围（秒）：",
                font=self.normal_font,
                text_color=self.font_color_light
            ).pack(anchor="w", pady=(0, 5))
            
            time_frame = ctk.CTkFrame(refresh_frame, fg_color="transparent")
            time_frame.pack(fill="x")
            
            min_time = ctk.CTkEntry(
                time_frame,
                width=60,
                height=30,
                font=self.normal_font,
                fg_color=self.entry_bg,
                text_color=self.entry_text,
                border_color=self.accent_color
            )
            min_time.pack(side="left")
            min_time.insert(0, str(self.config.get('refresh_interval', {}).get('min', 60)))
            
            ctk.CTkLabel(
                time_frame,
                text=" ~ ",
                font=self.normal_font,
                text_color=self.font_color_light
            ).pack(side="left", padx=5)
            
            max_time = ctk.CTkEntry(
                time_frame,
                width=60,
                height=30,
                font=self.normal_font,
                fg_color=self.entry_bg,
                text_color=self.entry_text,
                border_color=self.accent_color
            )
            max_time.pack(side="left")
            max_time.insert(0, str(self.config.get('refresh_interval', {}).get('max', 80)))
            
            # Cursor安装路径设置
            logger.debug("创建Cursor安装路径设置选项...")
            cursor_path_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
            cursor_path_frame.pack(fill="x", padx=15, pady=(0, 15))
            
            ctk.CTkLabel(
                cursor_path_frame,
                text="Cursor安装路径：",
                font=self.normal_font,
                text_color=self.font_color_light
            ).pack(anchor="w", pady=(0, 5))
            
            # 显示当前路径的标签
            current_path = Config.CURSOR_PATH if Config.CURSOR_PATH else "未设置"
            # 验证路径并处理显示
            is_valid_path = self.verify_cursor_path(current_path)
            display_path = current_path if is_valid_path else "未设置"
            
            cursor_path_label = ctk.CTkLabel(
                cursor_path_frame,
                text=display_path,
                font=self.normal_font,
                text_color="#AAAAAA" if is_valid_path else "#FF5555",  # 无效路径显示红色
                wraplength=250  # 确保长路径可以换行显示
            )
            cursor_path_label.pack(anchor="w", pady=(0, 5))
            
            # 选择路径按钮
            select_path_button = ctk.CTkButton(
                cursor_path_frame,
                text="选择Cursor路径",
                font=self.normal_font,
                fg_color=self.accent_color,
                hover_color=self.button_hover,
                command=lambda: self.select_cursor_path(cursor_path_label, settings_window)
            )
            select_path_button.pack(anchor="w")
            
            logger.debug("所有设置选项已创建")
            
            # 保存按钮
            def save_settings():
                logger.info("保存设置...")
                try:
                    self.config['save_api_key'] = api_save_var.get()
                    self.config['auto_refresh'] = auto_refresh_var.get()
                    self.config['refresh_interval'] = {
                        'min': int(min_time.get()),
                        'max': int(max_time.get())
                    }
                    # 确保保存cursor_path配置
                    self.config['cursor_path'] = Config.CURSOR_PATH
                    logger.debug(f"新的配置: {self.config}")
                    
                    self.save_config()
                    settings_window.destroy()
                    logger.info("设置已保存并关闭窗口")
                except ValueError as e:
                    logger.error(f"保存设置时发生错误: {str(e)}")
                    messagebox.showerror("错误", "请输入有效的数字")
                except Exception as e:
                    logger.error(f"保存设置时发生未知错误: {str(e)}")
                    logger.error(traceback.format_exc())
                    messagebox.showerror("错误", f"保存设置失败: {str(e)}")
            
            save_button = ctk.CTkButton(
                main_frame,
                text="保存设置",
                command=save_settings,
                font=self.button_font,
                fg_color=self.accent_color,
                hover_color=self.button_hover
            )
            save_button.pack(pady=20)
            logger.debug("保存按钮已创建")
            
            logger.info("设置窗口创建完成")
            
        except Exception as e:
            logger.error("创建设置窗口时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error("错误追踪:")
            logger.error(traceback.format_exc())
            logger.error("="*50)
            messagebox.showerror("错误", f"打开设置失败: {str(e)}")

    def initialize_account(self):
        """初始化账号信息并启动自动刷新"""
        def init_and_start():
            # 先加载账号信息，作为程序初始化，不是手动刷新
            self.load_current_account()  # 初始化加载账号信息，不需要修改暂停状态
            # 然后在主线程中启动自动刷新
            self.root.after(0, self.start_auto_refresh)
        
        # 在新线程中执行初始化
        threading.Thread(target=init_and_start).start()

    def start_auto_refresh(self):
        """启动自动刷新"""
        if self.auto_refresh_timer:
            self.root.after_cancel(self.auto_refresh_timer)
        
        if self.config.get('auto_refresh', True):
            import random
            min_time = self.config['refresh_interval']['min']
            max_time = self.config['refresh_interval']['max']
            next_refresh = random.randint(min_time * 1000, max_time * 1000)  # 转换为毫秒
            self.auto_refresh_timer = self.root.after(next_refresh, self.auto_refresh_callback)

    def stop_auto_refresh(self):
        """停止自动刷新"""
        if self.auto_refresh_timer:
            self.root.after_cancel(self.auto_refresh_timer)
            self.auto_refresh_timer = None

    def auto_refresh_callback(self):
        """自动刷新回调"""
        threading.Thread(target=lambda: self.refresh_account_info(manual_refresh=False)).start()
        self.start_auto_refresh()  # 设置下一次刷新

    def check_announcement(self):
        """检查公告更新"""
        try:
            # 确保每次检查公告时都使用最新的配置
            logger.info("="*30)
            logger.info("开始检查公告更新")
            logger.info("="*30)
            logger.info("检查公告更新前重新加载配置")
            self.load_config()  # 重新加载配置，确保使用最新的版本号
            
            # 为macOS创建自定义会话
            session = requests.Session()
            if platform.system() == "Darwin":  # macOS
                session.verify = False
                # 禁用警告
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            # 创建URL列表，首先使用主URL，然后是备用URL
            urls_to_try = [self.announcement_url] + self.backup_announcement_urls
            success = False
            data = None
            
            # 尝试所有URL直到成功
            for url in urls_to_try:
                try:
                    logger.info(f"正在从 {url} 获取公告信息")
                    response = session.get(url, timeout=10)
                    logger.info(f"公告请求状态码: {response.status_code}")
                    logger.info(f"公告响应头: {response.headers}")
                    
                    response.raise_for_status()
                    # 确保使用UTF-8编码
                    response.encoding = 'utf-8'
                    data = response.json()
                    logger.info(f"成功获取公告数据: {data}")
                    success = True
                    break  # 成功获取数据，跳出循环
                except requests.exceptions.Timeout:
                    logger.error(f"从 {url} 获取公告超时")
                    continue  # 尝试下一个URL
                except requests.exceptions.ConnectionError as e:
                    logger.error(f"连接公告服务器 {url} 失败: {str(e)}")
                    continue  # 尝试下一个URL
                except requests.exceptions.RequestException as e:
                    logger.error(f"请求公告 {url} 时发生错误: {str(e)}")
                    continue  # 尝试下一个URL
                except json.JSONDecodeError as e:
                    logger.error(f"解析公告JSON数据失败: {str(e)}")
                    logger.error(f"收到的原始数据: {response.text}")
                    continue  # 尝试下一个URL
            
            # 如果所有URL都尝试失败
            if not success:
                logger.error("所有公告URL都访问失败")
                raise Exception("无法获取公告信息")
            
            def update_announcement_ui():
                logger.info("-"*30)
                logger.info("开始更新公告UI")
                
                if data and isinstance(data, dict):
                    announcement = data.get('announcement', '')
                    version = data.get('latest_version', '')
                    self.download_url = data.get('download_url', None)
                    # 添加新字段
                    normal_message = data.get('message', '')
                    # 添加消息点击控制参数和URL
                    message_clickable = data.get('message_clickable', False)
                    message_url = data.get('message_url', '')
                    # 添加大更新标识字段
                    is_major_update = data.get('major_update', False)  # 默认为小更新

                    logger.info(f"原始公告内容: {announcement}")
                    logger.info(f"原始版本号: {version}")
                    logger.info(f"下载URL: {self.download_url}")
                    logger.info(f"普通消息内容: {normal_message}")
                    logger.info(f"消息是否可点击: {message_clickable}")
                    logger.info(f"消息点击URL: {message_url}")
                    logger.info(f"是否大更新: {is_major_update}")
                    
                    # 确保版本号格式正确
                    if version:
                        version = str(version).strip()
                        logger.info(f"处理后的云端版本: '{version}'")
                    else:
                        version = ""
                        logger.info("云端版本为空，设置为空字符串")
                    # 使用配置文件中的版本号
                    current_version = self.config.get('current_version', self.DEFAULT_VERSION)
                    logger.info(f"使用配置文件中的版本号: '{current_version}'")
                    
                    # 添加日志输出，便于调试
                    logger.info(f"云端版本: '{version}', 本地版本: '{current_version}'")
                    logger.info(f"版本比较结果: {version != current_version}")
                    
                    # 确保版本比较是严格的字符串比较
                    if version and version != current_version:
                        # 有新版本，显示公告内容并提醒下载
                        logger.info("检测到新版本，显示更新提示")
                        new_announcement = f"[发现新版本 {version}] {announcement} (点击下载)"
                        logger.info(f"更新后的公告内容: {new_announcement}")

                        # 根据更新类型设置不同的颜色
                        if is_major_update:
                            # 大更新使用红色
                            bg_color = "#8B0000"  # 深红色背景
                            text_color = "#FFFFFF"  # 白色文字
                            logger.info("大更新，使用红色样式")
                        else:
                            # 小更新使用蓝色（原来的样式）
                            bg_color = "#1E3A8A"  # 深蓝色背景
                            text_color = "#FFFFFF"  # 白色文字
                            logger.info("小更新，使用蓝色样式")

                        # 设置点击样式
                        self.announcement_frame.configure(fg_color=bg_color)
                        self.announcement_label.configure(text_color=text_color)
                        self.announcement_label.configure(text=new_announcement)
                        # 设置为不可点击（普通消息），但可点击下载
                        self.announcement_clickable = False
                        logger.info(f"已更新UI为新版本样式，背景色: {bg_color}, 文字色: {text_color}")
                        
                        # 新增：强制更新布局，确保标签尺寸已调整
                        self.announcement_label.update()
                        self.announcement_frame.update()
                        logger.info("已强制更新布局")
                        
                        # 新增：立即计算高度并调整窗口大小
                        def force_update_height():
                            logger.info("正在强制重新计算窗口高度（版本更新模式）")
                            # 清除高度缓存，强制重新计算
                            self.cached_required_height = 0
                            self.cached_announcement_text = ""
                            self.update_window_height()
                            
                        # 分两阶段重新计算高度，以确保UI已完全更新
                        self.root.after(50, force_update_height)
                        self.root.after(200, force_update_height)  # 再执行一次以防万一
                    else:
                        # 当前已是最新版本，检查是否有普通消息内容
                        logger.info("当前已是最新版本，检查是否有普通消息内容")
                        if normal_message and normal_message.strip():
                            # 有普通消息内容，显示普通消息
                            logger.info("发现普通消息内容，显示普通消息")
                            
                            # 根据message_clickable参数决定样式和点击行为
                            if message_clickable and message_url:
                                # 消息可点击，设置为可点击样式
                                logger.info(f"消息可点击，点击URL: {message_url}")
                                self.announcement_frame.configure(fg_color="#1A1A1A")  # 普通背景样式
                                self.announcement_label.configure(text_color="#999999")  # 使用灰色
                                self.announcement_label.configure(text=f"{normal_message} (点击访问)")
                                # 保存消息URL供点击使用
                                self.message_url = message_url
                                # 添加明确的可点击状态标记
                                self.announcement_clickable = True
                                logger.info(f"已设置消息点击URL: {self.message_url}")
                            else:
                                # 消息不可点击，使用普通样式
                                logger.info("消息不可点击，使用普通样式")
                                self.announcement_frame.configure(fg_color="#1A1A1A")  # 普通背景样式
                                self.announcement_label.configure(text_color="#CCCCCC")  # 普通文字颜色
                                self.announcement_label.configure(text=normal_message)
                                # 清空消息URL
                                self.message_url = None
                                # 设置为不可点击状态
                                self.announcement_clickable = False
                                logger.info("已清空消息点击URL")
                                
                            logger.info(f"已显示普通消息内容: {normal_message}")
                        else:
                            # 无普通消息内容，显示当前版本
                            logger.info("无普通消息内容，显示当前版本")
                            self.announcement_frame.configure(fg_color="#1A1A1A")  # 恢复普通样式
                            self.announcement_label.configure(text_color="#CCCCCC")  # 恢复普通文字颜色
                            self.announcement_label.configure(text=f"当前已是最新版本 {current_version}")
                            # 清空消息URL
                            self.message_url = None
                            # 设置为不可点击状态
                            self.announcement_clickable = False
                            logger.info("已清空消息点击URL")
                            logger.info(f"已更新UI为最新版本样式，显示版本号: {current_version}")
                        
                else:
                    logger.info("公告数据无效或为空")
                    self.announcement_label.configure(text="暂无公告")
                    # 设置为不可点击状态
                    self.announcement_clickable = False
                
                logger.info("公告UI更新完成")
                
                # 在公告更新后，等待一小段时间让UI更新完成，然后调整窗口高度
                # 替换单次更新为多次强制更新，确保UI完全渲染后计算高度
                self.root.after(100, self.force_update_height_for_all)
                self.root.after(300, self.force_update_height_for_all)  # 再次更新，确保公告完全渲染
                self.root.after(600, self.force_update_height_for_all)  # 第三次更新，防止任何延迟渲染问题
                
                logger.info("-"*30)
            
            # 安全地在主线程中更新UI
            self.root.after(0, update_announcement_ui)
            
            # 成功获取后，设置正常的检查间隔
            self.announcement_timer = self.root.after(
                self.announcement_check_interval * 1000,
                lambda: threading.Thread(target=self.check_announcement).start()
            )
            
        except Exception as e:
            error_msg = str(e)  # 在闭包外捕获错误信息
            logger.error(f"获取公告时发生异常: {error_msg}")
            logger.error(f"异常类型: {type(e).__name__}")
            logger.error(traceback.format_exc())
            
            def show_error():
                self.announcement_label.configure(text=f"获取公告失败: {error_msg[:50]}...")
                logger.error(f"获取公告失败: {error_msg}")
            self.root.after(0, show_error)
            
            # 失败后10秒重试
            self.announcement_timer = self.root.after(
                10000,  # 10秒后重试
                lambda: threading.Thread(target=self.check_announcement).start()
            )

    def start_announcement_check(self):
        """启动公告检查"""
        logger.info("="*50)
        logger.info("启动公告检查服务")
        logger.info("="*50)
        
        try:
            if self.announcement_timer:
                logger.debug("取消现有的公告检查定时器")
                self.root.after_cancel(self.announcement_timer)
                logger.debug("现有定时器已取消")
            
            logger.info("启动公告检查线程")
            # 记录当前配置中的版本号
            current_version = self.config.get('current_version', self.DEFAULT_VERSION)
            logger.info(f"启动公告检查前的当前版本: {current_version}")
            
            threading.Thread(target=self.check_announcement).start()
            logger.info("公告检查线程已启动")
            
        except Exception as e:
            logger.error("启动公告检查服务时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error(traceback.format_exc())

    def stop_announcement_check(self):
        """停止公告检查"""
        logger.info("停止公告检查服务")
        try:
            if self.announcement_timer:
                logger.debug("取消公告检查定时器")
                self.root.after_cancel(self.announcement_timer)
                self.announcement_timer = None
                logger.info("公告检查服务已停止")
            else:
                logger.debug("没有运行中的公告检查定时器")
        except Exception as e:
            logger.error("停止公告检查服务时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error(traceback.format_exc())

    def show_api_context_menu(self, event):
        """显示API输入框的右键菜单"""
        logger.debug("显示API输入框右键菜单")
        try:
            # 在事件发生的位置显示菜单
            self.api_context_menu.tk_popup(event.x_root, event.y_root)
            logger.debug(f"菜单显示位置: x={event.x_root}, y={event.y_root}")
        except Exception as e:
            logger.error("显示右键菜单时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error(traceback.format_exc())
        finally:
            # 确保菜单正确关闭
            self.api_context_menu.grab_release()
            logger.debug("释放菜单grab")
            
    def copy_api_key(self):
        """复制API Key到剪贴板"""
        logger.info("="*50)
        logger.info("复制API Key")
        logger.info("-"*50)
        
        try:
            api_key = self.api_entry.get()
            if api_key:
                self.root.clipboard_clear()
                self.root.clipboard_append(api_key)
                logger.info("API Key已复制到剪贴板")
                self.show_info("API Key已复制到剪贴板")
                # 记录部分API Key用于调试
                masked_key = f"{api_key[:5]}...{api_key[-5:]}"
                logger.debug(f"已复制的API Key: {masked_key}")
            else:
                logger.warning("没有API Key可供复制")
                self.show_warning("没有API Key可供复制")
        except Exception as e:
            logger.error("复制API Key时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error(traceback.format_exc())
            self.show_error(f"复制失败: {str(e)}")

    def paste_api_key(self):
        """从剪贴板粘贴内容到API输入框"""
        logger.info("="*50)
        logger.info("粘贴API Key")
        logger.info("-"*50)
        
        try:
            clipboard_text = self.root.clipboard_get()
            logger.debug("已获取剪贴板内容")
            
            self.api_entry.delete(0, tk.END)
            self.api_entry.insert(0, clipboard_text)
        except tk.TclError:
            # 剪贴板为空或格式不支持时不做任何操作
            pass
            
            # 记录部分API Key用于调试
            if clipboard_text:
                masked_text = f"{clipboard_text[:5]}...{clipboard_text[-5:]}"
                logger.debug(f"已粘贴的内容: {masked_text}")
                logger.info("API Key已成功粘贴")
            else:
                logger.warning("剪贴板内容为空")
                
        except tk.TclError as e:
            logger.warning("剪贴板为空或格式不支持")
            logger.debug(f"错误详情: {str(e)}")
        except Exception as e:
            logger.error("粘贴API Key时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error(traceback.format_exc())

    def run(self):
        """启动应用"""
        logger.info("="*50)
        logger.info("启动应用程序")
        logger.info("-"*50)
        
        try:
            # 添加窗口关闭事件处理
            self.root.protocol("WM_DELETE_WINDOW", self.on_close)
            
            # 启动公告检查
            logger.info("启动公告检查服务...")
            self.start_announcement_check()
            
            # 启动主循环
            logger.info("启动GUI主循环...")
            self.root.mainloop()
            
        except Exception as e:
            logger.error("应用程序运行时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error("错误追踪:")
            logger.error(traceback.format_exc())
            logger.error("="*50)
            messagebox.showerror("错误", f"程序运行出错: {str(e)}")
            
    def on_close(self):
        """处理窗口关闭事件"""
        logger.info("="*50)
        logger.info("用户关闭应用程序")
        logger.info("-"*50)
        
        try:
            # 停止所有定时器和线程
            logger.info("停止所有定时器...")
            self.stop_auto_refresh()
            self.stop_announcement_check()
            
            # 保存配置
            logger.info("保存配置...")
            self.save_config()
            
            # 等待一小段时间，确保所有操作完成
            logger.info("等待所有操作完成...")
            time.sleep(0.1)
            
            # 销毁窗口并退出
            logger.info("销毁窗口并退出...")
            self.root.destroy()
            
        except Exception as e:
            logger.error("关闭应用程序时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error("错误追踪:")
            logger.error(traceback.format_exc())
            logger.error("="*50)
            
            # 确保窗口被销毁
            try:
                self.root.destroy()
            except:
                pass

    def select_cursor_path(self, path_label, settings_window):
        """
        选择Cursor安装路径
        
        Args:
            path_label: 显示路径的标签控件
            settings_window: 设置窗口
        """
        logger.info("="*50)
        logger.info("开始选择Cursor安装路径")
        logger.info("-"*50)
        
        try:
            # 导入所需模块
            from update_cursor_token_main import FilePathManager, Config as TokenConfig
            import Cursor as cursor_finder
            
            # 先尝试查找系统中已安装的Cursor
            logger.info("查找系统中已安装的Cursor")
            try:
                cursor_installations = cursor_finder.find_cursor_installations()
                logger.info(f"找到 {len(cursor_installations)} 个Cursor安装")
            except Exception as e:
                logger.error(f"查找Cursor安装时出错: {e}")
                logger.error(traceback.format_exc())
                cursor_installations = []
            
            # 无论找到几个安装，都显示中间选择界面
            result = self.show_cursor_selection_dialog(cursor_installations, settings_window)
            
            if result == "MANUAL_SELECT":
                # 用户选择手动选择，使用传统选择方法
                logger.info("用户选择手动指定Cursor目录")
                
                # 保存原始路径
                original_path = TokenConfig.CURSOR_PATH
                
                # 临时清除保存的路径，强制显示选择对话框
                TokenConfig.CURSOR_PATH = None
                logger.info("已临时清除保存的路径，确保显示选择对话框")
                
                # 调用选择方法
                cursor_path = FilePathManager.prompt_user_cursor_directory()
                
                # 如果用户取消了选择，恢复原始路径
                if not cursor_path and original_path:
                    TokenConfig.CURSOR_PATH = original_path
                    logger.info(f"用户取消选择，恢复原始路径: {original_path}")
            elif result:
                # 用户选择了一个已找到的安装
                cursor_path = Path(result)
                logger.info(f"用户选择了Cursor路径: {cursor_path}")
            else:
                # 用户取消了选择
                cursor_path = None
                logger.info("用户取消了Cursor路径选择")
            
            # 处理选择结果
            if cursor_path:
                # 更新Config.CURSOR_PATH和settings.dat
                logger.info(f"最终选择的Cursor路径: {cursor_path}")
                FilePathManager.update_cursor_path(cursor_path)
                
                # 添加安全检查，确保标签控件仍然存在
                try:
                    if path_label.winfo_exists():
                        # 更新路径标签显示
                        path_label.configure(text=str(cursor_path))
                        
                        # 验证并更新文本颜色
                        is_valid_path = self.verify_cursor_path(str(cursor_path))
                        path_label.configure(
                            text=str(cursor_path),
                            text_color="#AAAAAA" if is_valid_path else "#FF5555"
                        )
                        
                        # 提示用户成功
                        messagebox.showinfo("成功", "Cursor安装路径已更新。")
                except Exception as ui_error:
                    logger.error(f"更新UI时出错，可能控件已销毁: {ui_error}")
                    # 不影响功能的错误，可以继续
                
        except Exception as e:
            logger.error("选择Cursor路径时发生错误")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误信息: {str(e)}")
            logger.error("错误追踪:")
            logger.error(traceback.format_exc())
            logger.error("="*50)
            
            # 安全地显示错误消息
            try:
                if settings_window.winfo_exists():
                    messagebox.showerror("错误", f"选择Cursor路径失败: {str(e)}")
            except:
                pass
            
        # 确保设置窗口保持前台显示（如果它仍然存在）
        try:
            if settings_window.winfo_exists():
                settings_window.lift()
                settings_window.focus_force()
        except:
            pass

    def show_cursor_selection_dialog(self, cursor_installations, parent_window):
        """
        显示Cursor安装选择对话框
        
        Args:
            cursor_installations: 找到的Cursor安装列表
            parent_window: 父窗口
            
        Returns:
            选择的路径字符串，或 "MANUAL_SELECT" 表示手动选择，或 None 表示取消
        """
        logger.info("显示Cursor安装选择对话框")
        
        # 导入所需模块
        import Cursor as cursor_finder
        
        # 创建选择窗口
        selection_window = ctk.CTkToplevel(parent_window)
        selection_window.title("选择Cursor安装")
        selection_window.geometry("500x400")
        selection_window.configure(fg_color=self.bg_color)
        selection_window.resizable(False, False)
        selection_window.transient(parent_window)
        selection_window.grab_set()
        # Windows系统下设置置顶
        if platform.system() == 'Windows':
            selection_window.attributes("-topmost", True)
            selection_window.lift()
            selection_window.focus_force()

        # 确保窗口在主窗口中心
        selection_window.update_idletasks()
        x = parent_window.winfo_rootx() + (parent_window.winfo_width() - selection_window.winfo_width()) // 2
        y = parent_window.winfo_rooty() + (parent_window.winfo_height() - selection_window.winfo_height()) // 2
        selection_window.geometry(f"+{x}+{y}")
        
        # 存储用户选择结果
        selection_window.result = None
        
        # 创建主框架
        main_frame = ctk.CTkFrame(selection_window, fg_color=self.section_color)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="选择Cursor安装路径",
            font=("Microsoft YaHei UI", 16, "bold"),
            text_color="#CCCCCC"
        )
        title_label.pack(pady=(15, 5))
        
        if cursor_installations:
            subtitle = f"找到 {len(cursor_installations)} 个Cursor安装，请选择一个："
        else:
            subtitle = "未找到Cursor安装，请手动选择："
            
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text=subtitle,
            font=("Microsoft YaHei UI", 12),
            text_color="#AAAAAA"
        )
        subtitle_label.pack(pady=(0, 15))
        
        # 创建滚动区域用于显示安装列表
        scrollable_frame = ctk.CTkScrollableFrame(
            main_frame,
            fg_color="#1A1A1A",
            width=450,
            height=200
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 添加找到的Cursor安装路径
        for i, installation in enumerate(cursor_installations):
            # 创建每个安装的容器框架
            install_frame = ctk.CTkFrame(
                scrollable_frame,
                fg_color="#222222",
                corner_radius=6,
                height=60
            )
            install_frame.pack(fill="x", padx=5, pady=5)
            
            # 获取安装信息
            name = installation.get("名称", "未知Cursor")
            version = installation.get("版本", "未知版本")
            path = installation.get("安装位置", "未知路径")
            is_valid = cursor_finder.validate_cursor_installation(path)
            is_default = installation.get("默认安装", False)
            
            # 创建信息标签
            name_label = ctk.CTkLabel(
                install_frame,
                text=f"{name} {version}",
                font=("Microsoft YaHei UI", 12, "bold"),
                text_color="#FFFFFF",
                anchor="w"
            )
            name_label.pack(anchor="w", padx=10, pady=(10, 0))
            
            path_label = ctk.CTkLabel(
                install_frame,
                text=f"路径: {path}",
                font=("Microsoft YaHei UI", 10),
                text_color="#AAAAAA",
                anchor="w"
            )
            path_label.pack(anchor="w", padx=10, pady=(5, 0))
            
            status_text = ""
            if is_valid:
                status_text += "有效 "
            else:
                status_text += "无效 "
                
            if is_default:
                status_text += "默认安装"
                
            status_label = ctk.CTkLabel(
                install_frame,
                text=status_text,
                font=("Microsoft YaHei UI", 10),
                text_color="#00AA00" if is_valid else "#AA0000",
                anchor="w"
            )
            status_label.pack(anchor="w", padx=10, pady=(5, 10))
            
            # 创建选择按钮
            select_button = ctk.CTkButton(
                install_frame,
                text="选择此路径",
                width=100,
                height=25,
                corner_radius=4,
                fg_color=self.accent_color,
                hover_color=self.button_hover,
                command=lambda p=path: self.on_path_selected(selection_window, p)
            )
            select_button.place(relx=1.0, rely=0.5, anchor="e", x=-10)
        
        # 创建底部按钮区域
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=10, pady=(10, 10))
        
        # 创建手动选择按钮
        manual_button = ctk.CTkButton(
            button_frame,
            text="手动选择路径",
            width=150,
            height=35,
            corner_radius=6,
            fg_color="#444444",
            hover_color="#555555",
            command=lambda: self.on_manual_select(selection_window)
        )
        manual_button.pack(side="left", padx=(0, 10))
        
        # 创建取消按钮
        cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            width=100,
            height=35,
            corner_radius=6,
            fg_color="#333333",
            hover_color="#444444",
            command=selection_window.destroy
        )
        cancel_button.pack(side="right")
        
        # 等待用户操作
        parent_window.wait_window(selection_window)
        return selection_window.result
    
    def on_path_selected(self, window, path):
        """当用户选择一个路径时的回调"""
        logger.info(f"用户从列表中选择了路径: {path}")
        window.result = path
        window.destroy()
        
    def on_manual_select(self, window):
        """当用户选择手动选择时的回调"""
        logger.info("用户选择手动指定路径")
        window.result = "MANUAL_SELECT"
        window.destroy()

    def update_button_size(self, parent_frame):
        """在窗口加载完成后调整按钮尺寸"""
        if hasattr(self, 'reset_button'):
            # 再次检查父容器宽度并更新按钮
            parent_width = parent_frame.winfo_width()
            if parent_width > 20:  # 确保父容器已经有合理的宽度
                # 触发一个Configure事件来更新按钮
                self.reset_button.event_generate("<Configure>")
            else:
                # 如果父容器宽度仍然不合理，稍后再尝试
                self.root.after(100, lambda: self.update_button_size(parent_frame))

    def verify_cursor_path(self, path_str):
        """验证Cursor路径是否有效，同时考虑Mac和Windows的不同路径结构"""
        import platform
        if not path_str or path_str == "未设置":
            return False
            
        try:
            path = Path(path_str)
            system = platform.system()
            
            # 对于Mac系统，需要特殊处理.app包
            if system == "Darwin":  # macOS
                if path.name.endswith('.app'):
                    app_path = path / "Contents" / "Resources" / "app"
                else:
                    app_path = path
                return app_path.exists() and (app_path / "package.json").exists()
            else:  # Windows或其他系统
                app_path = path / "resources" / "app"
                return app_path.exists() and (app_path / "package.json").exists()
        except Exception as e:
            logger.error(f"验证Cursor路径时出错: {e}")
            return False

    def calculate_required_window_height(self):
        """计算基于当前内容所需的窗口高度"""
        logger.info("计算所需窗口高度")

        # 获取当前公告文本和无限制状态显示状态
        current_announcement_text = self.announcement_label.cget("text")
        unlimited_status_visible = hasattr(self, 'unlimited_status_frame') and self.unlimited_status_frame.winfo_viewable()

        # 创建缓存键，包含公告文本和无限制状态显示状态
        cache_key = f"{current_announcement_text}_{unlimited_status_visible}"

        # 检查缓存是否有效
        if hasattr(self, 'cached_content_key') and cache_key == self.cached_content_key and self.cached_required_height > 0:
            logger.debug(f"内容未变化，使用缓存高度: {self.cached_required_height}px")
            return self.cached_required_height

        # 内容已变化，需要重新计算
        logger.info("内容已变化，重新计算所需高度")
        self.cached_content_key = cache_key

        # 获取公告标签实际高度
        announcement_height = self.announcement_label.winfo_height()
        logger.debug(f"公告区域高度: {announcement_height}px")

        # 获取无限制状态显示框高度（如果显示的话）
        unlimited_status_height = 0
        if unlimited_status_visible:
            unlimited_status_height = self.unlimited_status_label.winfo_height()
            logger.debug(f"无限制状态区域高度: {unlimited_status_height}px")

        # 基础窗口高度（包含所有其他UI元素的最小高度）
        base_height = 714  # 从710增加到714

        # 计算所需总高度
        required_height = base_height

        # 如果公告超过默认高度，增加窗口高度
        if announcement_height > 40:  # 40是公告框架的默认高度
            extra_height = announcement_height - 40
            required_height += extra_height
            logger.debug(f"公告需要额外高度: {extra_height}px")

        # 如果无限制状态显示框可见且超过默认高度，增加窗口高度
        if unlimited_status_visible and unlimited_status_height > 40:  # 40是默认高度
            extra_height = unlimited_status_height - 40
            required_height += extra_height
            logger.debug(f"无限制状态需要额外高度: {extra_height}px")

        # 更新缓存
        self.cached_required_height = required_height
        logger.info(f"计算得到所需总高度: {required_height}px")
        return required_height

    def update_window_height(self):
        """更新窗口高度以适应内容"""
        logger.info("更新窗口高度")
        
        # 获取当前窗口尺寸
        current_geometry = self.root.geometry()
        current_width = int(current_geometry.split('x')[0])
        
        # 计算所需高度
        required_height = self.calculate_required_window_height()
        
        # 更新窗口尺寸
        new_geometry = f"{current_width}x{required_height}"
        logger.info(f"设置新窗口尺寸: {new_geometry}")
        self.root.geometry(new_geometry)
        
        # 更新最小高度
        self.root.minsize(375, required_height)
        logger.info(f"更新窗口最小高度: {required_height}px")
        
    def force_update_height_for_all(self):
        """强制重新计算并更新窗口高度，适用于所有公告场景"""
        logger.info("强制重新计算窗口高度（全局模式）")
        # 强制更新公告标签布局
        self.announcement_label.update()
        self.announcement_frame.update()
        # 清除高度缓存，强制重新计算
        self.cached_required_height = 0
        self.cached_announcement_text = ""
        # 更新窗口高度
        self.update_window_height()

    def format_email_display(self, email):
        """
        格式化邮箱显示，将@icloud替换为@gmail
        
        Args:
            email: 原始邮箱地址
            
        Returns:
            格式化后的邮箱地址
        """
        if email and '@icloud.' in email:
            formatted_email = email.replace('@icloud.', '@gmail.')
            logger.debug(f"邮箱格式化: {email} -> {formatted_email}")
            return formatted_email
        return email

if __name__ == "__main__":
    try:
        # Windows系统管理员权限检查
        if platform.system() == 'Windows':
            # 检查是否有管理员权限
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                logger.info("当前非管理员权限，询问用户是否需要提升...")
                
                # 创建一个根窗口但不显示它 (用于显示消息框)
                root = tk.Tk()
                root.withdraw()
                # Windows系统下设置置顶
                root.attributes("-topmost", True)
                root.lift()
                root.focus_force()

                # 询问用户是否要以管理员权限运行
                result = messagebox.askyesno(
                    "权限确认",
                    "推荐以管理员权限运行此程序，以确保所有功能正常工作。\n\n是否以管理员权限运行？",
                    icon="question",
                    parent=root  # 添加parent参数，确保弹窗置顶
                )
                
                if result:
                    logger.info("用户选择以管理员权限运行")
                    # 请求管理员权限重新运行程序
                    ctypes.windll.shell32.ShellExecuteW(
                        None, "runas", sys.executable, " ".join(sys.argv), None, 1
                    )
                    # 退出当前进程
                    logger.info("已请求管理员权限，退出当前进程")
                    root.destroy()
                    sys.exit(0)
                else:
                    # 用户选择不使用管理员权限，显示二次确认弹窗
                    logger.info("用户选择不使用管理员权限，显示二次确认")
                    # Windows系统下确保根窗口仍然置顶
                    root.attributes("-topmost", True)
                    root.lift()
                    root.focus_force()
                    second_result = messagebox.askyesno(
                        "二次确认",
                        "确定不使用管理员权限启动吗？\n\n这可能会影响换号和某些功能的正常使用。",
                        icon="warning",
                        parent=root  # 添加parent参数，确保弹窗置顶
                    )
                    
                    if second_result:
                        # 用户在二次确认中改变主意，请求管理员权限
                        logger.info("用户在二次确认中选择使用管理员权限")
                        ctypes.windll.shell32.ShellExecuteW(
                            None, "runas", sys.executable, " ".join(sys.argv), None, 1
                        )
                        # 退出当前进程
                        logger.info("已请求管理员权限，退出当前进程")
                        root.destroy()
                        sys.exit(0)
                    
                    # 用户坚持不使用管理员权限，继续以普通权限运行
                    logger.info("用户坚持不使用管理员权限，继续以普通权限运行")
                    root.destroy()
            else:
                logger.info("当前已具有管理员权限")
                
        logger.info("="*50)
        logger.info("创建应用程序实例")
        app = ModernResetApp()
        logger.info("启动应用程序")
        app.run()
        logger.info("应用程序正常退出")
        logger.info("="*50)
    except Exception as e:
        logger.critical("应用程序启动失败")
        logger.critical(f"错误类型: {type(e).__name__}")
        logger.critical(f"错误信息: {str(e)}")
        logger.critical("错误追踪:")
        logger.critical(traceback.format_exc())
        logger.critical("="*50)
        sys.exit(1)
