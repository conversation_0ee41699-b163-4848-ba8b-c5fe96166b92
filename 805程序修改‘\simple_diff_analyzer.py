#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的main.js差异分析器
"""

import re

def read_file(filepath):
    """读取文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"读取文件失败 {filepath}: {e}")
        return None

def find_machine_id_functions(content):
    """查找机器ID相关函数"""
    # 查找getMachineId和getMacMachineId函数
    patterns = [
        r'getMachineId\(\)\{[^}]*\}',
        r'getMacMachineId\(\)\{[^}]*\}',
        r'async getMachineId\(\)\{[^}]*\}',
        r'async getMacMachineId\(\)\{[^}]*\}'
    ]
    
    results = []
    for pattern in patterns:
        matches = re.findall(pattern, content)
        if matches:
            results.extend(matches)
    
    return results

def search_key_terms(content):
    """搜索关键术语"""
    terms = ['getMachineId', 'getMacMachineId', 'machineId', 'macMachineId']
    results = {}
    
    for term in terms:
        count = content.count(term)
        results[term] = count
        
        # 查找包含该术语的行
        lines = content.split('\n')
        matching_lines = []
        for i, line in enumerate(lines):
            if term in line:
                matching_lines.append((i+1, line.strip()))
                if len(matching_lines) >= 5:  # 限制显示数量
                    break
        results[f"{term}_lines"] = matching_lines
    
    return results

def main():
    print("开始简单分析...")
    
    # 读取文件
    content1 = read_file("main.js对比/main修改之前_beautified.js")
    content2 = read_file("main.js对比/main修改之后_beautified.js")
    
    if not content1 or not content2:
        print("无法读取文件")
        return
    
    print(f"修改前文件大小: {len(content1):,} 字符")
    print(f"修改后文件大小: {len(content2):,} 字符")
    
    # 查找机器ID函数
    print("\n=== 修改前的机器ID函数 ===")
    funcs1 = find_machine_id_functions(content1)
    for i, func in enumerate(funcs1):
        print(f"{i+1}: {func}")
    
    print("\n=== 修改后的机器ID函数 ===")
    funcs2 = find_machine_id_functions(content2)
    for i, func in enumerate(funcs2):
        print(f"{i+1}: {func}")
    
    # 搜索关键术语
    print("\n=== 关键术语统计 ===")
    terms1 = search_key_terms(content1)
    terms2 = search_key_terms(content2)
    
    for term in ['getMachineId', 'getMacMachineId', 'machineId', 'macMachineId']:
        count1 = terms1.get(term, 0)
        count2 = terms2.get(term, 0)
        print(f"{term}: {count1} -> {count2}")
        
        if count1 != count2:
            print(f"  变化: {count2 - count1}")
            print("  修改前的行:")
            for line_num, line in terms1.get(f"{term}_lines", [])[:3]:
                print(f"    {line_num}: {line[:100]}...")
            print("  修改后的行:")
            for line_num, line in terms2.get(f"{term}_lines", [])[:3]:
                print(f"    {line_num}: {line[:100]}...")

if __name__ == "__main__":
    main()
