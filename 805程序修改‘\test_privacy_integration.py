#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试隐私模式集成功能
"""

import sys
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_privacy_manager_import():
    """测试隐私模式管理器导入"""
    try:
        from cursor_privacy_manager import enable_privacy_mode_sync, CursorPrivacyManager
        logger.info("✅ 成功导入隐私模式管理器")
        return True
    except ImportError as e:
        logger.error(f"❌ 导入隐私模式管理器失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 导入时发生异常: {e}")
        return False

def test_jwt_availability():
    """测试JWT库是否可用"""
    try:
        import jwt
        logger.info("✅ PyJWT库可用")
        return True
    except ImportError:
        logger.error("❌ PyJWT库未安装，请运行: pip install PyJWT")
        return False

def test_mock_privacy_function():
    """测试隐私模式函数（使用模拟token）"""
    try:
        from cursor_privacy_manager import enable_privacy_mode_sync
        
        # 使用一个模拟的JWT token进行测试（不会实际发送请求）
        mock_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhdXRoMHx0ZXN0X3VzZXJfaWQiLCJpYXQiOjE2MzAwMDAwMDAsImV4cCI6MTYzMDAwMzYwMH0.test_signature"
        
        # 这里只测试函数是否能正常创建管理器实例，不实际发送网络请求
        logger.info("🧪 测试隐私模式管理器创建...")
        
        # 由于网络请求会失败，我们只测试到创建管理器实例
        from cursor_privacy_manager import CursorPrivacyManager
        try:
            manager = CursorPrivacyManager(mock_token)
            logger.info(f"✅ 成功创建隐私模式管理器，用户ID: {manager.user_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 创建隐私模式管理器失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试隐私模式函数时发生异常: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🔒 开始测试隐私模式集成功能")
    logger.info("=" * 50)
    
    tests = [
        ("导入隐私模式管理器", test_privacy_manager_import),
        ("检查JWT库可用性", test_jwt_availability),
        ("测试隐私模式函数", test_mock_privacy_function),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 测试: {test_name}")
        if test_func():
            passed += 1
        else:
            logger.error(f"测试失败: {test_name}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！隐私模式集成功能准备就绪")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查相关依赖和配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
