import os
import sqlite3
import requests
try:
    import jwt
except ImportError:
    print("PyJWT 库未安装，请运行 'pip install PyJWT' 安装")
    jwt = None
from typing import Optional, Dict, Any
from datetime import datetime, timedelta, timezone
import asyncio
import aiohttp
import ssl
from dataclasses import dataclass
import platform
import logging
import traceback

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

@dataclass
class UsageStats:
    email: str  # 添加邮箱字段
    trial_days: 'UsageMetric'
    premium_usage: 'UsageMetric'
    omini_usage: 'UsageMetric'

@dataclass
class UsageMetric:
    total: int
    used: int
    remaining: int

class CursorUsageTracker:
    def __init__(self, token: str):
        logger.info("=== 初始化Cursor使用追踪器 ===")
        self.token = token
        logger.debug(f"Token前缀: {token[:10]}...")  # 只显示Token前10位
        
        self.headers = {
            "Cookie": f"WorkosCursorSessionToken={token}",
            "Authorization": f"Bearer {token}"
        }
        logger.debug("已设置请求头")
        
        self.base_url = "https://cursor.sh/api"
        logger.info(f"API基础URL: {self.base_url}")
        
        # 创建SSL上下文，处理macOS的证书验证问题
        self.ssl_context = None
        if platform.system() == "Darwin":  # macOS
            logger.info("检测到macOS系统，配置SSL上下文...")
            self.ssl_context = ssl.create_default_context()
            self.ssl_context.check_hostname = False
            self.ssl_context.verify_mode = ssl.CERT_NONE
            logger.info("SSL上下文配置完成")

    async def get_premium_usage(self) -> dict:
        """获取 Premium 使用情况 - 更新版本"""
        logger.info("=== 开始获取Premium使用情况 ===")
        max_retries = 2  # 减少重试次数从3次到2次
        retry_delay = 1  # 减少重试延迟从2秒到1秒
        
        for attempt in range(1, max_retries + 1):
            try:
                user_id = self.token.split('%3A%3A')[0]
                logger.debug(f"用户ID: {user_id}")
                
                async with aiohttp.ClientSession() as session:
                    logger.info("发送使用情况请求...")
                    try:
                        async with session.get(
                            "https://www.cursor.com/api/usage",
                            params={"user": user_id},
                            headers=self.headers,
                            ssl=self.ssl_context,
                            timeout=8  # 减少超时时间从15秒到8秒
                        ) as response:
                            logger.info(f"响应状态码: {response.status}")
                            if response.status != 200:
                                logger.error(f"获取使用情况失败: HTTP {response.status}")
                                if attempt < max_retries:
                                    logger.warning(f"HTTP错误 {response.status}，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                                    await asyncio.sleep(retry_delay)
                                    continue
                                return {"error": f"服务器返回错误状态码: {response.status}"}
                                
                            data = await response.json()
                            logger.debug(f"原始响应数据: {data}")
                            
                            gpt4_usage = data.get('gpt-4', {})
                            gpt35_usage = data.get('gpt-3.5-turbo', {})
                            
                            # 适应新API格式：maxRequestUsage现在是null
                            gpt4_limit = gpt4_usage.get('maxRequestUsage')
                            gpt35_limit = gpt35_usage.get('maxRequestUsage')
                            
                            result = {
                                "premium": {
                                    "current": gpt4_usage.get('numRequests', 0),
                                    "limit": gpt4_limit if gpt4_limit is not None else "无限制"
                                },
                                "gpt4omini": {
                                    "current": gpt35_usage.get('numRequests', 0),
                                    "limit": gpt35_limit if gpt35_limit is not None else "无限制"
                                }
                            }
                            
                            logger.info("=== Premium使用情况 ===")
                            logger.info(f"GPT-4使用量: {result['premium']['current']}/{result['premium']['limit']}")
                            logger.info(f"GPT-3.5使用量: {result['gpt4omini']['current']}/{result['gpt4omini']['limit']}")
                            
                            # 只有在有限制时才计算使用率
                            if isinstance(result['premium']['limit'], int):
                                usage_percent = (result['premium']['current'] / result['premium']['limit'] * 100)
                                logger.info(f"使用率: {usage_percent:.1f}%")
                                if usage_percent > 90:
                                    logger.warning("⚠️ Premium使用量已超过90%，请注意配额")
                                elif usage_percent > 80:
                                    logger.warning("⚠️ Premium使用量已超过80%，请关注使用情况")
                            
                            return result
                    except aiohttp.ClientConnectorError as e:
                        if attempt < max_retries:
                            logger.warning(f"连接失败，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            continue
                            
                        error_msg = f"连接Cursor服务器失败: {str(e)}"
                        logger.error(error_msg)
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        return {"error": error_msg}
                    except asyncio.TimeoutError:
                        if attempt < max_retries:
                            logger.warning(f"请求超时，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            continue
                            
                        error_msg = "连接Cursor服务器超时"
                        logger.error(error_msg)
                        return {"error": error_msg}
                        
            except Exception as e:
                error_msg = f"获取Premium使用情况时发生错误: {str(e)}"
                logger.error(error_msg)
                logger.error(f"错误详情: {traceback.format_exc()}")
                return {"error": error_msg}

    async def get_subscription_info(self) -> Dict[str, Any]:
        """获取订阅信息，包括会员类型和试用期"""
        logger.info("=== 开始获取订阅信息 ===")
        max_retries = 2  # 减少最大重试次数，提高UI响应速度
        retry_delay = 1  # 减少重试间隔，提高响应速度
        third_api_failure_count = 0  # 第三个API连续失败次数
        
        for attempt in range(1, max_retries + 1):
            try:
                user_id = self.token.split('%3A%3A')[0]
                logger.debug(f"用户ID: {user_id}")
                
                async with aiohttp.ClientSession() as session:
                    try:
                        # 并发请求所有 API，减少超时时间提高响应速度
                        tasks = [
                            session.get("https://www.cursor.com/api/usage", params={"user": user_id}, headers=self.headers, ssl=self.ssl_context, timeout=8),
                            session.post("https://www.cursor.com/api/dashboard/teams", json={}, headers=self.headers, ssl=self.ssl_context, timeout=8),
                            session.post("https://www.cursor.com/api/dashboard/get-hard-limit", json={}, headers=self.headers, ssl=self.ssl_context, timeout=8)
                        ]

                        # 减少超时时间到8秒，提高UI响应速度
                        responses = await asyncio.gather(*tasks, return_exceptions=True)
                        
                        # 检查是否有异常
                        has_timeout_error = False
                        third_api_failed = False
                        first_two_succeeded = True
                        
                        for i, response in enumerate(responses):
                            if isinstance(response, Exception):
                                logger.error(f"API请求 {i+1} 失败: {str(response)}")
                                
                                # 检查是否是第三个API失败
                                if i == 2:
                                    third_api_failed = True
                                else:
                                    # 前两个API中有失败的
                                    first_two_succeeded = False
                                
                                if isinstance(response, asyncio.TimeoutError):
                                    has_timeout_error = True
                                elif isinstance(response, aiohttp.ClientConnectorError):
                                    if attempt < max_retries:
                                        logger.warning(f"连接失败，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                                        await asyncio.sleep(retry_delay)
                                        break  # 跳出当前循环，进入重试
                                    return {"error": "连接Cursor服务器失败，请检查网络连接"}
                                else:
                                    return {"error": f"获取订阅信息时发生错误: {str(response)}"}
                        
                        # 判断是否是"前两个成功，第三个失败"的情况
                        if first_two_succeeded and third_api_failed:
                            third_api_failure_count += 1
                            logger.warning(f"前两个API成功，第三个API失败 (连续 {third_api_failure_count}/{max_retries} 次)")
                        else:
                            # 重置计数器
                            third_api_failure_count = 0
                        
                        # 如果第三次连续"前两个成功，第三个失败"，使用默认值
                        if third_api_failure_count >= max_retries:
                            logger.warning(f"第三个API连续 {max_retries} 次失败，使用默认值(Pro用户)")
                            # 创建一个假的第三个API响应结果 (pro用户 - 允许usage-based)
                            limit_data = {"noUsageBasedAllowed": False, "hardLimit": None}
                        elif has_timeout_error and attempt < max_retries and not (first_two_succeeded and third_api_failed):
                            # 普通超时错误且不是"前两个成功，第三个失败"的情况，进行常规重试
                            logger.warning(f"请求超时，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            continue
                        # 仅当不是第三次"前两个成功第三个失败"且还有重试次数时才继续重试
                        elif has_timeout_error and attempt < max_retries:
                            logger.warning(f"请求超时，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            continue
                        elif has_timeout_error:
                            # 所有重试都失败，且不是特殊情况
                            return {"error": "连接Cursor服务器超时，请稍后再试"}
                        else:
                            # 处理正常响应
                            usage_data = await responses[0].json()
                            team_data = await responses[1].json()
                            if not third_api_failed:
                                limit_data = await responses[2].json()
                            else:
                                # 尝试一次单独请求第三个API
                                logger.info("尝试单独请求第三个API...")
                                try:
                                    async with session.post(
                                        "https://www.cursor.com/api/dashboard/get-hard-limit",
                                        json={},
                                        headers=self.headers,
                                        ssl=self.ssl_context,
                                        timeout=8  # 减少超时时间
                                    ) as response:
                                        if response.status == 200:
                                            limit_data = await response.json()
                                            logger.info("单独请求第三个API成功")
                                            third_api_failed = False
                                        else:
                                            # 继续使用默认值
                                            logger.warning(f"单独请求第三个API失败: HTTP {response.status}")
                                            if third_api_failure_count >= max_retries:
                                                limit_data = {"noUsageBasedAllowed": False, "hardLimit": None}
                                except Exception as e:
                                    logger.warning(f"单独请求第三个API异常: {str(e)}")
                                    if third_api_failure_count >= max_retries:
                                        limit_data = {"noUsageBasedAllowed": False, "hardLimit": None}
                        
                        # 判断会员类型
                        is_team_member = team_data.get('teams', []) and len(team_data['teams']) > 0
                        
                        # 当使用默认值时明确记录
                        if third_api_failure_count >= max_retries and third_api_failed:
                            has_usage_based = True  # 默认为Pro用户
                            logger.warning("使用默认值: 假设为Pro用户 (has_usage_based=True)")
                        else:
                            has_usage_based = not limit_data.get('noUsageBasedAllowed', True)
                        
                        # 新的试用期判断逻辑：通过stripe API获取
                        # 旧逻辑已失效，因为maxRequestUsage现在都是null
                        # 改为调用新的stripe API进行判断
                        stripe_result = await self._get_stripe_info()
                        is_trial = stripe_result.get('is_trial', False)
                        trial_days_remaining = stripe_result.get('days_remaining_on_trial', 0)
                        stripe_membership_type = stripe_result.get('membership_type', 'unknown')
                        
                        # 试用期剩余时间现在直接从stripe API获取
                        # 删除旧的计算逻辑，因为已经在上面从stripe API获取了
                        
                        # 优化的订阅类型判断逻辑 - 优先使用Stripe信息
                        subscription_type = 'free'
                        if stripe_membership_type == 'pro':
                            subscription_type = 'pro'
                        elif stripe_membership_type == 'free_trial' or is_trial:
                            subscription_type = 'trial'
                        elif is_team_member:
                            subscription_type = 'team'
                        elif has_usage_based:
                            subscription_type = 'pro'
                            
                        return {
                            "subscription_type": subscription_type,
                            "is_team_member": is_team_member,
                            "has_usage_based": has_usage_based,
                            "usage_limit": limit_data.get('hardLimit'),
                            "is_trial": is_trial,
                            "trial_days_remaining": trial_days_remaining
                        }
                        
                    except aiohttp.ClientConnectorError as e:
                        if attempt < max_retries:
                            logger.warning(f"连接失败，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            continue
                            
                        error_msg = f"连接Cursor服务器失败: {str(e)}"
                        logger.error(error_msg)
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        return {"error": error_msg}
                    except asyncio.TimeoutError:
                        if attempt < max_retries:
                            logger.warning(f"请求超时，将在{retry_delay}秒后重试 (尝试 {attempt}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            continue
                            
                        error_msg = "连接Cursor服务器超时"
                        logger.error(error_msg)
                        return {"error": error_msg}
                    
            except Exception as e:
                error_msg = f"获取订阅信息时发生错误: {str(e)}"
                logger.error(error_msg)
                logger.error(f"错误详情: {traceback.format_exc()}")
                return {"error": error_msg}

    async def get_usage_stats(self) -> Optional[UsageStats]:
        """获取使用统计信息"""
        try:
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.get(
                        f"{self.base_url}/usage",
                        headers={"Authorization": f"Bearer {self.token}"},
                        ssl=self.ssl_context,  # 使用SSL上下文
                        timeout=10  # 添加超时设置
                    ) as response:
                        if response.status != 200:
                            logger.error(f"获取使用统计失败: HTTP {response.status}")
                            return None
                            
                        data = await response.json()
                        return UsageStats(
                            email="",
                            trial_days=UsageMetric(
                                total=40,
                                used=data.get('trial_days', 0),
                                remaining=40 - data.get('trial_days', 0)
                            ),
                            premium_usage=UsageMetric(
                                total=1000,
                                used=data.get('premium_usage', 0),
                                remaining=1000 - data.get('premium_usage', 0)
                            ),
                            omini_usage=UsageMetric(
                                total=1000,
                                used=data.get('omini_usage', 0),
                                remaining=1000 - data.get('omini_usage', 0)
                            )
                        )
                except aiohttp.ClientConnectorError as e:
                    error_msg = f"连接Cursor服务器失败: {str(e)}"
                    logger.error(error_msg)
                    return None
                except asyncio.TimeoutError:
                    logger.error("连接Cursor服务器超时")
                    return None
        except Exception as e:
            logger.error(f"获取使用统计失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None

    async def _get_stripe_info(self) -> Dict[str, Any]:
        """获取Stripe订阅信息 - 新增方法"""
        logger.info("=== 开始获取Stripe订阅信息 ===")
        
        try:
            async with aiohttp.ClientSession() as session:
                url = "https://www.cursor.com/api/auth/stripe"
                logger.info(f"请求URL: {url}")
                
                async with session.get(
                    url,
                    headers=self.headers,
                    ssl=self.ssl_context,
                    timeout=15
                ) as response:
                    logger.info(f"Stripe API响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        logger.info("✅ Stripe订阅信息获取成功")
                        logger.debug(f"Stripe数据: {data}")
                        
                        membership_type = data.get("membershipType", "unknown")
                        is_trial = membership_type == "free_trial"
                        
                        return {
                            "is_trial": is_trial,
                            "membership_type": membership_type,
                            "days_remaining_on_trial": data.get("daysRemainingOnTrial", 0),
                            "verified_student": data.get("verifiedStudent", False),
                            "is_on_student_plan": data.get("isOnStudentPlan", False)
                        }
                    else:
                        logger.warning(f"Stripe API调用失败: HTTP {response.status}")
                        # 返回默认值，不中断流程
                        return {
                            "is_trial": False,
                            "membership_type": "unknown",
                            "days_remaining_on_trial": 0,
                            "verified_student": False,
                            "is_on_student_plan": False
                        }
                        
        except Exception as e:
            logger.warning(f"获取Stripe信息失败: {str(e)}")
            # 返回默认值，不中断流程
            return {
                "is_trial": False,
                "membership_type": "unknown", 
                "days_remaining_on_trial": 0,
                "verified_student": False,
                "is_on_student_plan": False
            }

    async def get_user_info(self) -> Dict[str, Any]:
        """获取用户基本信息 - 新增方法"""
        logger.info("=== 开始获取用户基本信息 ===")
        
        try:
            async with aiohttp.ClientSession() as session:
                url = "https://www.cursor.com/api/auth/me"
                logger.info(f"请求URL: {url}")
                
                async with session.get(
                    url,
                    headers=self.headers,
                    ssl=self.ssl_context,
                    timeout=15
                ) as response:
                    logger.info(f"用户信息API响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        logger.info("✅ 用户基本信息获取成功")
                        logger.debug(f"用户数据: {data}")
                        
                        return {
                            "success": True,
                            "email": data.get("email", ""),
                            "email_verified": data.get("email_verified", False),
                            "user_id": data.get("sub", ""),
                            "name": data.get("name", ""),
                            "updated_at": data.get("updated_at", "")
                        }
                    else:
                        logger.warning(f"用户信息API调用失败: HTTP {response.status}")
                        return {"success": False, "error": f"HTTP {response.status}"}
                        
        except Exception as e:
            logger.warning(f"获取用户信息失败: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def _get_cursor_db_path() -> str:
        """获取 Cursor 数据库路径"""
        logger.info("=== 开始获取Cursor数据库路径 ===")
        home = os.path.expanduser('~')
        logger.debug(f"用户主目录: {home}")
        
        if os.name == 'nt':  # Windows
            logger.info("检测到Windows系统")
            app_data = os.getenv('APPDATA', '')
            logger.debug(f"APPDATA目录: {app_data}")
            paths = [
                os.path.join(app_data, 'Cursor', 'User', 'globalStorage', 'state.vscdb'),
                os.path.join(app_data, 'Cursor Nightly', 'User', 'globalStorage', 'state.vscdb')
            ]
        elif platform.system() == "Darwin":  # 明确区分macOS
            logger.info("检测到macOS系统")
            paths = [
                os.path.join(home, 'Library', 'Application Support', 'Cursor', 'User', 'globalStorage', 'state.vscdb'),
                os.path.join(home, 'Library', 'Application Support', 'Cursor Nightly', 'User', 'globalStorage', 'state.vscdb')
            ]
        else:  # Linux
            logger.info("检测到Linux系统")
            paths = [
                os.path.join(home, '.config', 'Cursor', 'User', 'globalStorage', 'state.vscdb'),
                os.path.join(home, '.config', 'Cursor Nightly', 'User', 'globalStorage', 'state.vscdb')
            ]

        logger.info("开始检查可能的数据库路径...")
        for path in paths:
            logger.debug(f"检查路径: {path}")
            if os.path.exists(path):
                logger.info(f"找到数据库文件: {path}")
                try:
                    size = os.path.getsize(path)
                    logger.debug(f"数据库文件大小: {size} 字节")
                except Exception as e:
                    logger.warning(f"无法获取文件大小: {e}")
                return path

        error_msg = "未找到 Cursor 数据库文件"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    @staticmethod
    def get_cursor_token() -> Optional[str]:
        """从数据库获取 Cursor token"""
        logger.info("=== 开始获取Cursor Token ===")
        try:
            # 检查 jwt 模块是否可用
            if jwt is None:
                logger.error("PyJWT 库未安装，无法解析 token")
                return None
                
            db_path = CursorUsageTracker._get_cursor_db_path()
            logger.info(f"连接数据库: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            logger.debug("执行SQL查询获取token...")
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'cursorAuth/accessToken'")
            result = cursor.fetchone()
            conn.close()
            logger.info("数据库连接已关闭")

            if result:
                token = result[0]
                logger.debug(f"Token前缀: {token[:10]}...")
                logger.info("开始解析JWT token...")
                
                decoded = jwt.decode(token, options={"verify_signature": False})
                user_id = decoded['sub'].split('|')[1]
                logger.debug(f"解析出的用户ID: {user_id}")
                
                final_token = f"{user_id}%3A%3A{token}"
                logger.info("Token获取和处理成功")
                return final_token
                
            logger.warning("数据库中未找到token")
            return None
            
        except sqlite3.Error as e:
            logger.error(f"数据库操作错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None
        except Exception as e:
            logger.error(f"获取token时发生未知错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None

    @staticmethod
    def get_cursor_email() -> Optional[str]:
        """从数据库获取 Cursor 邮箱"""
        logger.info("=== 开始获取Cursor邮箱 ===")
        try:
            db_path = CursorUsageTracker._get_cursor_db_path()
            logger.info(f"连接数据库: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            logger.debug("执行SQL查询获取邮箱...")
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'cursorAuth/cachedEmail'")
            result = cursor.fetchone()
            conn.close()
            logger.info("数据库连接已关闭")

            if result:
                email = result[0]
                logger.info(f"成功获取到邮箱: {email}")
                return email
                
            logger.warning("数据库中未找到邮箱信息")
            return None
            
        except sqlite3.Error as e:
            logger.error(f"数据库操作错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None
        except Exception as e:
            logger.error(f"获取邮箱时发生未知错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None

def utc_to_china_timezone(utc_datetime):
    """
    将UTC时间转换为中国时区（UTC+8）时间
    
    Args:
        utc_datetime: UTC时区的datetime对象
        
    Returns:
        中国时区的datetime对象
    """
    china_timezone = timezone(timedelta(hours=8))
    # 如果输入的时间没有时区信息，假定它是UTC时间
    if utc_datetime.tzinfo is None:
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
    # 转换到中国时区
    return utc_datetime.astimezone(china_timezone)

async def main():
    max_retries = 2  # 整体流程的最大重试次数
    retry_delay = 3  # 重试间隔（秒）
    
    for attempt in range(1, max_retries + 1):
        try:
            # 获取token
            token = CursorUsageTracker.get_cursor_token()
            if not token:
                logger.error("错误: 未找到 Cursor token")
                return {"error": "未找到 Cursor token，请确保 Cursor 已正确安装并登录"}
    
            tracker = CursorUsageTracker(token)
            
            # 使用gather获取数据，但处理可能的异常
            results = await asyncio.gather(
                tracker.get_premium_usage(),
                tracker.get_subscription_info(),
                return_exceptions=True
            )
            
            # 检查是否有错误
            has_error = False
            error_msg = None
            
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"获取数据时发生异常: {str(result)}")
                    error_msg = f"连接Cursor服务器失败: {str(result)}"
                    has_error = True
                    break
                elif isinstance(result, dict) and "error" in result:
                    logger.error(f"获取数据时发生错误: {result['error']}")
                    error_msg = result["error"]
                    has_error = True
                    break
            
            # 如果有错误且还有重试机会，则重试
            if has_error and attempt < max_retries:
                logger.warning(f"数据获取失败，将在{retry_delay}秒后进行整体重试 (尝试 {attempt}/{max_retries})")
                await asyncio.sleep(retry_delay)
                continue
                
            # 如果有错误且已达到最大重试次数，则返回错误
            if has_error:
                return {"error": error_msg}
            
            usage, subscription = results
            
            # 使用新的API获取用户信息
            tracker = CursorUsageTracker(token)
            user_info_result = await tracker.get_user_info()
            email = user_info_result.get("email", "") if user_info_result.get("success") else CursorUsageTracker.get_cursor_email()
            
            # 构建返回结果
            result = {
                "email": email,
                "subscription_type": subscription['subscription_type'],
                "premium_usage": {
                    "current": usage['premium']['current'],
                    "limit": usage['premium']['limit']
                },
                "gpt4omini_usage": {
                    "current": usage['gpt4omini']['current'],
                    "limit": usage['gpt4omini']['limit']
                }
            }
            
            # 只有在有数字限制时才计算百分比
            if isinstance(usage['premium']['limit'], int) and usage['premium']['limit'] > 0:
                result["premium_usage"]["percent"] = (usage['premium']['current'] / usage['premium']['limit'] * 100)
            
            # 如果是试用期，添加试用期信息
            if subscription['is_trial']:
                result["trial_days_remaining"] = subscription['trial_days_remaining']
            
            # 添加用户验证信息
            if user_info_result.get("success"):
                result["email_verified"] = user_info_result.get("email_verified", False)
            
            logger.info("\nCursor 使用统计:")
            if email:
                logger.info(f"账号邮箱: {email}")
            logger.info(f"账户类型: {subscription['subscription_type'].upper()}")
            logger.info(f"Premium 请求数: {usage['premium']['current']}/{usage['premium']['limit']}")
            
            # 只有在有限制时才显示使用率
            if isinstance(usage['premium']['limit'], int) and usage['premium']['limit'] > 0:
                usage_percent = (usage['premium']['current'] / usage['premium']['limit'] * 100)
                logger.info(f"使用率: {usage_percent:.1f}%")
            
            if subscription['is_trial']:
                logger.info(f"试用期剩余天数: {subscription['trial_days_remaining']} 天")
            
            # 添加 GPT4o-Mini 使用情况
            logger.info("\n[GPT4o-Mini 使用情况]")
            logger.info(f"请求数: {usage['gpt4omini']['current']}/{usage['gpt4omini']['limit']}")
            
            return result
            
        except Exception as e:
            error_msg = f"获取Cursor数据时发生错误: {str(e)}"
            logger.error(error_msg)
            logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 如果还有重试机会，则重试
            if attempt < max_retries:
                logger.warning(f"发生异常，将在{retry_delay}秒后进行整体重试 (尝试 {attempt}/{max_retries})")
                await asyncio.sleep(retry_delay)
                continue
                
            return {"error": error_msg}

if __name__ == "__main__":
    # 配置日志
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    result = asyncio.run(main())
    if isinstance(result, dict) and "error" in result:
        print(f"错误: {result['error']}")
    else:
        print("✅ Cursor统计信息获取成功！")