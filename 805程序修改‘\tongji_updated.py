#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor使用统计追踪器 - 更新版本
使用新发现的有效API端点获取Cursor使用情况
"""

import os
import sqlite3
import requests
try:
    import jwt
except ImportError:
    print("PyJWT 库未安装，请运行 'pip install PyJWT' 安装")
    jwt = None
from typing import Optional, Dict, Any
from datetime import datetime, timedelta, timezone
import asyncio
import aiohttp
import ssl
from dataclasses import dataclass
import platform
import logging
import traceback

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

@dataclass
class UsageStats:
    email: str                      # 邮箱地址
    user_id: str                   # 用户ID
    membership_type: str           # 会员类型
    days_remaining_on_trial: int   # 试用期剩余天数
    email_verified: bool           # 邮箱是否验证
    verified_student: bool         # 是否学生认证
    is_on_student_plan: bool       # 是否学生计划
    gpt4_requests: int            # GPT-4请求数
    gpt35_requests: int           # GPT-3.5请求数
    start_of_month: str           # 月初时间

class CursorUsageTrackerUpdated:
    def __init__(self, token: str):
        logger.info("=== 初始化Cursor使用追踪器 (更新版) ===")
        self.token = token
        logger.debug(f"Token前缀: {token[:10]}...")
        
        self.headers = {
            "Cookie": f"WorkosCursorSessionToken={token}",
            "Authorization": f"Bearer {token}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        logger.debug("已设置请求头")
        
        # 创建SSL上下文，处理macOS的证书验证问题
        self.ssl_context = None
        if platform.system() == "Darwin":  # macOS
            logger.info("检测到macOS系统，配置SSL上下文...")
            self.ssl_context = ssl.create_default_context()
            self.ssl_context.check_hostname = False
            self.ssl_context.verify_mode = ssl.CERT_NONE
            logger.info("SSL上下文配置完成")

    async def get_auth_info(self) -> Dict[str, Any]:
        """获取用户认证信息和邮箱"""
        logger.info("=== 开始获取用户认证信息 ===")
        
        try:
            async with aiohttp.ClientSession() as session:
                url = "https://www.cursor.com/api/auth/me"
                logger.info(f"请求URL: {url}")
                
                async with session.get(
                    url,
                    headers=self.headers,
                    ssl=self.ssl_context,
                    timeout=15
                ) as response:
                    logger.info(f"响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        logger.info("✅ 用户认证信息获取成功")
                        logger.debug(f"认证信息: {data}")
                        
                        return {
                            "success": True,
                            "email": data.get("email"),
                            "email_verified": data.get("email_verified", False),
                            "user_id": data.get("sub"),
                            "name": data.get("name", ""),
                            "updated_at": data.get("updated_at")
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 获取认证信息失败: HTTP {response.status}")
                        logger.error(f"错误响应: {error_text}")
                        return {"success": False, "error": f"HTTP {response.status}: {error_text}"}
                        
        except Exception as e:
            error_msg = f"获取认证信息时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def get_subscription_info(self) -> Dict[str, Any]:
        """获取订阅信息，包括会员类型和试用期"""
        logger.info("=== 开始获取订阅信息 ===")
        
        try:
            async with aiohttp.ClientSession() as session:
                url = "https://www.cursor.com/api/auth/stripe"
                logger.info(f"请求URL: {url}")
                
                async with session.get(
                    url,
                    headers=self.headers,
                    ssl=self.ssl_context,
                    timeout=15
                ) as response:
                    logger.info(f"响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        logger.info("✅ 订阅信息获取成功")
                        logger.debug(f"订阅信息: {data}")
                        
                        membership_type = data.get("membershipType", "unknown")
                        days_remaining = data.get("daysRemainingOnTrial")
                        
                        # 判断订阅类型
                        if membership_type == "free_trial":
                            subscription_type = "trial"
                        elif membership_type in ["pro", "premium"]:
                            subscription_type = "pro"
                        elif membership_type == "team":
                            subscription_type = "team"
                        elif membership_type == "free":
                            subscription_type = "free"
                        else:
                            subscription_type = membership_type
                        
                        return {
                            "success": True,
                            "subscription_type": subscription_type,
                            "membership_type": membership_type,
                            "days_remaining_on_trial": days_remaining,
                            "verified_student": data.get("verifiedStudent", False),
                            "is_on_student_plan": data.get("isOnStudentPlan", False),
                            "is_trial": membership_type == "free_trial"
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 获取订阅信息失败: HTTP {response.status}")
                        logger.error(f"错误响应: {error_text}")
                        return {"success": False, "error": f"HTTP {response.status}: {error_text}"}
                        
        except Exception as e:
            error_msg = f"获取订阅信息时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def get_usage_info(self) -> Dict[str, Any]:
        """获取使用量信息"""
        logger.info("=== 开始获取使用量信息 ===")
        
        try:
            # 解析token获取用户ID
            user_id = None
            try:
                decoded = jwt.decode(self.token.split('%3A%3A')[-1], options={"verify_signature": False})
                user_id = decoded['sub'].split('|')[1]
                logger.debug(f"解析出的用户ID: {user_id}")
            except Exception as e:
                logger.warning(f"解析用户ID失败: {e}")
            
            async with aiohttp.ClientSession() as session:
                url = "https://www.cursor.com/api/usage"
                params = {"user": user_id} if user_id else {}
                logger.info(f"请求URL: {url}")
                logger.debug(f"请求参数: {params}")
                
                async with session.get(
                    url,
                    params=params,
                    headers=self.headers,
                    ssl=self.ssl_context,
                    timeout=15
                ) as response:
                    logger.info(f"响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        logger.info("✅ 使用量信息获取成功")
                        logger.debug(f"使用量信息: {data}")
                        
                        gpt4_usage = data.get("gpt-4", {})
                        gpt35_usage = data.get("gpt-3.5-turbo", {})
                        
                        return {
                            "success": True,
                            "gpt4_requests": gpt4_usage.get("numRequests", 0),
                            "gpt4_tokens": gpt4_usage.get("numTokens", 0),
                            "gpt4_limit": gpt4_usage.get("maxRequestUsage"),
                            "gpt35_requests": gpt35_usage.get("numRequests", 0),
                            "gpt35_tokens": gpt35_usage.get("numTokens", 0),
                            "gpt35_limit": gpt35_usage.get("maxRequestUsage"),
                            "start_of_month": data.get("startOfMonth")
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 获取使用量信息失败: HTTP {response.status}")
                        logger.error(f"错误响应: {error_text}")
                        return {"success": False, "error": f"HTTP {response.status}: {error_text}"}
                        
        except Exception as e:
            error_msg = f"获取使用量信息时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def get_all_stats(self) -> Optional[UsageStats]:
        """获取完整的使用统计信息"""
        logger.info("=== 开始获取完整统计信息 ===")
        
        try:
            # 并行请求所有API
            results = await asyncio.gather(
                self.get_auth_info(),
                self.get_subscription_info(),
                self.get_usage_info(),
                return_exceptions=True
            )
            
            auth_result, subscription_result, usage_result = results
            
            # 检查是否有错误
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"API请求 {i+1} 异常: {str(result)}")
                    return None
                elif isinstance(result, dict) and not result.get("success", False):
                    logger.error(f"API请求 {i+1} 失败: {result.get('error', 'Unknown error')}")
                    return None
            
            # 构建完整统计信息
            stats = UsageStats(
                email=auth_result.get("email", ""),
                user_id=auth_result.get("user_id", ""),
                membership_type=subscription_result.get("membership_type", "unknown"),
                days_remaining_on_trial=subscription_result.get("days_remaining_on_trial", 0),
                email_verified=auth_result.get("email_verified", False),
                verified_student=subscription_result.get("verified_student", False),
                is_on_student_plan=subscription_result.get("is_on_student_plan", False),
                gpt4_requests=usage_result.get("gpt4_requests", 0),
                gpt35_requests=usage_result.get("gpt35_requests", 0),
                start_of_month=usage_result.get("start_of_month", "")
            )
            
            logger.info("✅ 完整统计信息获取成功")
            return stats
            
        except Exception as e:
            logger.error(f"获取完整统计信息时发生错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None

    @staticmethod
    def _get_cursor_db_path() -> str:
        """获取 Cursor 数据库路径"""
        logger.info("=== 开始获取Cursor数据库路径 ===")
        home = os.path.expanduser('~')
        logger.debug(f"用户主目录: {home}")
        
        if os.name == 'nt':  # Windows
            logger.info("检测到Windows系统")
            app_data = os.getenv('APPDATA', '')
            logger.debug(f"APPDATA目录: {app_data}")
            paths = [
                os.path.join(app_data, 'Cursor', 'User', 'globalStorage', 'state.vscdb'),
                os.path.join(app_data, 'Cursor Nightly', 'User', 'globalStorage', 'state.vscdb')
            ]
        elif platform.system() == "Darwin":  # macOS
            logger.info("检测到macOS系统")
            paths = [
                os.path.join(home, 'Library', 'Application Support', 'Cursor', 'User', 'globalStorage', 'state.vscdb'),
                os.path.join(home, 'Library', 'Application Support', 'Cursor Nightly', 'User', 'globalStorage', 'state.vscdb')
            ]
        else:  # Linux
            logger.info("检测到Linux系统")
            paths = [
                os.path.join(home, '.config', 'Cursor', 'User', 'globalStorage', 'state.vscdb'),
                os.path.join(home, '.config', 'Cursor Nightly', 'User', 'globalStorage', 'state.vscdb')
            ]

        logger.info("开始检查可能的数据库路径...")
        for path in paths:
            logger.debug(f"检查路径: {path}")
            if os.path.exists(path):
                logger.info(f"找到数据库文件: {path}")
                return path

        error_msg = "未找到 Cursor 数据库文件"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    @staticmethod
    def get_cursor_token() -> Optional[str]:
        """从数据库获取 Cursor token"""
        logger.info("=== 开始获取Cursor Token ===")
        try:
            # 检查 jwt 模块是否可用
            if jwt is None:
                logger.error("PyJWT 库未安装，无法解析 token")
                return None
                
            db_path = CursorUsageTrackerUpdated._get_cursor_db_path()
            logger.info(f"连接数据库: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            logger.debug("执行SQL查询获取token...")
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'cursorAuth/accessToken'")
            result = cursor.fetchone()
            conn.close()
            logger.info("数据库连接已关闭")

            if result:
                token = result[0]
                logger.debug(f"Token前缀: {token[:10]}...")
                logger.info("开始解析JWT token...")
                
                decoded = jwt.decode(token, options={"verify_signature": False})
                user_id = decoded['sub'].split('|')[1]
                logger.debug(f"解析出的用户ID: {user_id}")
                
                final_token = f"{user_id}%3A%3A{token}"
                logger.info("Token获取和处理成功")
                return final_token
                
            logger.warning("数据库中未找到token")
            return None
            
        except sqlite3.Error as e:
            logger.error(f"数据库操作错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"获取token时发生未知错误: {str(e)}")
            return None

def format_usage_stats(stats: UsageStats) -> Dict[str, Any]:
    """格式化使用统计信息为易读的字典格式"""
    return {
        "email": stats.email,
        "user_id": stats.user_id,
        "membership_type": stats.membership_type,
        "account_status": {
            "email_verified": stats.email_verified,
            "verified_student": stats.verified_student,
            "is_on_student_plan": stats.is_on_student_plan
        },
        "trial_info": {
            "days_remaining": stats.days_remaining_on_trial if stats.membership_type == "free_trial" else None
        },
        "usage": {
            "gpt4_requests": stats.gpt4_requests,
            "gpt35_requests": stats.gpt35_requests,
            "start_of_month": stats.start_of_month
        }
    }

async def main():
    """主函数"""
    try:
        # 获取token
        token = CursorUsageTrackerUpdated.get_cursor_token()
        if not token:
            logger.error("错误: 未找到 Cursor token")
            return {"error": "未找到 Cursor token，请确保 Cursor 已正确安装并登录"}

        tracker = CursorUsageTrackerUpdated(token)
        
        # 获取完整统计信息
        stats = await tracker.get_all_stats()
        if not stats:
            return {"error": "获取用户统计信息失败"}
        
        # 格式化结果
        result = format_usage_stats(stats)
        
        # 输出统计信息
        logger.info("\n" + "="*50)
        logger.info("🎯 Cursor 使用统计 (更新版)")
        logger.info("="*50)
        logger.info(f"📧 邮箱: {stats.email}")
        logger.info(f"🔖 会员类型: {stats.membership_type.upper()}")
        
        if stats.membership_type == "free_trial":
            logger.info(f"📅 试用期剩余天数: {stats.days_remaining_on_trial} 天")
        
        logger.info(f"🤖 GPT-4 请求数: {stats.gpt4_requests}")
        logger.info(f"🤖 GPT-3.5 请求数: {stats.gpt35_requests}")
        logger.info(f"✉️ 邮箱验证: {'是' if stats.email_verified else '否'}")
        
        if stats.verified_student:
            logger.info(f"🎓 学生认证: 是")
        if stats.is_on_student_plan:
            logger.info(f"📚 学生计划: 是")
            
        return result
        
    except Exception as e:
        error_msg = f"获取Cursor数据时发生错误: {str(e)}"
        logger.error(error_msg)
        logger.error(f"错误详情: {traceback.format_exc()}")
        return {"error": error_msg}

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    result = asyncio.run(main())
    if isinstance(result, dict) and "error" in result:
        print(f"错误: {result['error']}")
    else:
        print("统计信息获取成功，请查看日志输出")