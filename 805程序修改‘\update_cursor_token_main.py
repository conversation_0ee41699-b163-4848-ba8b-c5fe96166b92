import json
import logging
import os
import platform
import sqlite3
import subprocess
import time
import re
import uuid
import secrets
import string
import random
import datetime
import base64
import hashlib
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import psutil
import requests
import traceback
import tkinter as tk
from tkinter import filedialog, messagebox, ttk, simpledialog
import hashlib
import sys
from importlib.util import spec_from_file_location, module_from_spec
import shutil

# 仅在Windows系统上导入win32com和tkinterdnd2
if platform.system() == "Windows":
    import win32com.client
    from tkinterdnd2 import DND_FILES, TkinterDnD

# 判断是否为开发环境
def is_development_mode():
    """
    检测当前是否处于开发模式（源代码运行）而非打包后的环境
    
    Returns:
        bool: 如果是开发模式返回True，否则返回False
    """
    # PyInstaller打包后，__file__变量会指向临时目录中的提取文件
    # 而sys.frozen属性会被设置
    return not getattr(sys, 'frozen', False)

# 全局日志显示控制
ENABLE_CONSOLE_LOGS = is_development_mode()  # 只在开发模式下启用控制台日志

# 配置日志
import os
from pathlib import Path

# 创建桌面日志文件路径
desktop_path = os.path.join(str(Path.home()), "Desktop")
log_file_path = os.path.join(desktop_path, "cursor_pro_log.txt")

# 临时禁用桌面日志文件输出
'''
# 配置日志处理器
file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s")
file_handler.setFormatter(file_formatter)
'''

# 配置模块级日志记录器（不添加额外的handler，避免重复输出）
logger = logging.getLogger(__name__)
# 使用main.py中已配置的日志系统，不重复添加handler

# 输出初始日志，标记程序开始
logger.info("="*50)
logger.info("程序启动")
logger.info(f"操作系统: {platform.system()} {platform.release()}")
logger.info(f"Python版本: {platform.python_version()}")
logger.info("桌面日志文件输出已禁用")
logger.info("="*50)

# 导入Cursor.py模块
try:
    cursor_finder_path = os.path.join(os.path.dirname(__file__), "Cursor.py")
    if os.path.exists(cursor_finder_path):
        spec = spec_from_file_location("cursor_finder", cursor_finder_path)
        cursor_finder = module_from_spec(spec)
        spec.loader.exec_module(cursor_finder)
        logger.info(f"成功导入Cursor.py: {cursor_finder_path}")
    else:
        logger.warning(f"未找到Cursor.py，路径: {cursor_finder_path}")
        cursor_finder = None
except Exception as e:
    logger.error(f"导入Cursor.py失败: {e}")
    logger.error(traceback.format_exc())
    cursor_finder = None

# 常量配置
class Config:
    """配置常量类"""
    PRIMARY_URL = "https://api2.naoy.me"
    BACKUP_URL = "https://api2.naoy.me"  # 修改备用服务器地址
    BASE_URL = PRIMARY_URL  # 默认使用主服务器
    LAST_SUCCESSFUL_URL = None  # 记录上次成功连接的服务器URL
    LAST_ERROR_MESSAGE = None  # 记录最近的错误信息
    API_URL = f"{BASE_URL}/api/cookie"
    ACCESS_CODE = ""
    PROCESS_TIMEOUT = 5
    CURSOR_PROCESS_NAMES = ['cursor.exe', 'cursor', 'Cursor']  # 增加 'Cursor' 适配Mac
    DB_KEYS = {
        'email': 'cursorAuth/cachedEmail',
        'access_token': 'cursorAuth/accessToken',
        'refresh_token': 'cursorAuth/refreshToken',
        'server_config': 'cursorai/serverConfig'
    }
    MIN_PATCH_VERSION = "0.45.0"
    VERSION_PATTERN = r"^\d+\.\d+\.\d+$"
    SUBPROCESS_KWARGS = {}
    CURSOR_PATH = None  # 存储Cursor安装路径
    
    # Cookie加密密钥 (必须与服务器端一致)
    COOKIE_ENCRYPTION_KEY = "cookiesecuritykey123456789012345678"
    
    @staticmethod
    def get_subprocess_kwargs():
        """根据不同平台返回subprocess参数"""
        if platform.system() == 'Windows':
            from subprocess import CREATE_NO_WINDOW
            return {'creationflags': CREATE_NO_WINDOW}
        return {}


@dataclass
class TokenData:
    """Token数据类"""
    mac_machine_id: str
    machine_id: str
    dev_device_id: str
    email: str
    token: str
    sqm_id: str
    service_machine_id: str

    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'TokenData':
        """从字典创建TokenData实例"""
        return cls(
            mac_machine_id=data['mac_machine_id'],
            machine_id=data['machine_id'],
            dev_device_id=data['dev_device_id'],
            email=data['email'],
            token=data['token'],
            sqm_id=data['sqm_id'],
            service_machine_id=data['service_machine_id']
        )


class FilePathManager:
    """文件路径管理器"""

    @staticmethod
    def resolve_shortcut(shortcut_path: str) -> Optional[str]:
        """
        解析Windows快捷方式(.lnk文件)获取目标路径
        在非Windows系统上，直接返回路径本身
        
        Args:
            shortcut_path: 快捷方式的路径
            
        Returns:
            Optional[str]: 目标路径，如果解析失败则返回None
        """
        # 非Windows系统直接返回路径
        if platform.system() != "Windows":
            return shortcut_path
            
        try:
            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(shortcut_path)
            return shortcut.Targetpath
        except Exception as e:
            logger.error(f"解析快捷方式失败: {e}")
            return None

    @staticmethod
    def update_cursor_path(path: Optional[Path]) -> None:
        """
        更新Cursor路径配置
        
        Args:
            path: Cursor安装目录路径，如果为None则清除保存的路径
        """
        try:
            # 更新Config类中的CURSOR_PATH
            Config.CURSOR_PATH = str(path) if path else None
            logger.info(f"已更新Config.CURSOR_PATH: {Config.CURSOR_PATH}")
            
            # 获取配置文件路径
            system_name = platform.system()
            if system_name == "Windows":
                config_dir = Path(os.getenv('LOCALAPPDATA')) / 'CursorPro'
            elif system_name == "Darwin":  # macOS
                config_dir = Path.home() / 'Library' / 'Application Support' / 'CursorPro'
            else:  # Linux
                config_dir = Path.home() / '.config' / 'CursorPro'
                
            config_path = config_dir / 'settings.dat'
            
            # 读取现有配置
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # 更新cursor_path，确保Mac系统上保存的是完整的.app路径
            if path:
                path_str = str(path)
                # 当在Mac系统上处理非.app路径时，尝试向上查找.app
                if system_name == "Darwin" and not path_str.endswith('.app'):
                    path_obj = Path(path_str)
                    # 检查是否是在.app内部路径
                    parts = path_obj.parts
                    for i, part in enumerate(parts):
                        if part.endswith('.app'):
                            # 找到了.app，重建路径到.app
                            app_path = Path(*parts[:i+1])
                            path_str = str(app_path)
                            logger.info(f"在Mac上检测到内部路径，已修正为.app路径: {path_str}")
                            break
                config['cursor_path'] = path_str
            else:
                config['cursor_path'] = None
            
            # 保存配置
            config_dir.mkdir(exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            logger.info(f"已保存Cursor路径到配置文件: {config_path}")
            
        except Exception as e:
            logger.error(f"更新Cursor路径配置失败: {e}")
            logger.error(traceback.format_exc())

    @staticmethod
    def prompt_user_cursor_directory_mac() -> Optional[Path]:
        """
        Mac系统专用的Cursor目录选择器
        提示用户选择Cursor.app安装目录
        如果配置文件中已有保存的路径，会优先使用该路径
        
        Returns:
            Optional[Path]: 用户选择的有效Cursor目录路径，如果用户取消则返回None
        """
        def validate_cursor_path(path: Path) -> bool:
            """验证路径是否为有效的Cursor目录"""
            try:
                # macOS上特别处理.app包
                if path.name.endswith('.app'):
                    app_path = path / "Contents" / "Resources" / "app"
                else:
                    app_path = path
                
                # 如果直接指定了app路径，则直接验证
                if (app_path / "package.json").exists():
                    logger.info(f"有效的Cursor路径: {path}")
                    return True
                    
                # 检查是否在.app内部路径
                if not path.name.endswith('.app'):
                    parts = path.parts
                    for i, part in enumerate(parts):
                        if part.endswith('.app'):
                            # 找到了.app，检查内部结构
                            app_path = Path(*parts[:i+1]) / "Contents" / "Resources" / "app"
                            if (app_path / "package.json").exists():
                                logger.info(f"找到有效的Cursor.app内部路径: {path} -> {app_path}")
                                return True
                
                logger.warning(f"无效的Cursor路径: {path}")
                return False
            except Exception as e:
                logger.error(f"验证路径时出错: {e}")
                return False

        # 首先检查配置文件中是否有保存的路径
        if Config.CURSOR_PATH is not None:
            saved_path = Path(Config.CURSOR_PATH)
            if validate_cursor_path(saved_path):
                logger.info(f"使用已保存的Cursor路径: {saved_path}")
                return saved_path
            else:
                logger.warning(f"已保存的Cursor路径无效: {saved_path}")
                # 清除无效的保存路径
                FilePathManager.update_cursor_path(None)

        logger.info("在macOS上使用Cursor.app选择器")
        try:
            # 直接使用简单的文件选择对话框，避免创建自定义窗口
            import tkinter as tk
            from tkinter import filedialog
            
            # 隐藏主窗口，不会显示在屏幕上
            root = tk.Tk()
            root.withdraw()
            
            # 显示一个简单的消息框，告知用户需要选择Cursor.app
            messagebox.showinfo("选择Cursor目录", "请选择Cursor目录或应用程序\n默认目录未找到")
            
            # 设置初始目录为Applications
            initial_dir = "/Applications"
            if not os.path.exists(initial_dir):
                initial_dir = os.path.expanduser("~")
            
            # 允许用户选择文件或目录
            cursor_path = None
            selection_type = messagebox.askyesno("选择类型", "是否选择目录？\n选择'是'打开目录选择器\n选择'否'打开文件选择器")
            
            if selection_type:  # 选择目录
                cursor_path = filedialog.askdirectory(
                    title="选择Cursor目录",
                    initialdir=initial_dir
                )
            else:  # 选择文件
                cursor_path = filedialog.askopenfilename(
                    title="选择Cursor文件",
                    initialdir=initial_dir
                )
            
            if not cursor_path:
                logger.info("用户取消选择Cursor路径")
                root.destroy()
                return None
                
            path = Path(cursor_path)
            
            # 验证选择的路径
            if validate_cursor_path(path):
                FilePathManager.update_cursor_path(path)
                root.destroy()
                return path
            else:
                messagebox.showerror("错误", "所选路径不是有效的Cursor目录")
            
            # 如果选择无效，重新选择
            root.destroy()
            return FilePathManager.prompt_user_cursor_directory_mac()
            
        except Exception as e:
            logger.error(f"macOS上选择Cursor.app失败: {e}")
            logger.error(traceback.format_exc())
            return None

    @staticmethod
    def prompt_user_cursor_directory_windows() -> Optional[Path]:
        """
        Windows系统专用的Cursor目录选择器
        支持拖放快捷方式或手动选择目录
        如果配置文件中已有保存的路径，会优先使用该路径
        
        Returns:
            Optional[Path]: 用户选择的有效Cursor目录路径，如果用户取消则返回None
        """
        def validate_cursor_path(path: Path) -> bool:
            """验证路径是否为有效的Cursor目录"""
            app_path = path / "resources" / "app"
            return app_path.exists() and (app_path / "package.json").exists()

        # 首先检查配置文件中是否有保存的路径
        if Config.CURSOR_PATH is not None:
            saved_path = Path(Config.CURSOR_PATH)
            if validate_cursor_path(saved_path):
                logger.info(f"使用已保存的Cursor路径: {saved_path}")
                return saved_path
            else:
                logger.warning(f"已保存的Cursor路径无效: {saved_path}")
                # 清除无效的保存路径
                FilePathManager.update_cursor_path(None)

        def handle_drop(event):
            """处理文件拖放事件"""
            # 获取拖放的文件路径
            files = window.tk.splitlist(event.data)
            if not files:
                return
                
            file_path = files[0]  # 只处理第一个文件
            
            if file_path.lower().endswith('.lnk'):
                # 解析快捷方式
                target_path = FilePathManager.resolve_shortcut(file_path)
                if target_path:
                    cursor_path = Path(target_path).parent
                    if validate_cursor_path(cursor_path):
                        window.selected_path = cursor_path
                        # 保存有效的路径到配置
                        FilePathManager.update_cursor_path(cursor_path)
                        window.destroy()
                        return
                messagebox.showerror("错误", "无法解析快捷方式或目标不是有效的Cursor程序")
            elif file_path.lower().endswith('.exe'):
                # 如果是exe文件，使用其父目录
                cursor_path = Path(file_path).parent
                if validate_cursor_path(cursor_path):
                    window.selected_path = cursor_path
                    # 保存有效的路径到配置
                    FilePathManager.update_cursor_path(cursor_path)
                    window.destroy()
                    return
                messagebox.showerror("错误", "选择的程序所在目录不是有效的Cursor安装目录")
            else:
                messagebox.showerror("错误", "请拖放Cursor的快捷方式或可执行文件")

        def browse_directory():
            """浏览目录按钮回调函数"""
            # 获取桌面路径
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            
            # 定义文件类型（优先显示快捷方式）
            file_types = [
                ("Cursor快捷方式", "*.lnk"),
                ("Cursor程序", "*.exe"),
                ("所有Cursor文件", "*.lnk;*.exe"),
                ("所有文件", "*.*")
            ]

            # 打开文件选择对话框
            cursor_file = filedialog.askopenfilename(
                title="选择Cursor桌面快捷方式（推荐）或程序文件",
                filetypes=file_types,
                initialdir=desktop_path  # 设置初始目录为桌面
            )
            
            if cursor_file:
                if cursor_file.lower().endswith('.lnk'):
                    # 解析快捷方式
                    target_path = FilePathManager.resolve_shortcut(cursor_file)
                    if target_path:
                        cursor_path = Path(target_path).parent
                        if validate_cursor_path(cursor_path):
                            window.selected_path = cursor_path
                            # 保存有效的路径到配置
                            FilePathManager.update_cursor_path(cursor_path)
                            window.destroy()
                            return
                    messagebox.showerror("错误", "无法解析快捷方式或目标不是有效的Cursor程序")
                elif cursor_file.lower().endswith('.exe'):
                    # 如果是exe文件，使用其父目录
                    cursor_path = Path(cursor_file).parent
                    if validate_cursor_path(cursor_path):
                        window.selected_path = cursor_path
                        # 保存有效的路径到配置
                        FilePathManager.update_cursor_path(cursor_path)
                        window.destroy()
                        return
                    messagebox.showerror("错误", "选择的程序所在目录不是有效的Cursor安装目录")
                else:
                    messagebox.showerror("错误", "请选择Cursor的快捷方式或可执行文件")

        def on_closing():
            """窗口关闭回调函数"""
            logger.info("用户关闭选择目录窗口")
            window.selected_path = None
            window.destroy()

        # 创建主窗口（不使用拖放功能）
        logger.info("开始创建选择目录窗口")
        try:
            window = tk.Tk()
            logger.debug("成功创建窗口")
            window.title("选择Cursor目录")
            window.geometry("400x500")
            window.selected_path = None
        except Exception as e:
            logger.error(f"创建窗口失败: {e}")
            raise

        # 创建说明标签
        logger.debug("创建界面元素")
        try:
            # 创建普通说明标签
            label_normal = ttk.Label(
                window,
                text='默认cursor目录未找到\n手动选择当前安装目录\n--------------------------------------------------------',
                justify=tk.CENTER
            )
            label_normal.pack(pady=(20, 0))

            # 创建主要提示标签
            label_main_tip = ttk.Label(
                window,
                text='请选择Cursor的桌面快捷方式',
                justify=tk.CENTER,
                font=('Arial', 12, 'bold'),
                foreground='#0066CC'  # 使用蓝色突出显示
            )
            label_main_tip.pack(pady=(15, 5))

            # 创建详细说明标签
            label_detail = ttk.Label(
                window,
                text='通常位于桌面上的"Cursor.lnk"快捷方式文件\n程序会自动识别快捷方式指向的安装目录',
                justify=tk.CENTER,
                font=('Arial', 9),
                foreground='#666666'
            )
            label_detail.pack(pady=(0, 10))

            # 创建中间的空白区域
            info_frame = ttk.Frame(window)
            info_frame.pack(expand=True, fill='both', pady=20, padx=20)

            # 在中间区域添加说明文字
            center_label = ttk.Label(
                info_frame,
                text="\n点击下方按钮选择快捷方式\n支持 .lnk 和 .exe 文件\n\n",
                justify=tk.CENTER,
                font=('Arial', 11)
            )
            center_label.pack(expand=True)

            # 创建"浏览"按钮
            browse_button = ttk.Button(
                window,
                text="选择Cursor桌面快捷方式",
                command=browse_directory
            )
            browse_button.pack(pady=(0, 20))

            logger.debug("界面元素创建成功")

        except Exception as e:
            logger.error(f"创建界面元素时出错: {e}")
            logger.error(traceback.format_exc())
            raise

        # 设置窗口关闭事件
        window.protocol("WM_DELETE_WINDOW", on_closing)
        logger.debug("窗口关闭事件设置成功")

        # 运行窗口
        logger.info("开始运行选择目录窗口")
        window.mainloop()
        logger.info("选择目录窗口已关闭")

        if window.selected_path:
            logger.info(f"用户选择的Cursor路径: {window.selected_path}")
        else:
            logger.info("用户未选择Cursor路径")

        return window.selected_path

    @staticmethod
    def prompt_user_cursor_directory() -> Optional[Path]:
        """
        根据操作系统选择合适的目录选择方法
        
        Returns:
            Optional[Path]: 用户选择的有效Cursor目录路径，如果用户取消则返回None
        """
        system = platform.system()
        
        if system == "Darwin":  # macOS
            return FilePathManager.prompt_user_cursor_directory_mac()
        elif system == "Windows":
            return FilePathManager.prompt_user_cursor_directory_windows()
        else:  # Linux或其他系统
            # 使用简单的目录选择对话框
            root = tk.Tk()
            root.withdraw()
            cursor_dir = filedialog.askdirectory(title="选择Cursor安装目录")
            if not cursor_dir:
                return None
                
            # Linux系统验证目录有效性
            cursor_path = Path(cursor_dir)
            app_path = cursor_path / "resources" / "app"
            if app_path.exists() and (app_path / "package.json").exists():
                FilePathManager.update_cursor_path(cursor_path)
                return cursor_path
            else:
                messagebox.showerror("错误", "选择的目录不是有效的Cursor安装目录")
                return None

    @staticmethod
    def get_storage_path() -> Path:
        """获取storage.json文件路径"""
        logger.info("=== 开始获取storage.json路径 ===")
        system = platform.system()
        logger.info(f"当前操作系统: {system}")
        
        try:
            if system == "Windows":
                path = Path(os.getenv('APPDATA')) / 'Cursor' / 'User' / 'globalStorage' / 'storage.json'
            elif system == "Darwin":
                path = Path.home() / 'Library' / 'Application Support' / 'Cursor' / 'User' / 'globalStorage' / 'storage.json'
            elif system == "Linux":
                path = Path.home() / '.config' / 'Cursor' / 'User' / 'globalStorage' / 'storage.json'
            else:
                error_msg = f"当前系统{system}不被支持"
                logger.error(error_msg)
                raise OSError(error_msg)
            
            logger.info(f"确定的文件路径: {path}")
            logger.info(f"文件是否存在: {path.exists()}")
            if path.exists():
                try:
                    file_size = path.stat().st_size
                    logger.debug(f"文件大小: {file_size} 字节")
                except Exception as e:
                    logger.warning(f"无法获取文件大小: {e}")
            
            return path
            
        except Exception as e:
            logger.error(f"获取storage.json路径时出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise

    @staticmethod
    def get_db_path() -> Path:
        """获取Cursor状态数据库路径"""
        system = platform.system()
        logger.info(f"当前操作系统: {system} {platform.release()}")
        
        if system == "Windows":
            # 尝试多个可能的路径
            possible_paths = [
                Path(os.getenv("APPDATA", "")) / "Cursor" / "User" / "globalStorage" / "state.vscdb",
                Path(os.getenv("APPDATA", "")) / "Code" / "User" / "globalStorage" / "state.vscdb"
            ]
            
            # 检查哪个路径存在
            for path in possible_paths:
                if path.exists():
                    logger.info(f"找到数据库文件: {path}")
                    db_path = path
                    break
            else:
                # 如果都不存在，使用默认路径
                db_path = possible_paths[0]
                logger.warning(f"未找到数据库文件，将使用默认路径: {db_path}")
        elif system == "Darwin":  # macOS
            # 尝试多个可能的路径
            possible_paths = [
                Path(os.path.expanduser("~")) / "Library" / "Application Support" / "Cursor" / "User" / "globalStorage" / "state.vscdb",
                Path(os.path.expanduser("~")) / "Library" / "Application Support" / "Code" / "User" / "globalStorage" / "state.vscdb"
            ]
            
            # 检查哪个路径存在
            for path in possible_paths:
                if path.exists():
                    logger.info(f"找到数据库文件: {path}")
                    db_path = path
                    break
            else:
                # 如果都不存在，使用默认路径
                db_path = possible_paths[0]
                logger.warning(f"未找到数据库文件，将使用默认路径: {db_path}")
        elif system == "Linux":
            # 尝试多个可能的路径
            possible_paths = [
                Path(os.path.expanduser("~")) / ".config" / "Cursor" / "User" / "globalStorage" / "state.vscdb",
                Path(os.path.expanduser("~")) / ".config" / "Code" / "User" / "globalStorage" / "state.vscdb"
            ]
            
            # 检查哪个路径存在
            for path in possible_paths:
                if path.exists():
                    logger.info(f"找到数据库文件: {path}")
                    db_path = path
                    break
            else:
                # 如果都不存在，使用默认路径
                db_path = possible_paths[0]
                logger.warning(f"未找到数据库文件，将使用默认路径: {db_path}")
        else:
            raise OSError(f"当前操作系统{system}不被支持")
        
        logger.info(f"数据库路径: {db_path}")
        logger.info(f"数据库文件存在: {db_path.exists()}")
        return db_path

    @staticmethod
    def prompt_user_select_cursor(cursor_programs):
        """
        在找到多个Cursor安装时，提示用户选择要使用的Cursor
        
        Args:
            cursor_programs: 包含Cursor程序信息的字典列表
            
        Returns:
            选择的Cursor程序信息，如果用户未选择则返回None，
            如果用户选择"其他目录"则返回特殊标记"OTHER_DIRECTORY"
        """
        try:
            logger.info(f"提示用户从 {len(cursor_programs)} 个Cursor安装中选择")
            
            # 创建选择窗口
            root = tk.Tk()
            root.title("选择Cursor安装")
            root.geometry("600x400")
            
            # 准备选择信息
            frame = ttk.Frame(root)
            frame.pack(fill="both", expand=True, padx=10, pady=10)
            
            # 头部信息
            if len(cursor_programs) == 1:
                header_text = "检测到1个Cursor安装，请确认使用该安装或选择其他目录:"
            else:
                header_text = f"检测到系统中有 {len(cursor_programs)} 个Cursor安装，请选择要使用的:"
                
            header = ttk.Label(frame, text=header_text, wraplength=580)
            header.pack(pady=(0, 10))
            
            # 创建列表框
            listbox_frame = ttk.Frame(frame)
            listbox_frame.pack(fill="both", expand=True)
            
            scrollbar = ttk.Scrollbar(listbox_frame)
            scrollbar.pack(side="right", fill="y")
            
            listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, font=("Courier New", 10), height=10)
            listbox.pack(side="left", fill="both", expand=True)
            
            scrollbar.config(command=listbox.yview)
            
            # 添加选项
            for idx, program in enumerate(cursor_programs, 1):
                install_location = program.get("安装位置", "未知")
                version = program.get("版本", "未知")
                
                # 检查安装路径是否有效
                is_valid = False
                if install_location and install_location != "未知":
                    try:
                        is_valid = cursor_finder.validate_cursor_installation(install_location)
                    except:
                        pass
                
                status = "有效" if is_valid else "无效"
                default_mark = " (默认安装)" if program.get("默认安装", False) else ""
                listbox.insert(tk.END, f"{idx}. {program['名称']}{default_mark}")
                listbox.insert(tk.END, f"    版本: {version}, 状态: {status}")
                listbox.insert(tk.END, f"    路径: {install_location}")
                listbox.insert(tk.END, "")  # 空行分隔
                
            # 添加其他目录选项
            listbox.insert(tk.END, f"{len(cursor_programs) + 1}. 选择其他目录...")
            
            # 选择初始项
            listbox.selection_set(0)
            
            # 按钮框
            button_frame = ttk.Frame(frame)
            button_frame.pack(fill="x", pady=10)
            
            selected_index = [-1]  # 使用列表存储，以便在回调中修改
            
            def on_select():
                # 获取选择的索引（总是选取实际的项目索引，每个安装占4行）
                if listbox.curselection():
                    sel_idx = listbox.curselection()[0]
                    # 计算实际的安装索引（每4行对应一个安装）
                    real_idx = sel_idx // 4
                    if real_idx < len(cursor_programs):
                        # 选择了一个现有安装
                        selected_index[0] = real_idx
                    else:
                        # 选择了"其他目录"
                        selected_index[0] = -2  # 特殊标记
                else:
                    selected_index[0] = -1
                root.destroy()
            
            def on_cancel():
                selected_index[0] = -1
                root.destroy()
            
            def on_double_click(event):
                on_select()
            
            listbox.bind("<Double-1>", on_double_click)
            
            select_button = ttk.Button(button_frame, text="确定", command=on_select)
            select_button.pack(side="right", padx=5)
            
            cancel_button = ttk.Button(button_frame, text="取消", command=on_cancel)
            cancel_button.pack(side="right", padx=5)
            
            # 设置窗口关闭事件
            root.protocol("WM_DELETE_WINDOW", on_cancel)
            
            # 居中显示窗口
            root.update_idletasks()
            width = root.winfo_width()
            height = root.winfo_height()
            x = (root.winfo_screenwidth() // 2) - (width // 2)
            y = (root.winfo_screenheight() // 2) - (height // 2)
            root.geometry(f"{width}x{height}+{x}+{y}")
            
            # 运行窗口
            root.mainloop()
            
            # 处理选择结果
            if selected_index[0] >= 0:
                # 用户选择了一个安装
                selected_cursor = cursor_programs[selected_index[0]]
                logger.info(f"用户选择了Cursor: {selected_cursor['名称']}")
                return selected_cursor
            elif selected_index[0] == -2:
                # 用户选择了"其他目录"
                logger.info("用户选择了'其他目录'选项")
                return "OTHER_DIRECTORY"
            else:
                # 用户取消了选择
                logger.info("用户取消选择Cursor")
                return None
            
        except Exception as e:
            logger.error(f"选择Cursor时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    @staticmethod
    def get_cursor_app_paths() -> Tuple[Path, Path]:
        """获取Cursor应用相关路径"""
        system = platform.system()
        logger.info(f"开始获取Cursor应用路径，当前系统：{system}")

        try:
            # 首先检查Config.CURSOR_PATH是否已经设置且有效
            if Config.CURSOR_PATH:
                cursor_path = Path(Config.CURSOR_PATH)
                logger.info(f"检查已设置的Cursor路径: {cursor_path}")

                # 验证路径是否有效
                if system == "Windows":
                    base_path = cursor_path / "resources" / "app"
                elif system == "Darwin":
                    if cursor_path.name.endswith('.app'):
                        base_path = cursor_path / "Contents" / "Resources" / "app"
                    else:
                        base_path = cursor_path
                else:  # Linux
                    base_path = cursor_path / "resources" / "app"

                pkg_path = base_path / "package.json"
                main_path = base_path / "out" / "main.js"

                # 检查关键文件是否存在
                if pkg_path.exists() and main_path.exists():
                    logger.info(f"使用已设置的有效Cursor路径: {cursor_path}")
                    logger.info(f"包路径: {pkg_path}")
                    logger.info(f"主文件路径: {main_path}")
                    return pkg_path, main_path
                else:
                    logger.warning(f"已设置的Cursor路径无效，关键文件不存在: {cursor_path}")
                    # 清除无效路径
                    Config.CURSOR_PATH = None

            if system == "Windows":
                # 检查是否有Cursor.py模块可用
                if cursor_finder is not None:
                    # 使用Cursor.py查找所有安装的Cursor程序
                    logger.info("使用Cursor.py查找所有安装的Cursor程序")
                    all_cursors = cursor_finder.find_cursor_installations()
                    logger.info(f"找到 {len(all_cursors)} 个Cursor安装")
                    
                    # 过滤出安装位置有效的Cursor
                    valid_cursors = []
                    for cursor in all_cursors:
                        install_location = cursor.get("安装位置", "")
                        if install_location and cursor_finder.validate_cursor_installation(install_location):
                            valid_cursors.append(cursor)
                    
                    logger.info(f"有效的Cursor安装数量: {len(valid_cursors)}")
                    
                    if len(valid_cursors) == 0:
                        # 没有找到有效的Cursor安装，提示用户手动选择目录
                        logger.warning("未找到任何有效的Cursor安装，提示用户手动选择")
                        if selected_path := FilePathManager.prompt_user_cursor_directory():
                            # 规范化路径
                            try:
                                selected_path = selected_path.resolve()
                            except Exception as e:
                                logger.error(f"规范化路径时出错: {e}")
                                
                            base_path = selected_path / "resources" / "app"
                            # 保存有效路径到配置
                            FilePathManager.update_cursor_path(selected_path)
                        else:
                            raise OSError("用户取消选择Cursor目录")
                    
                    elif len(valid_cursors) == 1:
                        # 找到一个有效安装，提示用户确认或选择其他
                        cursor_choice = FilePathManager.prompt_user_select_cursor(valid_cursors)
                        
                        if cursor_choice is None:
                            # 用户取消了选择
                            raise OSError("用户取消选择Cursor目录")
                        elif cursor_choice == "OTHER_DIRECTORY":
                            # 用户选择了其他目录
                            logger.info("用户选择手动指定其他Cursor目录")
                            if selected_path := FilePathManager.prompt_user_cursor_directory():
                                # 规范化路径
                                try:
                                    selected_path = selected_path.resolve()
                                except Exception as e:
                                    logger.error(f"规范化路径时出错: {e}")
                                    
                                base_path = selected_path / "resources" / "app"
                                # 保存有效路径到配置
                                FilePathManager.update_cursor_path(selected_path)
                            else:
                                raise OSError("用户取消选择Cursor目录")
                        else:
                            # 用户确认使用找到的安装
                            install_location = cursor_choice.get("安装位置", "")
                            logger.info(f"用户确认使用找到的Cursor安装: {install_location}")
                            path = Path(install_location)
                            # 规范化路径
                            try:
                                path = path.resolve()
                            except Exception as e:
                                logger.error(f"规范化路径时出错: {e}")
                                
                            base_path = path / "resources" / "app"
                            # 保存有效路径到配置
                            FilePathManager.update_cursor_path(path)
                    
                    else:
                        # 找到多个有效的Cursor安装，提示用户选择
                        logger.info(f"找到 {len(valid_cursors)} 个有效的Cursor安装，提示用户选择")
                        cursor_choice = FilePathManager.prompt_user_select_cursor(valid_cursors)
                        
                        if cursor_choice is None:
                            # 用户取消了选择
                            raise OSError("用户取消选择Cursor目录")
                        elif cursor_choice == "OTHER_DIRECTORY":
                            # 用户选择了其他目录
                            logger.info("用户选择手动指定其他Cursor目录")
                            if selected_path := FilePathManager.prompt_user_cursor_directory():
                                # 规范化路径
                                try:
                                    selected_path = selected_path.resolve()
                                except Exception as e:
                                    logger.error(f"规范化路径时出错: {e}")
                                    
                                base_path = selected_path / "resources" / "app"
                                # 保存有效路径到配置
                                FilePathManager.update_cursor_path(selected_path)
                            else:
                                raise OSError("用户取消选择Cursor目录")
                        else:
                            # 用户选择了一个安装
                            install_location = cursor_choice.get("安装位置", "")
                            logger.info(f"用户选择的Cursor安装: {install_location}")
                            path = Path(install_location)
                            # 规范化路径
                            try:
                                path = path.resolve()
                            except Exception as e:
                                logger.error(f"规范化路径时出错: {e}")
                                
                            base_path = path / "resources" / "app"
                            # 保存有效路径到配置
                            FilePathManager.update_cursor_path(path)
                else:
                    # Cursor.py不可用，使用原有逻辑
                    logger.warning("Cursor.py不可用，使用默认查找逻辑")
                    if selected_path := FilePathManager.prompt_user_cursor_directory():
                        # 规范化路径
                        try:
                            selected_path = selected_path.resolve()
                        except Exception as e:
                            logger.error(f"规范化路径时出错: {e}")
                            
                        base_path = selected_path / "resources" / "app"
                        # 保存有效路径到配置
                        FilePathManager.update_cursor_path(selected_path)
                    else:
                        raise OSError("用户取消选择Cursor目录")
                            
            elif system == "Darwin":
                # 设置正确的默认路径
                default_path = Path("/Applications/Cursor.app/Contents/Resources/app")
                base_path = default_path
                logger.info(f"Mac系统默认路径: {default_path}")
                
                if not base_path.exists():
                    logger.warning(f"默认位置未找到Cursor目录: {default_path}")
                    
                    # 尝试在用户目录下查找，同样设置为不存在的路径
                    user_app_path = Path.home() / "Applications" / "Cursor.app" / "Contents" / "Resources" / "app"
                    if user_app_path.exists():
                        logger.info(f"在用户目录找到Cursor: {user_app_path}")
                        base_path = user_app_path
                    else:
                        # 提示用户手动选择目录
                        logger.info("尝试让用户手动选择Cursor.app位置")
                        selected_path = None
                        try:
                            selected_path = FilePathManager.prompt_user_cursor_directory()
                        except Exception as e:
                            logger.error(f"选择Cursor.app路径时出错: {str(e)}")
                            logger.error(traceback.format_exc())
                            # 再次尝试，使用系统文件对话框
                            try:
                                import subprocess
                                result = subprocess.run(
                                    ["osascript", "-e", 'tell application "System Events" to POSIX path of (choose file with prompt "请选择Cursor.app" of type {"APPL"})'],
                                    capture_output=True, text=True
                                )
                                if result.returncode == 0 and result.stdout.strip():
                                    selected_path = Path(result.stdout.strip())
                                    logger.info(f"通过AppleScript选择的路径: {selected_path}")
                            except Exception as e2:
                                logger.error(f"使用AppleScript选择文件失败: {str(e2)}")
                        
                        if selected_path:
                            logger.info(f"用户选择的路径: {selected_path}")
                            # 对于macOS，如果选择的是.app文件，需要进一步定位到Resources/app
                            if selected_path.name.endswith('.app'):
                                logger.info(f"选择的是.app文件: {selected_path}")
                                base_path = selected_path / "Contents" / "Resources" / "app"
                            else:
                                # 如果已经是内部路径，直接使用
                                logger.info(f"选择的是内部路径: {selected_path}")
                                base_path = selected_path
                        else:
                            raise OSError("用户取消选择Cursor目录")
            elif system == "Linux":
                # 检查可能的Linux安装路径
                possible_paths = [
                    Path("/opt/Cursor/resources/app"),
                    Path("/usr/share/cursor/resources/app")
                ]
                base_path = next((p for p in possible_paths if p.exists()), None)
                if not base_path:
                    raise OSError("无法定位Linux系统下的Cursor程序目录")
            else:
                raise OSError(f"当前系统类型 {system} 暂不支持")
            
            pkg_path = base_path / "package.json"
            main_path = base_path / "out" / "main.js"
            
            logger.info(f"最终确定的包路径: {pkg_path}")
            logger.info(f"最终确定的主文件路径: {main_path}")
            
            # 检查文件是否存在
            if not pkg_path.exists():
                logger.error(f"包文件不存在: {pkg_path}")
                raise FileNotFoundError(f"包文件不存在: {pkg_path}")
                
            if not main_path.exists():
                logger.error(f"主文件不存在: {main_path}")
                raise FileNotFoundError(f"主文件不存在: {main_path}")
                
            return pkg_path, main_path
        
        except Exception as e:
            logger.error(f"获取Cursor应用路径时出错: {str(e)}")
            logger.error(traceback.format_exc())
            raise


class FilePermissionManager:
    """文件权限管理器"""

    @staticmethod
    def make_file_writable(file_path: Union[str, Path]) -> None:
        """修改文件权限为可写"""
        file_path = Path(file_path)
        if platform.system() == "Windows":
            subprocess.run(['attrib', '-R', str(file_path)], check=True, **Config.get_subprocess_kwargs())
        else:
            os.chmod(file_path, 0o666)

    @staticmethod
    def make_file_readonly(file_path: Union[str, Path]) -> None:
        """修改文件权限为只读"""
        file_path = Path(file_path)
        if platform.system() == "Windows":
            subprocess.run(['attrib', '+R', str(file_path)], check=True, **Config.get_subprocess_kwargs())
        else:
            os.chmod(file_path, 0o444)


class CursorAuthManager:
    """Cursor认证信息管理器"""

    def __init__(self):
        self.db_path = FilePathManager.get_db_path()

    def update_auth(self, email: Optional[str] = None,
                    access_token: Optional[str] = None,
                    refresh_token: Optional[str] = None) -> bool:
        """更新或插入Cursor的认证信息"""
        updates: List[Tuple[str, str]] = []
        if email is not None:
            updates.append((Config.DB_KEYS['email'], email))
        if access_token is not None:
            updates.append((Config.DB_KEYS['access_token'], access_token))
        if refresh_token is not None:
            updates.append((Config.DB_KEYS['refresh_token'], refresh_token))

        if not updates:
            logger.info("未检测到需要更新的数据")
            return False

        try:
            # 获取数据库文件的权限
            try:
                file_stat = os.stat(self.db_path)
                logger.info(f"数据库文件权限: {oct(file_stat.st_mode)}")
            except Exception as e:
                logger.warning(f"无法获取数据库文件权限: {e}")

            # 在Mac系统上尝试修改文件权限
            if platform.system() == "Darwin":
                try:
                    os.chmod(self.db_path, 0o666)
                    logger.info("已修改数据库文件权限为可读写")
                except Exception as e:
                    logger.warning(f"修改数据库文件权限失败: {e}")

            # 连接数据库
            logger.info(f"尝试连接数据库: {self.db_path}")
            
            # 添加重试机制
            max_retries = 3
            retry_count = 0
            last_error = None
            
            while retry_count < max_retries:
                try:
                    with sqlite3.connect(self.db_path) as conn:
                        cursor = conn.cursor()
                        
                        # 检查数据库结构
                        logger.info("检查数据库结构...")
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable'")
                        table_exists = cursor.fetchone() is not None
                        logger.info(f"ItemTable表是否存在: {table_exists}")
                        
                        # 如果表不存在，创建表
                        if not table_exists:
                            try:
                                logger.info("表不存在，尝试创建ItemTable表")
                                cursor.execute("""
                                CREATE TABLE IF NOT EXISTS ItemTable (
                                    key TEXT PRIMARY KEY,
                                    value TEXT
                                )
                                """)
                                conn.commit()
                                logger.info("成功创建ItemTable表")
                            except Exception as e:
                                logger.error(f"创建ItemTable表失败: {e}")
                                raise
                        
                        # 执行更新操作
                        for key, value in updates:
                            if table_exists:
                                # 表存在，检查键是否存在
                                cursor.execute("SELECT 1 FROM ItemTable WHERE key = ?", (key,))
                                exists = cursor.fetchone() is not None

                                if exists:
                                    cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?", (value, key))
                                else:
                                    cursor.execute("INSERT INTO ItemTable (key, value) VALUES (?, ?)", (key, value))
                                logger.info(f"成功{'更新' if exists else '插入'} {key.split('/')[-1]}")
                            else:
                                # 表是新创建的，直接插入
                                cursor.execute("INSERT INTO ItemTable (key, value) VALUES (?, ?)", (key, value))
                        
                        conn.commit()
                        return True
                        
                except sqlite3.Error as e:
                    last_error = e
                    retry_count += 1
                    logger.warning(f"SQLite操作失败(第{retry_count}次尝试): {e}")
                    if retry_count < max_retries:
                        time.sleep(0.5)  # 重试前等待
                    continue
                except Exception as e:
                    logger.error(f"遇到未知异常: {e}")
                    return False
                    
            # 如果所有重试都失败
            if last_error:
                logger.error(f"SQLite数据库操作在{max_retries}次尝试后仍然失败: {last_error}")
            return False
            
        except Exception as e:
            logger.error(f"遇到未知异常: {e}")
            return False

    def delete_auth_key(self, key_name: str) -> bool:
        """从数据库中删除指定的键值对"""
        if key_name not in Config.DB_KEYS:
            logger.error(f"未知的键名: {key_name}")
            return False
            
        key = Config.DB_KEYS[key_name]
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 检查键是否存在
                cursor.execute("SELECT 1 FROM ItemTable WHERE key = ?", (key,))
                exists = cursor.fetchone() is not None
                
                if exists:
                    # 删除键值对
                    cursor.execute("DELETE FROM ItemTable WHERE key = ?", (key,))
                    logger.info(f"成功删除 {key_name} 记录")
                    return True
                else:
                    logger.info(f"未找到 {key_name} 记录，无需删除")
                    return True
        except sqlite3.Error as e:
            logger.error(f"数据库错误: {e}")
            return False
        except Exception as e:
            logger.error(f"删除 {key_name} 时发生错误: {e}")
            return False


class CursorManager:
    """Cursor管理器"""

    @staticmethod
    def generate_mac_machine_id(mac_address: str = None) -> str:
        """
        生成符合Cursor格式的macMachineId (SHA256哈希)

        Args:
            mac_address: MAC地址，如果为None则随机生成

        Returns:
            str: 64位十六进制SHA256哈希字符串
        """
        if mac_address is None:
            # 生成随机MAC地址 (避免使用虚拟MAC地址)
            # 格式: XX:XX:XX:XX:XX:XX
            mac_parts = []
            for i in range(6):
                if i == 0:
                    # 第一个字节确保不是虚拟MAC地址
                    # 避免 00, ff, ac:de:48 开头
                    first_byte = random.randint(0x10, 0xfe)
                    while first_byte in [0x00, 0xff, 0xac]:
                        first_byte = random.randint(0x10, 0xfe)
                    mac_parts.append(f"{first_byte:02x}")
                else:
                    mac_parts.append(f"{random.randint(0x00, 0xff):02x}")

            mac_address = ":".join(mac_parts)
            logger.info(f"生成随机MAC地址: {mac_address}")

        # 使用SHA256哈希处理MAC地址 (模拟Cursor的X5函数)
        mac_hash = hashlib.sha256(mac_address.encode('utf-8')).hexdigest()
        logger.info(f"生成macMachineId: {mac_hash}")
        return mac_hash

    @staticmethod
    def reset_cursor_id(token_data: TokenData) -> bool:
        """重置Cursor ID"""
        storage_path = FilePathManager.get_storage_path()
        if not storage_path.exists():
            logger.warning(f"找不到指定文件: {storage_path}")
            return False

        try:
            FilePermissionManager.make_file_writable(storage_path)
            
            # 读取文件内容并处理可能的编码问题
            try:
                content = storage_path.read_text(encoding='utf-8')
                # 处理 Windows 路径中的反斜杠
                content = content.replace('\\', '\\\\')
                data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
                # 尝试使用原始字符串读取
                with open(storage_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 处理 Windows 路径中的反斜杠
                    content = content.replace('\\', '\\\\')
                    data = json.loads(content)

            # 生成符合Cursor格式的macMachineId (SHA256哈希)
            mac_machine_id_hash = CursorManager.generate_mac_machine_id()

            # 更新数据
            data.update({
                "telemetry.macMachineId": mac_machine_id_hash,  # 使用SHA256哈希格式
                "telemetry.machineId": token_data.machine_id,
                "telemetry.devDeviceId": token_data.dev_device_id,
                "telemetry.sqmId": "{" + token_data.sqm_id.upper() + "}",
                "storage.serviceMachineId": token_data.service_machine_id
            })

            # 写入文件时确保 Windows 路径正确
            json_str = json.dumps(data, indent=4)
            if platform.system() == 'Windows':
                # 恢复 Windows 路径格式
                json_str = json_str.replace('\\\\', '\\')
            
            storage_path.write_text(json_str, encoding='utf-8')
            FilePermissionManager.make_file_readonly(storage_path)
            logger.info("已完成 Cursor 机器码的修改")
            return True
        except Exception as e:
            logger.error(f"修改 Cursor 机器码过程中出现错误: {e}")
            return False

    def exit_cursor(self):
        """安全退出 Cursor 应用程序"""
        try:
            logger.info("正在关闭 Cursor 应用程序...")
            
            if platform.system() == 'Darwin':  # macOS
                try:
                    # 首先尝试使用 osascript 优雅地退出
                    logger.info("尝试使用 AppleScript 优雅退出 Cursor...")
                    quit_cmd = """
                    osascript -e '
                        try
                            tell application "Cursor" to quit
                            return true
                        on error
                            return false
                        end try
                    '
                    """
                    result = subprocess.run(['osascript', '-e', quit_cmd], 
                                         capture_output=True, 
                                         text=True)
                    logger.info(f"AppleScript 退出结果: {result.stdout.strip()}")
                    
                    # 等待一段时间看是否已经退出
                    time.sleep(2)
                    
                    # 检查进程是否还在运行
                    ps_cmd = "pgrep -f Cursor"
                    result = subprocess.run(ps_cmd.split(), capture_output=True, text=True)
                    
                    if result.stdout.strip():
                        # 如果进程还在运行，使用 pkill
                        logger.info("使用 pkill 强制终止 Cursor 进程...")
                        subprocess.run(['pkill', '-f', 'Cursor'])
                        time.sleep(1)  # 给进程一些时间来终止
                        
                        # 再次检查进程
                        result = subprocess.run(ps_cmd.split(), capture_output=True, text=True)
                        if result.stdout.strip():
                            # 如果还在运行，使用 SIGKILL
                            logger.info("使用 SIGKILL 强制终止 Cursor 进程...")
                            subprocess.run(['pkill', '-9', '-f', 'Cursor'])
                            time.sleep(1)
                    
                    # 最后检查一次
                    result = subprocess.run(ps_cmd.split(), capture_output=True, text=True)
                    if not result.stdout.strip():
                        logger.info("Cursor 进程已成功终止")
                        return True
                    else:
                        logger.error("无法完全终止 Cursor 进程")
                        return False
                        
                except Exception as e:
                    logger.error(f"终止 Cursor 进程时出错: {e}")
                    logger.error(traceback.format_exc())
                    return False
            
            elif platform.system() == 'Windows':  # Windows
                try:
                    logger.info("在Windows系统上终止Cursor进程...")
                    
                    # 使用tasklist检查进程
                    tasklist_cmd = "tasklist /FI \"IMAGENAME eq cursor.exe\" /FO CSV /NH"
                    result = subprocess.run(tasklist_cmd, capture_output=True, text=True, shell=True)
                    
                    if "cursor.exe" in result.stdout.lower():
                        # 首先尝试优雅地终止
                        logger.info("尝试使用taskkill优雅终止进程...")
                        subprocess.run(['taskkill', '/IM', 'cursor.exe'], 
                                    capture_output=True,
                                    creationflags=subprocess.CREATE_NO_WINDOW)
                        time.sleep(2)
                        
                        # 再次检查进程
                        result = subprocess.run(tasklist_cmd, capture_output=True, text=True, shell=True)
                        if "cursor.exe" in result.stdout.lower():
                            # 如果进程还在运行，使用强制终止
                            logger.info("使用taskkill /F强制终止进程...")
                            subprocess.run(['taskkill', '/F', '/IM', 'cursor.exe'], 
                                        capture_output=True,
                                        creationflags=subprocess.CREATE_NO_WINDOW)
                            time.sleep(1)
                        
                        # 最后检查
                        result = subprocess.run(tasklist_cmd, capture_output=True, text=True, shell=True)
                        if "cursor.exe" not in result.stdout.lower():
                            logger.info("Cursor进程已成功终止")
                            return True
                        else:
                            logger.error("无法完全终止Cursor进程")
                            return False
                    else:
                        logger.info("未发现正在运行的Cursor进程")
                        return True
                        
                except Exception as e:
                    logger.error(f"终止Cursor进程时出错: {e}")
                    logger.error(traceback.format_exc())
                    return False
            else:
                logger.error("不支持的操作系统")
                return False
                
        except Exception as e:
            logger.error(f"退出 Cursor 时发生错误: {e}")
            logger.error(traceback.format_exc())
            return False


class TokenStorage:
    """Token存储管理器"""
    
    @staticmethod
    def get_tokens_path() -> Path:
        """获取token存储文件路径"""
        system = platform.system()
        logger.debug(f"当前操作系统: {system}")
        
        # 使用与其他配置文件相同的目录
        if system == "Windows":
            config_dir = Path(os.getenv('LOCALAPPDATA')) / 'CursorPro'
            logger.debug(f"Windows配置目录: {config_dir}")
        elif system == "Darwin":  # macOS
            home_dir = Path.home()
            logger.debug(f"MacOS home目录: {home_dir}")
            config_dir = home_dir / 'Library' / 'Application Support' / 'CursorPro'
            logger.debug(f"MacOS配置目录: {config_dir}")
        else:  # Linux
            config_dir = Path.home() / '.config' / 'CursorPro'
            logger.debug(f"Linux配置目录: {config_dir}")
            
        # 确保目录存在
        try:
            config_dir.mkdir(exist_ok=True)
            logger.debug(f"确保目录存在: {config_dir}, 成功创建或已存在")
        except Exception as e:
            logger.error(f"创建目录失败: {config_dir}, 错误: {e}")
            
        token_path = config_dir / 'tokens.enc'
        logger.debug(f"最终令牌文件路径: {token_path}")
        return token_path

    @staticmethod
    def get_encryption_key(salt: bytes = None) -> Tuple[bytes, bytes]:
        """
        生成或获取加密密钥
        
        Args:
            salt: 可选的盐值，如果为None则生成新的盐值
            
        Returns:
            Tuple[bytes, bytes]: (密钥, 盐值)
        """
        # 使用机器特定信息作为密码基础
        password_base = platform.node() + platform.machine() + platform.processor()
        password = password_base.encode()
        
        # 如果没有提供盐值，生成新的盐值
        if salt is None:
            salt = os.urandom(16)
        
        # 使用PBKDF2HMAC从密码派生密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key, salt

    @staticmethod
    def encrypt_data(data: Dict) -> Tuple[bytes, bytes]:
        """
        加密数据
        
        Args:
            data: 要加密的数据字典
            
        Returns:
            Tuple[bytes, bytes]: (加密数据, 盐值)
        """
        # 生成密钥和盐值
        key, salt = TokenStorage.get_encryption_key()
        
        # 使用Fernet对称加密
        cipher = Fernet(key)
        
        # 将数据转换为JSON字符串并加密
        data_json = json.dumps(data).encode()
        encrypted_data = cipher.encrypt(data_json)
        
        return encrypted_data, salt

    @staticmethod
    def decrypt_data(encrypted_data: bytes, salt: bytes) -> Dict:
        """
        解密数据
        
        Args:
            encrypted_data: 加密的数据
            salt: 用于生成密钥的盐值
            
        Returns:
            Dict: 解密后的数据字典
        """
        # 使用盐值生成密钥
        key, _ = TokenStorage.get_encryption_key(salt)
        
        # 使用Fernet解密
        cipher = Fernet(key)
        
        # 解密数据并解析JSON
        decrypted_data = cipher.decrypt(encrypted_data)
        data = json.loads(decrypted_data.decode())
        
        return data

    @staticmethod
    def save_token(access_code: str, token_data: TokenData) -> bool:
        """
        加密保存token数据到本地文件
        
        Args:
            access_code: 获取token时使用的访问码
            token_data: TokenData实例
            
        Returns:
            bool: 保存是否成功
        """
        logger.info("=== 开始保存Token数据 ===")
        # 添加详细路径信息日志
        logger.info(f"当前用户: {os.getenv('USER')}")
        logger.info(f"当前home路径: {Path.home()}")
        logger.info(f"当前工作目录: {os.getcwd()}")
        tokens_path = TokenStorage.get_tokens_path()
        logger.info(f"保存tokens的完整路径: {tokens_path}")
        logger.info(f"该路径是否存在: {tokens_path.parent.exists()}")
        
        try:
            # 构建要保存的数据
            current_time = datetime.datetime.now(datetime.timezone(datetime.timedelta(hours=8))).isoformat()
            data = {
                "access_code": access_code,
                "timestamp": current_time,
                "last_switch_time": current_time,  # 添加上次切换时间，初次创建时与timestamp相同
                "record_id": f"{access_code}_{int(time.time())}",  # 添加唯一记录ID（API Key + 时间戳）
                "token_data": {
                    "mac_machine_id": token_data.mac_machine_id,
                    "machine_id": token_data.machine_id,
                    "dev_device_id": token_data.dev_device_id,
                    "email": token_data.email,
                    "token": token_data.token,
                    "sqm_id": token_data.sqm_id,
                    "service_machine_id": token_data.service_machine_id
                }
            }
            
            # 加密数据
            encrypted_data, salt = TokenStorage.encrypt_data(data)
            
            # 获取存储文件路径
            tokens_path = TokenStorage.get_tokens_path()
            
            # 读取现有数据（如果有）
            tokens_list = []
            if tokens_path.exists():
                try:
                    with open(tokens_path, 'rb') as f:
                        file_content = f.read()
                    
                    # 文件格式: [salt_size:4bytes][salt][encrypted_data]
                    if len(file_content) > 4:
                        stored_tokens = []
                        offset = 0
                        
                        while offset < len(file_content):
                            # 读取盐值大小
                            salt_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                            offset += 4
                            
                            # 读取盐值
                            stored_salt = file_content[offset:offset+salt_size]
                            offset += salt_size
                            
                            # 读取加密数据大小
                            data_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                            offset += 4
                            
                            # 读取加密数据
                            stored_encrypted_data = file_content[offset:offset+data_size]
                            offset += data_size
                            
                            try:
                                # 解密数据
                                token_info = TokenStorage.decrypt_data(stored_encrypted_data, stored_salt)
                                stored_tokens.append(token_info)
                            except Exception as e:
                                logger.error(f"解密令牌数据失败: {e}")
                                continue
                        
                        tokens_list = stored_tokens
                        logger.info(f"成功读取{len(tokens_list)}条已保存的令牌数据")
                        #打印详细信息
                        logger.info(f"已保存的令牌数据: {tokens_list}")
                except Exception as e:
                    logger.error(f"读取已存储的令牌数据时出错: {e}")
                    logger.warning("将创建新的令牌存储文件")
            
            # 添加新的token数据
            tokens_list.append(data)
            
            # 限制每个API Key最多保存的记录数（例如每个API Key保留10条记录）
            max_records_per_api = 10
            
            # 按API Key分组并按时间戳排序
            api_groups = {}
            for token in tokens_list:
                api_key = token.get("access_code", "unknown")
                if api_key not in api_groups:
                    api_groups[api_key] = []
                api_groups[api_key].append(token)
            
            # 对每个API Key的记录按时间戳排序，并只保留最新的max_records_per_api条
            filtered_tokens = []
            for api_key, records in api_groups.items():
                sorted_records = sorted(
                    records,
                    key=lambda x: x.get("timestamp", "1970-01-01T00:00:00"),
                    reverse=True  # 降序排列，最新的在前面
                )
                filtered_tokens.extend(sorted_records[:max_records_per_api])
            
            # 写入文件
            with open(tokens_path, 'wb') as f:
                for token_info in filtered_tokens:
                    # 重新加密数据（每条记录使用不同的盐值）
                    enc_data, enc_salt = TokenStorage.encrypt_data(token_info)
                    
                    # 写入盐值大小
                    f.write(len(enc_salt).to_bytes(4, byteorder='big'))
                    # 写入盐值
                    f.write(enc_salt)
                    # 写入加密数据大小
                    f.write(len(enc_data).to_bytes(4, byteorder='big'))
                    # 写入加密数据
                    f.write(enc_data)
            
            logger.info(f"已保存{len(filtered_tokens)}条令牌数据到文件: {tokens_path}")
            return True
        
        except Exception as e:
            logger.error(f"保存令牌数据时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    @staticmethod
    def list_tokens() -> List[Dict]:
        """
        获取所有保存的token数据
        
        Returns:
            List[Dict]: 保存的token数据列表
        """
        logger.info("=== 获取已保存的Token数据 ===")
        # 添加详细路径信息日志
        logger.info(f"当前用户: {os.getenv('USER')}")
        logger.info(f"当前home路径: {Path.home()}")
        logger.info(f"当前工作目录: {os.getcwd()}")
        tokens_path = TokenStorage.get_tokens_path()
        logger.info(f"读取tokens的完整路径: {tokens_path}")
        logger.info(f"该路径是否存在: {tokens_path.exists()}")
        logger.info(f"父目录是否存在: {tokens_path.parent.exists()}")
        
        if not tokens_path.exists():
            logger.info(f"令牌存储文件不存在: {tokens_path}")
            return []
        
        try:
            with open(tokens_path, 'rb') as f:
                file_content = f.read()
            
            # 文件格式: [salt_size:4bytes][salt][encrypted_data_size:4bytes][encrypted_data]...
            if len(file_content) > 4:
                stored_tokens = []
                offset = 0
                
                while offset < len(file_content):
                    # 读取盐值大小
                    salt_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                    offset += 4
                    
                    # 读取盐值
                    stored_salt = file_content[offset:offset+salt_size]
                    offset += salt_size
                    
                    # 读取加密数据大小
                    data_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                    offset += 4
                    
                    # 读取加密数据
                    stored_encrypted_data = file_content[offset:offset+data_size]
                    offset += data_size
                    
                    try:
                        # 解密数据
                        token_info = TokenStorage.decrypt_data(stored_encrypted_data, stored_salt)
                        
                        # 兼容性处理：为旧数据添加last_switch_time字段
                        if "last_switch_time" not in token_info:
                            token_info["last_switch_time"] = token_info.get("timestamp", "")
                            logger.debug(f"为旧记录添加last_switch_time字段: {token_info.get('record_id', 'unknown')}")
                        
                        # 将原始token_info添加到stored_tokens
                        stored_tokens.append(token_info)
                        
                        # 创建一个用于日志显示的副本，截断token值
                        display_token_info = token_info.copy()
                        if "token_data" in display_token_info:
                            display_token_info["token_data"] = display_token_info["token_data"].copy()
                            if "token" in display_token_info["token_data"]:
                                token = display_token_info["token_data"]["token"]
                                display_token_info["token_data"]["token"] = token[:10] + "..." + token[-5:] if len(token) > 15 else token
                    except Exception as e:
                        logger.error(f"解密令牌数据失败: {e}")
                        continue
                
                logger.info(f"成功读取{len(stored_tokens)}条已保存的令牌数据")
                # 打印详细信息（使用截断后的token以保护敏感信息）
                display_tokens = []
                for token in stored_tokens:
                    token_copy = token.copy()
                    if "token_data" in token_copy:
                        token_copy["token_data"] = token_copy["token_data"].copy()
                        if "token" in token_copy["token_data"]:
                            full_token = token_copy["token_data"]["token"]
                            token_copy["token_data"]["token"] = full_token[:10] + "..." + full_token[-5:] if len(full_token) > 15 else full_token
                    display_tokens.append(token_copy)
                
                logger.info(f"已保存的令牌数据: {display_tokens}")
                return stored_tokens
            
            return []
            
        except Exception as e:
            logger.error(f"读取已存储的令牌数据时出错: {e}")
            logger.error(traceback.format_exc())
            return []

    @staticmethod
    def get_token_by_access_code(access_code: str) -> Optional[Dict]:
        """
        根据access_code获取保存的token数据
        
        Args:
            access_code: 访问码
            
        Returns:
            Optional[Dict]: 对应的token数据，如果未找到则返回None
        """
        tokens = TokenStorage.list_tokens()
        
        for token_info in tokens:
            if token_info.get("access_code") == access_code:
                return token_info
        
        return None

    @staticmethod
    def update_last_switch_time(access_code: str, record_id: str) -> bool:
        """
        更新指定记录的上次切换时间
        
        Args:
            access_code: 访问码
            record_id: 记录ID
            
        Returns:
            bool: 更新是否成功
        """
        try:
            logger.info(f"=== 更新上次切换时间 ===")
            logger.info(f"access_code: {access_code}")
            logger.info(f"record_id: {record_id}")
            
            # 获取所有token数据
            tokens = TokenStorage.list_tokens()
            if not tokens:
                logger.warning("没有找到任何token记录")
                return False
            
            # 查找目标记录
            target_token = None
            for token_info in tokens:
                if (token_info.get("access_code") == access_code and 
                    token_info.get("record_id") == record_id):
                    target_token = token_info
                    break
            
            if not target_token:
                logger.warning(f"未找到匹配的token记录: access_code={access_code}, record_id={record_id}")
                return False
            
            # 更新上次切换时间
            current_time = datetime.datetime.now(datetime.timezone(datetime.timedelta(hours=8))).isoformat()
            target_token["last_switch_time"] = current_time
            logger.info(f"已更新上次切换时间为: {current_time}")
            
            # 重新保存所有token数据
            tokens_path = TokenStorage.get_tokens_path()
            
            # 写入文件
            with open(tokens_path, 'wb') as f:
                for token_info in tokens:
                    # 重新加密数据（每条记录使用不同的盐值）
                    enc_data, enc_salt = TokenStorage.encrypt_data(token_info)
                    
                    # 写入盐值大小
                    f.write(len(enc_salt).to_bytes(4, byteorder='big'))
                    # 写入盐值
                    f.write(enc_salt)
                    # 写入加密数据大小
                    f.write(len(enc_data).to_bytes(4, byteorder='big'))
                    # 写入加密数据
                    f.write(enc_data)
            
            logger.info("成功更新并保存token记录")
            return True
            
        except Exception as e:
            logger.error(f"更新上次切换时间时出错: {e}")
            logger.error(traceback.format_exc())
            return False


class TokenManager:
    """Token管理器"""

    @staticmethod
    def generate_random_email() -> str:
        """生成随机邮箱地址（已弃用）
        
        现在直接返回空字符串，不再生成虚拟邮箱
        """
        logger.info("generate_random_email已弃用，返回空字符串")
        return ""

    @staticmethod
    def decrypt_cookie(encrypted_cookie: str) -> str:
        """解密从服务器收到的加密cookie"""
        logger.info("开始解密cookie...")
        try:
            # 使用SHA-256生成一个固定长度的密钥
            key_bytes = hashlib.sha256(Config.COOKIE_ENCRYPTION_KEY.encode()).digest()
            # 获取base64编码的密钥
            key = base64.urlsafe_b64encode(key_bytes)
            
            f = Fernet(key)
            # 解密并返回原始cookie
            decrypted_cookie = f.decrypt(encrypted_cookie.encode())
            logger.info("cookie解密成功")
            return decrypted_cookie.decode()
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            logger.error(f"将使用原始(可能是加密的)cookie")
            return encrypted_cookie

    @staticmethod
    def fetch_token_data(access_code: str) -> Optional[TokenData]:
        """获取Token数据"""
        logger.info("=== 开始获取Token数据 ===")
        # 重置上一次的错误信息
        Config.LAST_ERROR_MESSAGE = None
        
        # 优先使用上次成功的服务器URL，如果没有则使用主服务器
        if Config.LAST_SUCCESSFUL_URL:
            logger.info(f"使用上次成功连接的服务器: {Config.LAST_SUCCESSFUL_URL}")
            Config.BASE_URL = Config.LAST_SUCCESSFUL_URL
        else:
            logger.info(f"没有上次成功连接的记录，使用主服务器: {Config.PRIMARY_URL}")
            Config.BASE_URL = Config.PRIMARY_URL
            
        Config.API_URL = f"{Config.BASE_URL}/api/cookie"
        logger.debug(f"使用的API URL: {Config.API_URL}")
        
        try:
            logger.info("发送API请求获取Token...")
            response = requests.get(
                Config.API_URL,
                headers={"X-API-Key": access_code}
            )
            
            logger.info(f"API响应状态码: {response.status_code}")
            
            # 如果当前服务器请求失败，尝试另一个服务器
            if response.status_code != 200:
                logger.warning(f"当前服务器请求失败，状态码: {response.status_code}")
                
                try:
                    # 尝试解析错误响应
                    error_data = response.json()
                    error_message = error_data.get('detail', '未知错误')
                    logger.error(f"Token获取失败: {error_message}")
                    logger.error(f"完整错误响应: {error_data}")
                    # 保存错误信息以便主函数中显示
                    Config.LAST_ERROR_MESSAGE = error_message
                except Exception as e:
                    logger.error(f"解析错误响应失败: {str(e)}")
                    logger.error(f"错误响应内容: {response.text}")
                    Config.LAST_ERROR_MESSAGE = "获取账号失败，请稍后再试"
                
                # 确定备用服务器URL
                backup_url = Config.BACKUP_URL if Config.BASE_URL != Config.BACKUP_URL else Config.PRIMARY_URL
                logger.info(f"尝试使用备用服务器: {backup_url}")
                
                # 切换到备用服务器
                Config.BASE_URL = backup_url
                Config.API_URL = f"{Config.BASE_URL}/api/cookie"
                
                try:
                    logger.info("向备用服务器发送API请求...")
                    response = requests.get(
                        Config.API_URL,
                        headers={"X-API-Key": access_code}
                    )
                    logger.info(f"备用服务器响应状态码: {response.status_code}")
                    
                    if response.status_code != 200:
                        try:
                            error_data = response.json()
                            error_message = error_data.get('detail', '未知错误')
                            logger.error(f"备用服务器Token获取失败: {error_message}")
                            logger.error(f"完整错误响应: {error_data}")
                            # 保存错误信息以便主函数中显示
                            Config.LAST_ERROR_MESSAGE = error_message
                            return None
                        except Exception as e:
                            logger.error(f"解析错误响应失败: {str(e)}")
                            logger.error(f"错误响应内容: {response.text}")
                            Config.LAST_ERROR_MESSAGE = "获取账号失败，请稍后再试"
                            return None
                except requests.exceptions.RequestException as e:
                    logger.error(f"备用服务器API请求异常: {str(e)}")
                    logger.error(f"异常详情: {traceback.format_exc()}")
                    return None
            
            # 请求成功，记录当前成功的服务器URL
            Config.LAST_SUCCESSFUL_URL = Config.BASE_URL
            logger.info(f"请求成功，记录当前成功的服务器URL: {Config.LAST_SUCCESSFUL_URL}")
                
            data = response.json()
            logger.info("成功获取API响应数据")
            logger.debug(f"API响应内容: {data}")
            
            # 检查cookie是否加密
            encrypted_cookie = data.get("cookie")
            is_encrypted = data.get("is_encrypted", False)
            
            # 如果cookie是加密的，需要解密
            if is_encrypted:
                logger.info("检测到加密的cookie，正在解密...")
                cookie = TokenManager.decrypt_cookie(encrypted_cookie)
            else:
                logger.info("收到未加密的cookie")
                cookie = encrypted_cookie
            
            # 生成本地机器码（使用版本2的生成方式）
            logger.info("开始生成本地标识符...")
            
            # 生成macMachineId - 使用标准UUID v4格式（版本2方式）
            mac_machine_id = str(uuid.uuid4())
            
            # 生成machineId - 应该是64个字符的十六进制字符串
            machine_id = secrets.token_hex(32)  # 32字节=64个十六进制字符
            
            dev_device_id = str(uuid.uuid4())
            email = TokenManager.generate_random_email()
            sqm_id = str(uuid.uuid4())
            service_machine_id = dev_device_id  # 复用dev_device_id作为service_machine_id
            
            # 构建完整的token数据
            token_data = {
                "mac_machine_id": mac_machine_id,
                "machine_id": machine_id,
                "dev_device_id": dev_device_id,
                "email": email,
                "token": cookie,
                "sqm_id": sqm_id,
                "service_machine_id": service_machine_id
            }
            
            logger.info("=== Token数据生成完成 ===")
            logger.info(f"邮箱: {token_data['email']}")
            logger.debug(f"机器码 1 (macMachineId): {token_data['mac_machine_id']} [UUID格式]")
            logger.debug(f"机器码 2 (machineId): {token_data['machine_id']} [64位十六进制]")
            logger.debug(f"设备 ID: {token_data['dev_device_id']}")
            logger.debug(f"SQM ID: {token_data['sqm_id']}")
            logger.debug(f"Token: {token_data['token'][:10]}...")  # 只显示Token前10位
            
            logger.info("正在创建TokenData实例...")
            result = TokenData.from_dict(token_data)
            logger.info("TokenData实例创建成功")
            
            # 加密保存token数据
            try:
                TokenStorage.save_token(access_code, result)
                logger.info("Token数据已加密保存到本地")
            except Exception as e:
                logger.error(f"保存Token数据时出错: {e}")
                logger.error("继续处理，不影响主流程")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求异常: {str(e)}")
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 设置错误信息
            Config.LAST_ERROR_MESSAGE = f"网络请求错误: {str(e)}"
            return None
        except Exception as e:
            logger.error(f"Token数据获取过程中发生未知错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            # 设置错误信息
            Config.LAST_ERROR_MESSAGE = f"处理请求时发生内部错误: {str(e)}"
            return None

    @staticmethod
    def update_token(token_data: TokenData) -> bool:
        """更新Cursor的token信息"""
        logger.info("=== 开始更新Token信息 ===")
        try:
            # 1. 更新machine ID
            logger.info("1. 更新机器码")
            if not CursorManager.reset_cursor_id(token_data):
                logger.error("更新机器码失败")
                return False
            
            # 2. 获取AuthManager实例
            auth_manager = CursorAuthManager()

            # 3. 先删除server_config配置 (顺序调整)
            logger.info("2. 删除server_config配置")
            if not auth_manager.delete_auth_key('server_config'):
                logger.warning("删除server_config失败，但这不影响主要功能")

            # 4. 更新数据库认证信息 (顺序调整)
            logger.info("3. 更新认证信息")
            if not auth_manager.update_auth(
                email=token_data.email,
                access_token=token_data.token,
                refresh_token=token_data.token
            ):
                logger.error("更新认证信息失败")
                return False
            
            logger.info("=== Token信息更新完成 ===")
            return True
            
        except Exception as e:
            logger.error(f"更新Token信息时发生错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False


class Utils:
    """工具类"""

    @staticmethod
    def version_check(version: str, min_version: str = "", max_version: str = "") -> bool:
        """
        版本号检查

        Args:
            version: 当前版本号
            min_version: 最小版本号要求
            max_version: 最大版本号要求

        Returns:
            bool: 版本号是否符合要求
        """
        logger.info(f"=== 开始版本检查 ===")
        logger.info(f"当前版本: {version}")
        logger.info(f"最小版本要求: {min_version if min_version else '无'}")
        logger.info(f"最大版本要求: {max_version if max_version else '无'}")
        
        try:
            if not re.match(Config.VERSION_PATTERN, version):
                logger.error(f"版本号格式不正确: {version}")
                return False

            def parse_version(ver: str) -> Tuple[int, ...]:
                parsed = tuple(map(int, ver.split(".")))
                logger.debug(f"解析版本号 {ver} -> {parsed}")
                return parsed

            current = parse_version(version)

            if min_version and current < parse_version(min_version):
                logger.warning(f"当前版本 {version} 低于最小要求 {min_version}")
                return False

            if max_version and current > parse_version(max_version):
                logger.warning(f"当前版本 {version} 高于最大要求 {max_version}")
                return False

            logger.info("版本检查通过")
            return True

        except Exception as e:
            logger.error(f"版本号校验出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    @staticmethod
    def check_files_exist(pkg_path: Path, main_path: Path) -> bool:
        """
        检查文件是否存在

        Args:
            pkg_path: package.json 文件路径
            main_path: main.js 文件路径

        Returns:
            bool: 检查是否通过
        """
        logger.info("=== 开始检查文件 ===")
        logger.info(f"检查package.json路径: {pkg_path}")
        logger.info(f"检查main.js路径: {main_path}")
        
        for file_path in [pkg_path, main_path]:
            if not file_path.exists():
                logger.error(f"找不到指定文件: {file_path}")
                return False
            else:
                logger.info(f"文件存在: {file_path}")
                try:
                    file_size = file_path.stat().st_size
                    logger.debug(f"文件大小: {file_size} 字节")
                except Exception as e:
                    logger.warning(f"无法获取文件 {file_path} 的大小: {e}")
        
        logger.info("所有文件检查通过")
        return True


class CursorPatcher:
    """Cursor补丁管理器"""

    @staticmethod
    def check_version(version: str) -> bool:
        return Utils.version_check(version, min_version=Config.MIN_PATCH_VERSION)

    @staticmethod
    def patch_main_js(main_path: Path) -> bool:
        """
        修改main.js文件以移除机器码检查

        Args:
            main_path: main.js文件路径

        Returns:
            bool: 修改是否成功
        """
        try:
            # 读取文件内容
            content = main_path.read_text(encoding="utf-8")

            # 执行替换
            patterns = {
                r"async getMachineId\(\)\{return [^??]+\?\?([^}]+)\}": r"async getMachineId(){return \1}",
                r"async getMacMachineId\(\)\{return [^??]+\?\?([^}]+)\}": r"async getMacMachineId(){return \1}"
            }


            # 检查是否存在需要修复的代码
            found_patterns = False
            for pattern in patterns.keys():
                if re.search(pattern, content):
                    found_patterns = True
                    break

            if not found_patterns:
                logger.info("未发现需要修复的代码，可能已经修复或不支持当前版本")
                return True
            
            
            for pattern, replacement in patterns.items():
                content = re.sub(pattern, replacement, content)

            # 写入修改后的内容
            FilePermissionManager.make_file_writable(main_path)
            main_path.write_text(content, encoding="utf-8")
            FilePermissionManager.make_file_readonly(main_path)
            logger.info("已完成 Cursor 机器码修改")
            return True

        except Exception as e:
            logger.error(f"修改 Cursor 机器码过程中出现异常: {e}")
            # 如果出错，尝试恢复备份
            return False


def main(access_code: str = None) -> bool:
    """主函数"""
    try:
        logger.info("=== 启动Cursor Token更新工具 ===")
        
        # 获取授权码
        if not access_code:
            access_code = input("请输入授权码: ") or Config.ACCESS_CODE
        
        # 获取token数据
        if token_data := TokenManager.fetch_token_data(access_code):
            logger.info("\n=== Token获取成功 ===")
            logger.info("开始执行更新流程")
            
            if TokenManager.update_token(token_data):
                logger.info("\n=== 更新成功 ===")
                logger.info("已完成Cursor认证信息更新")
                
                # 仅用于信息展示，不影响测试流程
                try:
                    stored_tokens = TokenStorage.list_tokens()
                    if stored_tokens:
                        logger.info(f"\n已保存 {len(stored_tokens)} 条令牌记录")
                except Exception:
                    pass
                
                return True
            
        return False

    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {e}")
        return False


def backup_file(file_path):
    """创建文件备份"""
    backup_path = file_path.with_suffix(file_path.suffix + ".bak")
    
    if backup_path.exists():
        logger.info(f"备份文件 {backup_path} 已存在，跳过备份")
        return True
    
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"已创建备份文件 {backup_path}")
        return True
    except Exception as e:
        logger.error(f"创建备份失败: {e}")
        return False


def update_product_json() -> bool:
    """删除product.json中的checksums部分以绕过完整性校验"""
    try:
        # 获取基础路径（package.json的父目录）
        pkg_path, _ = FilePathManager.get_cursor_app_paths()
        app_path = pkg_path.parent  # 这就是app目录
        product_path = app_path / "product.json"  # app目录下的product.json
        logger.info(f"正在处理 {product_path}...")

        if not product_path.exists():
            logger.error(f"错误: 文件 {product_path} 不存在")
            return False

        # 创建备份
        if not backup_file(product_path):
            return False

        # 读取JSON文件
        with open(product_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        # 检查是否有checksums字段
        if "checksums" in data:
            # 删除checksums字段
            del data["checksums"]
            logger.info("已删除checksums字段")

            # 写回文件
            FilePermissionManager.make_file_writable(product_path)
            with open(product_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
            FilePermissionManager.make_file_readonly(product_path)

            logger.info(f"已成功更新 {product_path}")
            return True
        else:
            logger.info("checksums字段不存在，无需修改")
            return True

    except Exception as e:
        logger.error(f"处理 product.json 时出错: {e}")
        return False



def clear_email_from_database() -> bool:
    """清除数据库中的Gmail邮箱信息"""
    try:
        logger.info("=== 开始清除Gmail邮箱信息 ===")
        
        # 获取AuthManager实例
        auth_manager = CursorAuthManager()
        
        # 删除邮箱记录
        if auth_manager.delete_auth_key('email'):
            logger.info("✅ 成功清除数据库中的Gmail邮箱信息")
            return True
        else:
            logger.error("❌ 清除Gmail邮箱信息失败")
            return False
            
    except Exception as e:
        logger.error(f"清除Gmail邮箱信息时发生错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False


def modify_settings_email_display():
    """修改设置界面邮箱显示逻辑（仅UI层）- 跨平台支持"""
    try:
        # 获取workbench.desktop.main.js文件路径
        pkg_path, _ = FilePathManager.get_cursor_app_paths()
        app_path = pkg_path.parent  # 这就是app目录
        js_path = app_path / "out" / "vs" / "workbench" / "workbench.desktop.main.js"
        
        logger.info(f"正在处理 {js_path}...")
        
        if not js_path.exists():
            logger.error(f"错误: 文件 {js_path} 不存在")
            return False
        
        # 创建备份
        if not backup_file(js_path):
            return False
        
        with open(js_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否已经修改过
        if "email:v.replace(/icloud/g," in content or "email:g.replace(/icloud/g," in content:
            logger.info("检测到已有的邮箱替换逻辑，无需重复修改")
            return True
        
        modified = False
        current_system = platform.system()
        logger.info(f"当前系统: {current_system}")
        
        # Windows版本的模式匹配
        logger.info("尝试Windows版本模式匹配...")
        
        # Windows方法1：在getEmailAndSignUpType调用后立即对邮箱进行UI层替换
        pattern1 = r'(\{email:v,signUpType:C\}\)=>\{v&&f\()v(\),C&&m\(C\)\})'
        replacement1 = r'\1v.replace(/icloud/g,"gmail")\2'
        
        if re.search(pattern1, content):
            content = re.sub(pattern1, replacement1, content)
            logger.info("已修改设置界面邮箱显示逻辑（Windows方法1）")
            modified = True
        
        # Windows方法2：直接查找并替换v&&f(v)模式
        if not modified:
            pattern2 = r'v&&f\(v\)'
            replacement2 = r'v&&f(v.replace(/icloud/g,"gmail"))'
            
            if re.search(pattern2, content):
                content = re.sub(pattern2, replacement2, content)
                logger.info("已修改设置界面邮箱显示逻辑（Windows方法2）")
                modified = True
        
        # Windows方法3：查找更具体的getEmailAndSignUpType处理逻辑
        if not modified:
            pattern3 = r'getEmailAndSignUpType\(\)\.then\(\(\{email:v,signUpType:C\}\)=>\{v&&f\(v\)'
            replacement3 = r'getEmailAndSignUpType().then(({email:v,signUpType:C})=>{v&&f(v.replace(/icloud/g,"gmail"))'
            
            if re.search(pattern3, content):
                content = re.sub(pattern3, replacement3, content)
                logger.info("已修改设置界面邮箱显示逻辑（Windows方法3）")
                modified = True
        
        # Mac版本的模式匹配
        if not modified:
            logger.info("尝试Mac版本模式匹配...")
            
            # Mac方法1：直接查找并替换g&&u(g)模式
            pattern_mac1 = r'g&&u\(g\)'
            replacement_mac1 = r'g&&u(g.replace(/icloud/g,"gmail"))'
            
            if re.search(pattern_mac1, content):
                content = re.sub(pattern_mac1, replacement_mac1, content)
                logger.info("已修改设置界面邮箱显示逻辑（Mac方法1）")
                modified = True
            
            # Mac方法2：查找更具体的getEmailAndSignUpType处理逻辑
            if not modified:
                pattern_mac2 = r'getEmailAndSignUpType\(\)\.then\(\(\{email:g,signUpType:p\}\)=>\{g&&u\(g\)'
                replacement_mac2 = r'getEmailAndSignUpType().then(({email:g,signUpType:p})=>{g&&u(g.replace(/icloud/g,"gmail"))'
                
                if re.search(pattern_mac2, content):
                    content = re.sub(pattern_mac2, replacement_mac2, content)
                    logger.info("已修改设置界面邮箱显示逻辑（Mac方法2）")
                    modified = True
            
            # Mac方法3：更宽泛的匹配模式
            if not modified:
                pattern_mac3 = r'(\{email:g,signUpType:p\}\)=>\{g&&u\()g(\),p&&f\(p\)\})'
                replacement_mac3 = r'\1g.replace(/icloud/g,"gmail")\2'
                
                if re.search(pattern_mac3, content):
                    content = re.sub(pattern_mac3, replacement_mac3, content)
                    logger.info("已修改设置界面邮箱显示逻辑（Mac方法3）")
                    modified = True
        
        # 通用模式：查找变量&&函数(变量)的形式
        if not modified:
            logger.info("尝试通用模式匹配...")
            
            # 通用模式1：匹配 变量&&函数(变量) 的形式
            pattern_generic1 = r'(\w)&&(\w)\(\1\)'
            matches = re.findall(pattern_generic1, content)
            
            for var, func in matches:
                # 查找包含email的上下文
                context_pattern = f'email:{var}.*?{var}&&{func}\\({var}\\)'
                if re.search(context_pattern, content):
                    replacement_generic = f'{var}&&{func}({var}.replace(/icloud/g,"gmail"))'
                    content = re.sub(f'{var}&&{func}\\({var}\\)', replacement_generic, content)
                    logger.info(f"已修改设置界面邮箱显示逻辑（通用模式：{var}&&{func}({var})）")
                    modified = True
                    break
        
        if not modified:
            logger.warning("未找到需要修改的设置界面邮箱显示代码")
            
            # 提供诊断信息
            logger.info("诊断信息：")
            if re.search(r'getEmailAndSignUpType', content):
                logger.info("  ✓ 找到getEmailAndSignUpType函数")
            else:
                logger.info("  ✗ 未找到getEmailAndSignUpType函数")
                
            if re.search(r'email:', content):
                logger.info("  ✓ 找到email字段")
                # 显示email字段的上下文
                email_matches = re.finditer(r'email:(\w+)', content)
                email_vars = set()
                for match in email_matches:
                    email_vars.add(match.group(1))
                logger.info(f"  email变量名: {', '.join(email_vars)}")
            else:
                logger.info("  ✗ 未找到email字段")
            
            return False
        
        FilePermissionManager.make_file_writable(js_path)
        with open(js_path, "w", encoding="utf-8") as f:
            f.write(content)
        FilePermissionManager.make_file_readonly(js_path)
        
        logger.info("已成功修改设置界面邮箱显示逻辑：icloud -> gmail")
        logger.info("示例：<EMAIL> -> <EMAIL>")
        logger.info("注意：此修改仅影响设置界面显示，不影响实际邮箱数据")
        return True
        
    except Exception as e:
        logger.error(f"修改过程中发生错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    import sys
    
    # 如果命令行参数包含 --clear-email，则清除邮箱
    if len(sys.argv) > 1 and sys.argv[1] == "--clear-email":
        if clear_email_from_database():
            print("Gmail邮箱信息已成功清除")
        else:
            print("清除Gmail邮箱信息失败")
        sys.exit(0)
    main()

