import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import customtkinter as ctk
import json
import platform
import os
from pathlib import Path
from datetime import datetime
from cryptography.fernet import Fernet
import base64
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend

# 设置外观模式
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")  # 使用蓝色主题

# 设置DPI感知
try:
    import ctypes
    ctypes.windll.shcore.SetProcessDpiAwareness(2)
    ctypes.windll.user32.SetProcessDPIAware()
except:
    try:
        import ctypes
        ctypes.windll.user32.SetProcessDPIAware()
    except:
        pass

class TokenStorage:
    """Token存储管理器"""
    
    @staticmethod
    def get_tokens_path() -> Path:
        """获取token存储文件路径"""
        system = platform.system()
        
        if system == "Windows":
            config_dir = Path(os.getenv('LOCALAPPDATA')) / 'CursorPro'
        elif system == "Darwin":  # macOS
            config_dir = Path.home() / 'Library' / 'Application Support' / 'CursorPro'
        else:  # Linux
            config_dir = Path.home() / '.config' / 'CursorPro'
            
        config_dir.mkdir(exist_ok=True)
        return config_dir / 'tokens.enc'

    @staticmethod
    def get_encryption_key(salt: bytes = None) -> tuple:
        """生成或获取加密密钥"""
        password_base = platform.node() + platform.machine() + platform.processor()
        password = password_base.encode()
        
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key, salt

    @staticmethod
    def decrypt_data(encrypted_data: bytes, salt: bytes) -> dict:
        """解密数据"""
        key, _ = TokenStorage.get_encryption_key(salt)
        cipher = Fernet(key)
        decrypted_data = cipher.decrypt(encrypted_data)
        data = json.loads(decrypted_data.decode())
        return data

    @staticmethod
    def encrypt_data(data: dict, salt: bytes = None) -> tuple:
        """加密数据"""
        if salt is None:
            salt = os.urandom(16)
        
        key, _ = TokenStorage.get_encryption_key(salt)
        cipher = Fernet(key)
        json_data = json.dumps(data, ensure_ascii=False)
        encrypted_data = cipher.encrypt(json_data.encode())
        return encrypted_data, salt

    @staticmethod
    def load_all_tokens() -> list:
        """加载所有token数据，返回包含索引的列表"""
        tokens_path = TokenStorage.get_tokens_path()
        
        if not tokens_path.exists():
            return []
        
        try:
            with open(tokens_path, 'rb') as f:
                file_content = f.read()
            
            if len(file_content) <= 4:
                return []
            
            stored_tokens = []
            offset = 0
            index = 0
            
            while offset < len(file_content):
                # 读取盐值大小
                if offset + 4 > len(file_content):
                    break
                salt_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                offset += 4
                
                # 读取盐值
                if offset + salt_size > len(file_content):
                    break
                stored_salt = file_content[offset:offset+salt_size]
                offset += salt_size
                
                # 读取加密数据大小
                if offset + 4 > len(file_content):
                    break
                data_size = int.from_bytes(file_content[offset:offset+4], byteorder='big')
                offset += 4
                
                # 读取加密数据
                if offset + data_size > len(file_content):
                    break
                stored_encrypted_data = file_content[offset:offset+data_size]
                offset += data_size
                
                try:
                    # 解密数据
                    token_info = TokenStorage.decrypt_data(stored_encrypted_data, stored_salt)
                    token_info['_index'] = index  # 添加索引
                    token_info['_salt'] = stored_salt  # 保存盐值用于重新加密
                    stored_tokens.append(token_info)
                    index += 1
                except Exception as e:
                    print(f"解密令牌数据失败: {e}")
                    continue
            
            return stored_tokens
            
        except Exception as e:
            print(f"读取token文件时出错: {e}")
            return []

    @staticmethod
    def save_tokens(tokens: list):
        """保存token数据到文件"""
        tokens_path = TokenStorage.get_tokens_path()
        
        try:
            file_content = bytearray()
            
            for token in tokens:
                # 移除内部字段
                token_copy = token.copy()
                token_copy.pop('_index', None)
                salt = token_copy.pop('_salt', None)
                
                # 如果没有盐值，生成新的
                if salt is None:
                    salt = os.urandom(16)
                
                # 加密数据
                encrypted_data, salt = TokenStorage.encrypt_data(token_copy, salt)
                
                # 写入盐值大小和盐值
                file_content.extend(len(salt).to_bytes(4, byteorder='big'))
                file_content.extend(salt)
                
                # 写入加密数据大小和加密数据
                file_content.extend(len(encrypted_data).to_bytes(4, byteorder='big'))
                file_content.extend(encrypted_data)
            
            # 写入文件
            with open(tokens_path, 'wb') as f:
                f.write(file_content)
            
            return True
            
        except Exception as e:
            print(f"保存token文件时出错: {e}")
            return False


class AccountDeleter:
    """账号删除器主类"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("账号删除器 - 选择性删除")
        self.root.geometry("1400x900")
        
        # 设置缩放因子
        try:
            self.root.tk.call('tk', 'scaling', 1.5)
        except:
            pass
        
        # 设置窗口居中
        self.center_window()
        
        # 存储所有账号数据和选择状态
        self.all_accounts = []
        self.selected_accounts = set()
        
        # 创建UI
        self.create_ui()
        
        # 自动加载账号数据
        self.load_accounts()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1400
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_ui(self):
        """创建用户界面"""
        # 主容器
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="⚠️ 账号删除器 ⚠️",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=32, weight="bold"),
            text_color="#ff4444"
        )
        title_label.pack(pady=(15, 10))
        
        # 警告信息
        warning_label = ctk.CTkLabel(
            main_frame,
            text="警告：删除操作不可逆！请谨慎选择要删除的账号",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=16),
            text_color="#ffaa00"
        )
        warning_label.pack(pady=(0, 20))
        
        # 创建上下分栏
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # 上方控制面板
        top_frame = ctk.CTkFrame(content_frame)
        top_frame.pack(fill="x", padx=15, pady=(15, 10))
        
        # 下方显示面板
        bottom_frame = ctk.CTkFrame(content_frame)
        bottom_frame.pack(fill="both", expand=True, padx=15, pady=(10, 15))
        
        self.create_control_panel(top_frame)
        self.create_account_list(bottom_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 统计信息
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.pack(side="left", fill="y", padx=(15, 10), pady=15)
        
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="统计信息",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=18, weight="bold")
        )
        stats_title.pack(pady=(15, 10))
        
        self.total_label = ctk.CTkLabel(
            stats_frame,
            text="总账号数: 0",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14)
        )
        self.total_label.pack(pady=3)
        
        self.selected_label = ctk.CTkLabel(
            stats_frame,
            text="已选择: 0",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14),
            text_color="#ff4444"
        )
        self.selected_label.pack(pady=3)
        
        self.status_label = ctk.CTkLabel(
            stats_frame,
            text="准备就绪",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14)
        )
        self.status_label.pack(pady=(10, 15))
        
        # 操作按钮
        actions_frame = ctk.CTkFrame(parent)
        actions_frame.pack(side="right", fill="y", padx=(10, 15), pady=15)
        
        actions_title = ctk.CTkLabel(
            actions_frame,
            text="操作",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=18, weight="bold")
        )
        actions_title.pack(pady=(15, 15))
        
        # 刷新按钮
        refresh_btn = ctk.CTkButton(
            actions_frame,
            text="🔄 刷新数据",
            command=self.load_accounts,
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14),
            height=40,
            width=120
        )
        refresh_btn.pack(pady=5)
        
        # 全选按钮
        select_all_btn = ctk.CTkButton(
            actions_frame,
            text="✅ 全选",
            command=self.select_all,
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14),
            height=40,
            width=120,
            fg_color="#4caf50",
            hover_color="#388e3c"
        )
        select_all_btn.pack(pady=5)
        
        # 取消全选按钮
        deselect_all_btn = ctk.CTkButton(
            actions_frame,
            text="❌ 取消全选",
            command=self.deselect_all,
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14),
            height=40,
            width=120,
            fg_color="#ff9800",
            hover_color="#f57c00"
        )
        deselect_all_btn.pack(pady=5)
        
        # 删除选中按钮
        delete_btn = ctk.CTkButton(
            actions_frame,
            text="🗑️ 删除选中",
            command=self.delete_selected,
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14, weight="bold"),
            height=50,
            width=120,
            fg_color="#d32f2f",
            hover_color="#b71c1c"
        )
        delete_btn.pack(pady=(15, 5))
        
        # 完全清空按钮
        clear_all_btn = ctk.CTkButton(
            actions_frame,
            text="💥 清空所有",
            command=self.clear_all_accounts,
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14, weight="bold"),
            height=50,
            width=120,
            fg_color="#8b0000",
            hover_color="#640000"
        )
        clear_all_btn.pack(pady=(5, 15))
    
    def create_account_list(self, parent):
        """创建账号列表"""
        # 列表标题
        list_title = ctk.CTkLabel(
            parent,
            text="账号列表 (点击选择要删除的账号)",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=18, weight="bold")
        )
        list_title.pack(pady=(15, 10))
        
        # 创建框架来包含Treeview
        tree_frame = ctk.CTkFrame(parent)
        tree_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # 创建Treeview
        style = ttk.Style()
        style.theme_use("clam")
        style.configure("Custom.Treeview",
                       background="#2b2b2b",
                       foreground="white",
                       fieldbackground="#2b2b2b",
                       borderwidth=0,
                       relief="flat")
        style.configure("Custom.Treeview.Heading",
                       background="#404040",
                       foreground="white",
                       relief="flat")
        style.map("Custom.Treeview",
                 background=[('selected', '#0078d4')])
        
        # 创建Treeview和滚动条
        tree_container = tk.Frame(tree_frame, bg="#2b2b2b")
        tree_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.tree = ttk.Treeview(tree_container, style="Custom.Treeview")
        self.tree["columns"] = ("email", "timestamp", "access_code", "record_id")
        self.tree["show"] = "tree headings"
        
        # 配置列
        self.tree.column("#0", width=80, minwidth=80, stretch=False)
        self.tree.column("email", width=250, minwidth=200)
        self.tree.column("timestamp", width=180, minwidth=150)
        self.tree.column("access_code", width=300, minwidth=250)
        self.tree.column("record_id", width=200, minwidth=150)
        
        # 配置列标题
        self.tree.heading("#0", text="选择", anchor="center")
        self.tree.heading("email", text="邮箱", anchor="w")
        self.tree.heading("timestamp", text="时间戳", anchor="w")
        self.tree.heading("access_code", text="访问代码", anchor="w")
        self.tree.heading("record_id", text="记录ID", anchor="w")
        
        # 绑定事件
        self.tree.bind("<Button-1>", self.on_tree_click)
        self.tree.bind("<Double-1>", self.on_tree_double_click)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(tree_container, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 打包
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def load_accounts(self):
        """加载账号数据"""
        self.status_label.configure(text="正在加载账号数据...")
        self.root.update()
        
        try:
            self.all_accounts = TokenStorage.load_all_tokens()
            self.selected_accounts.clear()
            self.refresh_tree()
            self.update_stats()
            self.status_label.configure(text=f"已加载 {len(self.all_accounts)} 个账号")
        except Exception as e:
            self.status_label.configure(text=f"加载失败: {str(e)}")
            messagebox.showerror("错误", f"加载账号数据时出错:\n{str(e)}")
    
    def refresh_tree(self):
        """刷新树形列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加账号数据
        for account in self.all_accounts:
            index = account.get('_index', 0)
            email = account.get("token_data", {}).get("email", "未知邮箱")
            timestamp = account.get("timestamp", "未知时间")
            access_code = account.get("access_code", "")[:50] + "..." if len(account.get("access_code", "")) > 50 else account.get("access_code", "")
            record_id = account.get("record_id", "")
            
            # 格式化时间戳
            try:
                dt = datetime.fromisoformat(timestamp)
                formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
            except:
                formatted_time = timestamp
            
            # 检查是否被选中
            check_mark = "☑️" if index in self.selected_accounts else "☐"
            
            self.tree.insert("", "end", 
                           text=check_mark,
                           values=(email, formatted_time, access_code, record_id),
                           tags=(str(index),))
    
    def update_stats(self):
        """更新统计信息"""
        total = len(self.all_accounts)
        selected = len(self.selected_accounts)
        
        self.total_label.configure(text=f"总账号数: {total}")
        self.selected_label.configure(text=f"已选择: {selected}")
    
    def on_tree_click(self, event):
        """处理树形列表点击事件"""
        item = self.tree.identify('item', event.x, event.y)
        column = self.tree.identify('column', event.x, event.y)
        
        if item and column == '#0':  # 点击选择列
            tags = self.tree.item(item, 'tags')
            if tags:
                index = int(tags[0])
                if index in self.selected_accounts:
                    self.selected_accounts.remove(index)
                else:
                    self.selected_accounts.add(index)
                
                self.refresh_tree()
                self.update_stats()
    
    def on_tree_double_click(self, event):
        """处理双击事件 - 显示账号详细信息"""
        item = self.tree.identify('item', event.x, event.y)
        if item:
            tags = self.tree.item(item, 'tags')
            if tags:
                index = int(tags[0])
                account = next((acc for acc in self.all_accounts if acc.get('_index') == index), None)
                if account:
                    self.show_account_details(account)
    
    def show_account_details(self, account):
        """显示账号详细信息"""
        details_window = ctk.CTkToplevel(self.root)
        details_window.title("账号详细信息 - 可编辑")
        details_window.geometry("800x600")
        details_window.transient(self.root)
        
        # 主框架
        main_frame = ctk.CTkFrame(details_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="📝 账号详细信息编辑",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=20, weight="bold")
        )
        title_label.pack(pady=(15, 20))
        
        # 创建滚动框架用于表单
        form_frame = ctk.CTkScrollableFrame(main_frame)
        form_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # 存储编辑控件的引用
        self.edit_widgets = {}
        
        # 基本信息编辑
        basic_label = ctk.CTkLabel(
            form_frame,
            text="=== 基本信息 ===",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=16, weight="bold")
        )
        basic_label.pack(pady=(10, 15), anchor="w")
        
        # 邮箱
        email_frame = ctk.CTkFrame(form_frame)
        email_frame.pack(fill="x", pady=5)
        ctk.CTkLabel(email_frame, text="邮箱:", width=100).pack(side="left", padx=(10, 5), pady=10)
        self.edit_widgets['email'] = ctk.CTkEntry(email_frame, width=400)
        self.edit_widgets['email'].pack(side="left", padx=(5, 10), pady=10, fill="x", expand=True)
        self.edit_widgets['email'].insert(0, account.get('token_data', {}).get('email', ''))
        
        # 时间戳
        timestamp_frame = ctk.CTkFrame(form_frame)
        timestamp_frame.pack(fill="x", pady=5)
        ctk.CTkLabel(timestamp_frame, text="时间戳:", width=100).pack(side="left", padx=(10, 5), pady=10)
        self.edit_widgets['timestamp'] = ctk.CTkEntry(timestamp_frame, width=400)
        self.edit_widgets['timestamp'].pack(side="left", padx=(5, 10), pady=10, fill="x", expand=True)
        self.edit_widgets['timestamp'].insert(0, account.get('timestamp', ''))
        
        # 记录ID
        record_id_frame = ctk.CTkFrame(form_frame)
        record_id_frame.pack(fill="x", pady=5)
        ctk.CTkLabel(record_id_frame, text="记录ID:", width=100).pack(side="left", padx=(10, 5), pady=10)
        self.edit_widgets['record_id'] = ctk.CTkEntry(record_id_frame, width=400)
        self.edit_widgets['record_id'].pack(side="left", padx=(5, 10), pady=10, fill="x", expand=True)
        self.edit_widgets['record_id'].insert(0, account.get('record_id', ''))
        
        # 访问代码
        access_code_frame = ctk.CTkFrame(form_frame)
        access_code_frame.pack(fill="x", pady=5)
        ctk.CTkLabel(access_code_frame, text="访问代码:", width=100).pack(side="top", padx=10, pady=(10, 5), anchor="w")
        self.edit_widgets['access_code'] = ctk.CTkTextbox(access_code_frame, height=80)
        self.edit_widgets['access_code'].pack(fill="x", padx=10, pady=(0, 10))
        self.edit_widgets['access_code'].insert("1.0", account.get('access_code', ''))
        
        # Token数据编辑
        token_label = ctk.CTkLabel(
            form_frame,
            text="=== Token 数据 ===",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=16, weight="bold")
        )
        token_label.pack(pady=(20, 15), anchor="w")
        
        # Token数据编辑区域
        token_data = account.get('token_data', {})
        self.edit_widgets['token_data'] = {}
        
        for key, value in token_data.items():
            token_item_frame = ctk.CTkFrame(form_frame)
            token_item_frame.pack(fill="x", pady=3)
            
            ctk.CTkLabel(token_item_frame, text=f"{key}:", width=120).pack(side="left", padx=(10, 5), pady=8)
            
            if isinstance(value, (dict, list)):
                # 对于复杂数据类型，使用文本框
                text_widget = ctk.CTkTextbox(token_item_frame, height=60)
                text_widget.pack(side="left", padx=(5, 10), pady=8, fill="x", expand=True)
                text_widget.insert("1.0", json.dumps(value, ensure_ascii=False, indent=2))
                self.edit_widgets['token_data'][key] = text_widget
            else:
                # 对于简单数据类型，使用输入框
                entry_widget = ctk.CTkEntry(token_item_frame)
                entry_widget.pack(side="left", padx=(5, 10), pady=8, fill="x", expand=True)
                entry_widget.insert(0, str(value))
                self.edit_widgets['token_data'][key] = entry_widget
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=15, pady=(15, 15))
        
        # 保存按钮
        save_btn = ctk.CTkButton(
            button_frame,
            text="💾 保存修改",
            command=lambda: self.save_account_changes(account, details_window),
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14, weight="bold"),
            height=40,
            width=120,
            fg_color="#4caf50",
            hover_color="#388e3c"
        )
        save_btn.pack(side="left", padx=(15, 10), pady=15)
        
        # 取消按钮
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ 取消",
            command=details_window.destroy,
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14),
            height=40,
            width=120,
            fg_color="#f44336",
            hover_color="#d32f2f"
        )
        cancel_btn.pack(side="right", padx=(10, 15), pady=15)
        
        # 重置按钮
        reset_btn = ctk.CTkButton(
            button_frame,
            text="🔄 重置",
            command=lambda: self.reset_form(account),
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14),
            height=40,
            width=120,
            fg_color="#ff9800",
            hover_color="#f57c00"
        )
        reset_btn.pack(side="left", padx=(10, 10), pady=15)
    
    def save_account_changes(self, original_account, details_window):
        """保存账号修改"""
        try:
            # 获取修改后的数据
            modified_account = original_account.copy()
            
            # 更新基本信息
            email = self.edit_widgets['email'].get().strip()
            timestamp = self.edit_widgets['timestamp'].get().strip()
            record_id = self.edit_widgets['record_id'].get().strip()
            access_code = self.edit_widgets['access_code'].get("1.0", "end-1c").strip()
            
            # 验证必要字段
            if not email:
                messagebox.showerror("错误", "邮箱不能为空！")
                return
            
            # 更新基本字段
            modified_account['timestamp'] = timestamp
            modified_account['record_id'] = record_id
            modified_account['access_code'] = access_code
            
            # 更新token_data
            token_data = modified_account.get('token_data', {})
            token_data['email'] = email
            
            # 更新其他token数据
            for key, widget in self.edit_widgets['token_data'].items():
                if hasattr(widget, 'get'):  # Entry控件
                    value = widget.get().strip()
                    # 尝试转换为原始数据类型
                    try:
                        if value.lower() in ['true', 'false']:
                            token_data[key] = value.lower() == 'true'
                        elif value.isdigit():
                            token_data[key] = int(value)
                        elif value.replace('.', '').isdigit():
                            token_data[key] = float(value)
                        else:
                            token_data[key] = value
                    except:
                        token_data[key] = value
                else:  # Textbox控件
                    value = widget.get("1.0", "end-1c").strip()
                    try:
                        # 尝试解析JSON
                        token_data[key] = json.loads(value)
                    except json.JSONDecodeError:
                        token_data[key] = value
            
            modified_account['token_data'] = token_data
            
            # 在内存中更新账号数据
            account_index = original_account.get('_index', 0)
            for i, account in enumerate(self.all_accounts):
                if account.get('_index') == account_index:
                    # 保留内部字段
                    modified_account['_index'] = account_index
                    modified_account['_salt'] = account.get('_salt')
                    self.all_accounts[i] = modified_account
                    break
            
            # 保存到文件
            if TokenStorage.save_tokens(self.all_accounts):
                # 刷新主界面
                self.refresh_tree()
                self.status_label.configure(text="账号信息修改成功")
                messagebox.showinfo("成功", "账号信息已成功保存！")
                details_window.destroy()
            else:
                messagebox.showerror("错误", "保存文件时出错，修改失败！")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存账号信息时出错:\n{str(e)}")
    
    def reset_form(self, account):
        """重置表单到原始值"""
        try:
            # 重置基本信息
            self.edit_widgets['email'].delete(0, "end")
            self.edit_widgets['email'].insert(0, account.get('token_data', {}).get('email', ''))
            
            self.edit_widgets['timestamp'].delete(0, "end")
            self.edit_widgets['timestamp'].insert(0, account.get('timestamp', ''))
            
            self.edit_widgets['record_id'].delete(0, "end")
            self.edit_widgets['record_id'].insert(0, account.get('record_id', ''))
            
            self.edit_widgets['access_code'].delete("1.0", "end")
            self.edit_widgets['access_code'].insert("1.0", account.get('access_code', ''))
            
            # 重置token数据
            token_data = account.get('token_data', {})
            for key, widget in self.edit_widgets['token_data'].items():
                if hasattr(widget, 'delete') and hasattr(widget, 'insert'):  # Entry控件
                    widget.delete(0, "end")
                    widget.insert(0, str(token_data.get(key, '')))
                else:  # Textbox控件
                    widget.delete("1.0", "end")
                    value = token_data.get(key, '')
                    if isinstance(value, (dict, list)):
                        widget.insert("1.0", json.dumps(value, ensure_ascii=False, indent=2))
                    else:
                        widget.insert("1.0", str(value))
            
            messagebox.showinfo("提示", "表单已重置为原始值")
            
        except Exception as e:
            messagebox.showerror("错误", f"重置表单时出错:\n{str(e)}")
    
    def select_all(self):
        """全选所有账号"""
        self.selected_accounts = set(acc.get('_index', 0) for acc in self.all_accounts)
        self.refresh_tree()
        self.update_stats()
        self.status_label.configure(text="已全选所有账号")
    
    def deselect_all(self):
        """取消全选"""
        self.selected_accounts.clear()
        self.refresh_tree()
        self.update_stats()
        self.status_label.configure(text="已取消选择所有账号")
    
    def delete_selected(self):
        """删除选中的账号"""
        if not self.selected_accounts:
            messagebox.showwarning("警告", "请先选择要删除的账号！")
            return
        
        # 确认删除
        result = messagebox.askyesno(
            "确认删除",
            f"确定要删除选中的 {len(self.selected_accounts)} 个账号吗？\n\n"
            "⚠️ 此操作不可逆！删除后无法恢复！",
            icon="warning"
        )
        
        if not result:
            return
        
        try:
            self.status_label.configure(text="正在删除选中账号...")
            self.root.update()
            
            # 过滤出未选中的账号
            remaining_accounts = []
            deleted_count = 0
            
            for account in self.all_accounts:
                index = account.get('_index', 0)
                if index not in self.selected_accounts:
                    # 重新分配索引
                    account_copy = account.copy()
                    account_copy['_index'] = len(remaining_accounts)
                    remaining_accounts.append(account_copy)
                else:
                    deleted_count += 1
            
            # 保存更新后的数据
            if TokenStorage.save_tokens(remaining_accounts):
                self.all_accounts = remaining_accounts
                self.selected_accounts.clear()
                self.refresh_tree()
                self.update_stats()
                self.status_label.configure(text=f"成功删除 {deleted_count} 个账号")
                messagebox.showinfo("删除成功", f"已成功删除 {deleted_count} 个账号！")
            else:
                self.status_label.configure(text="删除失败 - 保存文件出错")
                messagebox.showerror("错误", "保存文件时出错，删除操作失败！")
                
        except Exception as e:
            self.status_label.configure(text=f"删除失败: {str(e)}")
            messagebox.showerror("错误", f"删除账号时出错:\n{str(e)}")
    
    def clear_all_accounts(self):
        """清空所有账号数据"""
        if not self.all_accounts:
            messagebox.showinfo("提示", "没有账号数据需要清空。")
            return
        
        # 双重确认
        result1 = messagebox.askyesno(
            "⚠️ 危险操作确认",
            f"您即将删除所有 {len(self.all_accounts)} 个账号数据！\n\n"
            "🚨 这是极其危险的操作！\n"
            "🚨 所有数据将被永久删除！\n"
            "🚨 此操作无法撤销！\n\n"
            "确定要继续吗？",
            icon="warning"
        )
        
        if not result1:
            return
        
        result2 = messagebox.askyesno(
            "🔥 最终确认",
            "这是最后一次确认！\n\n"
            "点击【是】将立即删除所有账号数据！\n"
            "点击【否】取消操作！\n\n"
            "您真的确定要删除所有数据吗？",
            icon="question"
        )
        
        if not result2:
            return
        
        try:
            self.status_label.configure(text="正在清空所有账号数据...")
            self.root.update()
            
            # 删除文件
            tokens_path = TokenStorage.get_tokens_path()
            if tokens_path.exists():
                tokens_path.unlink()
            
            # 清空内存数据
            self.all_accounts.clear()
            self.selected_accounts.clear()
            self.refresh_tree()
            self.update_stats()
            
            self.status_label.configure(text="所有账号数据已清空")
            messagebox.showinfo("清空完成", "所有账号数据已被永久删除！")
            
        except Exception as e:
            self.status_label.configure(text=f"清空失败: {str(e)}")
            messagebox.showerror("错误", f"清空账号数据时出错:\n{str(e)}")
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = AccountDeleter()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动时出错:\n{str(e)}")


if __name__ == "__main__":
    main()