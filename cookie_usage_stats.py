#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie使用统计查询脚本
用于获取每天Cookie的使用量统计
"""

import requests
import json
from datetime import datetime, timedelta
import sys
import os
from typing import Optional, Dict, Any

class CookieStatsClient:
    def __init__(self, base_url: str = "https://api2.naoy.me", admin_username: str = "imcycyc", admin_password: str = "Ming980913."):
        """
        初始化客户端
        
        Args:
            base_url: 服务器地址，默认为本地7001端口
            admin_username: 管理员用户名
            admin_password: 管理员密码
        """
        self.base_url = base_url.rstrip('/')
        self.admin_username = admin_username
        self.admin_password = admin_password
        self.token = None
        self.session = requests.Session()
        
    def login(self) -> bool:
        """
        管理员登录获取token

        Returns:
            bool: 登录是否成功
        """
        try:
            # 构建正确的登录URL，避免重复的/api
            if self.base_url.endswith('/api'):
                login_url = f"{self.base_url}/admin/login"
            else:
                login_url = f"{self.base_url}/api/admin/login"

            # 使用HTTP Basic Auth而不是JSON
            from requests.auth import HTTPBasicAuth

            print(f"🔐 正在登录到 {login_url}...")
            response = self.session.post(
                login_url,
                auth=HTTPBasicAuth(self.admin_username, self.admin_password),
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                self.token = result.get("access_token")
                if self.token:
                    # 设置Authorization头
                    self.session.headers.update({
                        "Authorization": f"Bearer {self.token}"
                    })
                    print("✅ 登录成功！")
                    return True
                else:
                    print("❌ 登录失败：未获取到token")
                    return False
            else:
                print(f"❌ 登录失败：HTTP {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"错误详情: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败：无法连接到服务器 {self.base_url}")
            print("请确保服务器正在运行并且地址正确")
            return False
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return False
        except Exception as e:
            print(f"❌ 登录时发生错误: {str(e)}")
            return False
    
    def get_cookie_usage_stats(self, days: int = 7) -> Optional[Dict[str, Any]]:
        """
        获取Cookie使用统计

        Args:
            days: 查询天数，默认7天

        Returns:
            Dict: 统计数据，包含total_used和daily_stats
        """
        if not self.token:
            print("❌ 未登录，请先调用login()方法")
            return None

        try:
            # 构建正确的统计URL
            if self.base_url.endswith('/api'):
                stats_url = f"{self.base_url}/admin/cookies/usage"
            else:
                stats_url = f"{self.base_url}/api/admin/cookies/usage"

            params = {"days": days}

            print(f"📊 正在获取过去{days}天的Cookie使用统计...")
            response = self.session.get(stats_url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                print("✅ 获取统计数据成功！")
                return data
            else:
                print(f"❌ 获取统计失败：HTTP {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"错误详情: {response.text}")
                return None
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败：无法连接到服务器 {self.base_url}")
            return None
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return None
        except Exception as e:
            print(f"❌ 获取统计时发生错误: {str(e)}")
            return None
    
    def get_token_count(self) -> Optional[Dict[str, Any]]:
        """
        获取当前号池中剩余的token数量

        Returns:
            Dict: 包含available, total, used的统计信息
        """
        try:
            # 构建正确的token统计URL
            if self.base_url.endswith('/api'):
                count_url = f"{self.base_url.rstrip('/api')}/api/tokens/count"
            else:
                count_url = f"{self.base_url}/api/tokens/count"

            print("🔢 正在获取号池统计...")
            response = self.session.get(count_url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                print("✅ 获取号池统计成功！")
                return data
            else:
                print(f"❌ 获取号池统计失败：HTTP {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"错误详情: {response.text}")
                return None

        except Exception as e:
            print(f"❌ 获取号池统计时发生错误: {str(e)}")
            return None
    
    def format_stats(self, stats_data: Dict[str, Any]) -> str:
        """
        格式化统计数据为易读的字符串
        
        Args:
            stats_data: 统计数据
            
        Returns:
            str: 格式化后的字符串
        """
        if not stats_data:
            return "无统计数据"
        
        result = []
        result.append("=" * 50)
        result.append("📊 Cookie使用统计报告")
        result.append("=" * 50)
        
        # 总体统计
        total_used = stats_data.get("total_used", 0)
        result.append(f"🔢 总共已使用的Cookie数量: {total_used}")
        result.append("")
        
        # 每日统计
        daily_stats = stats_data.get("daily_stats", [])
        if daily_stats:
            result.append("📅 每日使用统计:")
            result.append("-" * 30)
            
            # 按日期排序
            daily_stats.sort(key=lambda x: x['date'])
            
            total_daily_usage = 0
            for stat in daily_stats:
                date = stat['date']
                count = stat['count']
                total_daily_usage += count
                result.append(f"  {date}: {count:>6} 次")
            
            result.append("-" * 30)
            result.append(f"  总计: {total_daily_usage:>6} 次")
        else:
            result.append("📅 暂无每日使用记录")
        
        result.append("=" * 50)
        return "\n".join(result)

def main():
    """主函数"""
    print("🚀 Cookie使用统计查询工具")
    print("=" * 50)
    
    # 可以通过命令行参数或环境变量配置服务器地址
    server_url = os.getenv("SERVER_URL", "https://api2.naoy.me")
    admin_user = os.getenv("ADMIN_USERNAME", "imcycyc")
    admin_pass = os.getenv("ADMIN_PASSWORD", "Ming980913.")
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    print(f"🌐 服务器地址: {server_url}")
    print(f"👤 管理员用户: {admin_user}")
    
    # 创建客户端
    client = CookieStatsClient(server_url, admin_user, admin_pass)
    
    # 登录
    if not client.login():
        print("❌ 登录失败，程序退出")
        sys.exit(1)
    
    print()
    
    # 获取号池统计
    token_stats = client.get_token_count()
    if token_stats:
        print("🏊 号池统计:")
        print(f"  可用: {token_stats.get('available', 0)}")
        print(f"  总数: {token_stats.get('total', 0)}")
        print(f"  已用: {token_stats.get('used', 0)}")
        print()
    
    # 获取使用统计
    days = 7  # 查询过去7天
    stats = client.get_cookie_usage_stats(days)
    
    if stats:
        # 格式化并显示统计信息
        formatted_stats = client.format_stats(stats)
        print(formatted_stats)
        
        # 保存到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cookie_stats_{timestamp}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(formatted_stats)
            print(f"\n💾 统计报告已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存文件失败: {str(e)}")
    else:
        print("❌ 获取统计数据失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
