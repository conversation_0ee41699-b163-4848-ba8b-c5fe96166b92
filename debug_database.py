#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库调试脚本 - 检查表结构和数据
"""

import os
import sys
import traceback

# 添加卡密端路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '805服务器部分', '卡密端'))

try:
    from app.db import get_db_connection, close_db_connection
    
    def check_database_tables():
        """检查数据库表结构"""
        print("🔍 开始检查数据库表结构...")
        
        try:
            conn = get_db_connection()
            print("✅ 数据库连接成功")
            
            # 检查所有表
            tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """
            
            tables = conn.run(tables_query)
            print(f"\n📋 数据库中的表 ({len(tables)}个):")
            for table in tables:
                print(f"  - {table[0]}")
            
            # 检查cookies表是否存在
            if any('cookies' in str(table[0]) for table in tables):
                print("\n✅ cookies表存在")
                
                # 检查cookies表结构
                cookies_structure_query = """
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = 'cookies'
                    ORDER BY ordinal_position;
                """
                
                columns = conn.run(cookies_structure_query)
                print("\n📊 cookies表结构:")
                for col in columns:
                    print(f"  - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
                
                # 检查cookies表数据
                cookies_count_query = "SELECT COUNT(*) FROM cookies;"
                count = conn.run(cookies_count_query)
                print(f"\n📈 cookies表记录数: {count[0][0]}")
                
                # 检查可用性分布
                availability_query = """
                    SELECT 
                        is_available,
                        COUNT(*) as count
                    FROM cookies
                    GROUP BY is_available;
                """
                availability = conn.run(availability_query)
                print("\n📊 cookies可用性分布:")
                for avail in availability:
                    status = "可用" if avail[0] else "已用"
                    print(f"  - {status}: {avail[1]}")
                    
            else:
                print("\n❌ cookies表不存在！")
                print("需要创建cookies表")
            
            # 检查usage_logs表
            if any('usage_logs' in str(table[0]) for table in tables):
                print("\n✅ usage_logs表存在")
                
                # 检查usage_logs表数据
                usage_count_query = "SELECT COUNT(*) FROM usage_logs;"
                count = conn.run(usage_count_query)
                print(f"📈 usage_logs表记录数: {count[0][0]}")
                
                # 检查最近7天的数据
                recent_usage_query = """
                    SELECT 
                        DATE(timestamp) as date,
                        COUNT(*) as count
                    FROM usage_logs
                    WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                    GROUP BY DATE(timestamp)
                    ORDER BY date DESC;
                """
                
                recent_data = conn.run(recent_usage_query)
                print("\n📅 最近7天使用记录:")
                for data in recent_data:
                    print(f"  - {data[0]}: {data[1]}次")
                    
            else:
                print("\n❌ usage_logs表不存在！")
            
            close_db_connection(conn)
            
        except Exception as e:
            print(f"❌ 数据库检查失败: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
    
    def test_cookie_stats_query():
        """测试Cookie统计查询"""
        print("\n🧪 测试Cookie统计查询...")
        
        try:
            conn = get_db_connection()
            
            # 测试Cookie池统计查询
            pool_stats_query = """
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_available = true THEN 1 END) as available,
                    COUNT(CASE WHEN is_available = false THEN 1 END) as used
                FROM cookies
            """
            
            print("执行查询:", pool_stats_query)
            pool_stats = conn.run(pool_stats_query)
            
            if pool_stats:
                total, available, used = pool_stats[0]
                print(f"✅ Cookie池统计:")
                print(f"  - 总数: {total}")
                print(f"  - 可用: {available}")
                print(f"  - 已用: {used}")
            else:
                print("❌ 没有获取到统计数据")
            
            # 测试每日统计查询
            daily_stats_query = """
                SELECT 
                    DATE(timestamp) as date,
                    COUNT(*) as count
                FROM usage_logs
                WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            """
            
            print(f"\n执行查询: {daily_stats_query}")
            daily_stats = conn.run(daily_stats_query)
            
            print(f"✅ 每日统计 ({len(daily_stats)}条记录):")
            for stat in daily_stats:
                print(f"  - {stat[0]}: {stat[1]}次")
            
            close_db_connection(conn)
            
        except Exception as e:
            print(f"❌ 查询测试失败: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
    
    def create_cookies_table_if_not_exists():
        """如果cookies表不存在则创建"""
        print("\n🔧 检查并创建cookies表...")
        
        try:
            conn = get_db_connection()
            
            # 检查表是否存在
            check_table_query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'cookies'
                );
            """
            
            exists = conn.run(check_table_query)[0][0]
            
            if not exists:
                print("❌ cookies表不存在，正在创建...")
                
                create_table_query = """
                    CREATE TABLE cookies (
                        id SERIAL PRIMARY KEY,
                        value TEXT UNIQUE NOT NULL,
                        is_available BOOLEAN DEFAULT TRUE,
                        last_used_at TIMESTAMP WITH TIME ZONE
                    );
                """
                
                conn.run(create_table_query)
                print("✅ cookies表创建成功")
                
                # 创建索引
                create_index_query = """
                    CREATE INDEX idx_cookies_value ON cookies(value);
                    CREATE INDEX idx_cookies_available ON cookies(is_available);
                """
                
                conn.run(create_index_query)
                print("✅ 索引创建成功")
                
            else:
                print("✅ cookies表已存在")
            
            close_db_connection(conn)
            
        except Exception as e:
            print(f"❌ 创建表失败: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
    
    def main():
        print("🚀 数据库调试工具")
        print("=" * 50)
        
        # 1. 检查数据库表结构
        check_database_tables()
        
        # 2. 如果需要，创建cookies表
        create_cookies_table_if_not_exists()
        
        # 3. 测试统计查询
        test_cookie_stats_query()
        
        print("\n" + "=" * 50)
        print("🎯 调试完成！")
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在正确的目录下运行此脚本")
    sys.exit(1)
except Exception as e:
    print(f"❌ 运行时错误: {e}")
    print(f"错误详情: {traceback.format_exc()}")
    sys.exit(1)
