#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库查询语句
"""

import sys
import os
import traceback

# 添加卡密端路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '805服务器部分', '卡密端'))

try:
    from app.db import get_db_connection, close_db_connection
    
    def test_cookie_stats_queries():
        """测试Cookie统计查询语句"""
        print("🧪 测试Cookie统计查询语句")
        print("=" * 50)
        
        try:
            conn = get_db_connection()
            print("✅ 数据库连接成功")
            
            # 测试1: 检查cookies表是否存在
            print("\n1️⃣ 测试cookies表检查查询...")
            check_cookies_table_query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'cookies'
                )
            """
            
            cookies_table_exists = conn.run(check_cookies_table_query)[0][0]
            print(f"✅ cookies表存在: {cookies_table_exists}")
            
            # 测试2: Cookie池统计查询
            if cookies_table_exists:
                print("\n2️⃣ 测试Cookie池统计查询...")
                pool_stats_query = """
                    SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN is_available = true THEN 1 END) as available,
                        COUNT(CASE WHEN is_available = false THEN 1 END) as used
                    FROM cookies
                """
                
                pool_stats = conn.run(pool_stats_query)
                print(f"✅ Cookie池统计查询成功: {pool_stats}")
                
                if pool_stats:
                    total, available, used = pool_stats[0]
                    print(f"  总数: {total}, 可用: {available}, 已用: {used}")
            
            # 测试3: 检查usage_logs表是否存在
            print("\n3️⃣ 测试usage_logs表检查查询...")
            check_usage_logs_table_query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'usage_logs'
                )
            """
            
            usage_logs_table_exists = conn.run(check_usage_logs_table_query)[0][0]
            print(f"✅ usage_logs表存在: {usage_logs_table_exists}")
            
            # 测试4: 每日统计查询
            if usage_logs_table_exists:
                print("\n4️⃣ 测试每日统计查询...")
                daily_stats_query = """
                    SELECT 
                        DATE(timestamp) as date,
                        COUNT(*) as count
                    FROM usage_logs
                    WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                    GROUP BY DATE(timestamp)
                    ORDER BY date DESC
                """
                
                daily_stats = conn.run(daily_stats_query)
                print(f"✅ 每日统计查询成功: {len(daily_stats) if daily_stats else 0}条记录")
                
                for date, count in daily_stats[:3]:  # 只显示前3条
                    print(f"  {date}: {count}次")
                
                if len(daily_stats) > 3:
                    print(f"  ... 还有{len(daily_stats) - 3}条记录")
            
            # 测试5: 完整的统计数据格式化
            print("\n5️⃣ 测试完整统计数据格式化...")
            
            # 模拟API返回的数据结构
            if cookies_table_exists and pool_stats:
                total, available, used = pool_stats[0]
            else:
                total, available, used = 0, 0, 0
            
            formatted_daily_stats = []
            if usage_logs_table_exists and daily_stats:
                total_daily_usage = sum(stat[1] for stat in daily_stats)
                
                for date, count in daily_stats:
                    percentage = (count / total_daily_usage * 100) if total_daily_usage > 0 else 0
                    formatted_daily_stats.append({
                        "date": date.strftime("%Y-%m-%d"),
                        "count": count,
                        "percentage": round(percentage, 1)
                    })
            
            result = {
                "success": True,
                "pool_stats": {
                    "total": total or 0,
                    "available": available or 0,
                    "used": used or 0
                },
                "daily_stats": formatted_daily_stats,
                "debug_info": {
                    "cookies_table_exists": cookies_table_exists,
                    "usage_logs_table_exists": usage_logs_table_exists,
                    "daily_records_count": len(formatted_daily_stats)
                }
            }
            
            print("✅ 完整统计数据格式化成功")
            print(f"📊 结果预览:")
            print(f"  Cookie池: 总数{result['pool_stats']['total']}, 可用{result['pool_stats']['available']}, 已用{result['pool_stats']['used']}")
            print(f"  每日记录: {len(result['daily_stats'])}条")
            print(f"  调试信息: {result['debug_info']}")
            
            close_db_connection(conn)
            print("\n✅ 所有查询测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 查询测试失败: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
            return False
    
    def test_api_simulation():
        """模拟API调用"""
        print("\n🎭 模拟API调用测试")
        print("=" * 50)
        
        try:
            from datetime import datetime
            import json
            
            conn = get_db_connection()
            
            # 模拟完整的API逻辑
            # 1. 检查表存在性
            check_cookies_table_query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'cookies'
                )
            """
            cookies_table_exists = conn.run(check_cookies_table_query)[0][0]
            
            check_usage_logs_table_query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'usage_logs'
                )
            """
            usage_logs_table_exists = conn.run(check_usage_logs_table_query)[0][0]
            
            # 2. 获取Cookie池统计
            if cookies_table_exists:
                pool_stats_query = """
                    SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN is_available = true THEN 1 END) as available,
                        COUNT(CASE WHEN is_available = false THEN 1 END) as used
                    FROM cookies
                """
                pool_stats = conn.run(pool_stats_query)
                if pool_stats:
                    total, available, used = pool_stats[0]
                else:
                    total, available, used = 0, 0, 0
            else:
                total, available, used = 0, 0, 0
            
            # 3. 获取每日统计
            formatted_daily_stats = []
            if usage_logs_table_exists:
                daily_stats_query = """
                    SELECT 
                        DATE(timestamp) as date,
                        COUNT(*) as count
                    FROM usage_logs
                    WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                    GROUP BY DATE(timestamp)
                    ORDER BY date DESC
                """
                daily_stats = conn.run(daily_stats_query)
                
                total_daily_usage = sum(stat[1] for stat in daily_stats) if daily_stats else 0
                
                for date, count in daily_stats:
                    percentage = (count / total_daily_usage * 100) if total_daily_usage > 0 else 0
                    formatted_daily_stats.append({
                        "date": date.strftime("%Y-%m-%d"),
                        "count": count,
                        "percentage": round(percentage, 1)
                    })
            
            # 4. 构建响应
            result = {
                "success": True,
                "pool_stats": {
                    "total": total or 0,
                    "available": available or 0,
                    "used": used or 0
                },
                "daily_stats": formatted_daily_stats,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "debug_info": {
                    "cookies_table_exists": cookies_table_exists,
                    "usage_logs_table_exists": usage_logs_table_exists,
                    "daily_records_count": len(formatted_daily_stats)
                }
            }
            
            close_db_connection(conn)
            
            print("✅ API模拟调用成功！")
            print("📋 完整响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            return result
            
        except Exception as e:
            print(f"❌ API模拟失败: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
            return None
    
    def main():
        print("🚀 数据库查询测试工具")
        
        # 测试1: 基础查询
        success1 = test_cookie_stats_queries()
        
        # 测试2: API模拟
        if success1:
            result = test_api_simulation()
            if result:
                print("\n🎯 所有测试通过！API应该能正常工作。")
            else:
                print("\n❌ API模拟失败")
        else:
            print("\n❌ 基础查询测试失败")
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在正确的目录下运行此脚本")
    sys.exit(1)
except Exception as e:
    print(f"❌ 运行时错误: {e}")
    print(f"错误详情: {traceback.format_exc()}")
    sys.exit(1)
