#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地测试路由功能
"""

from app import create_app
from flask import url_for
import json

def test_local_routes():
    """测试本地路由"""
    print("🧪 测试本地Flask路由")
    print("=" * 50)
    
    # 创建应用
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("📋 测试Cookie统计API路由...")
            
            try:
                # 测试健康检查
                health_response = client.get('/api/health')
                print(f"健康检查状态: {health_response.status_code}")
                if health_response.status_code == 200:
                    print(f"健康检查响应: {health_response.get_json()}")
                
                # 测试Cookie统计API（未登录状态）
                stats_response = client.get('/api/stats/cookie-usage')
                print(f"Cookie统计API状态: {stats_response.status_code}")
                
                if stats_response.status_code == 401:
                    print("✅ API需要登录认证（正常）")
                elif stats_response.status_code == 200:
                    print("✅ API调用成功")
                    data = stats_response.get_json()
                    print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ 意外的状态码: {stats_response.status_code}")
                    print(f"响应内容: {stats_response.get_data(as_text=True)}")
                
                # 尝试模拟登录后测试
                print("\n🔐 尝试模拟登录测试...")
                
                # 先获取登录页面
                login_page = client.get('/login')
                print(f"登录页面状态: {login_page.status_code}")
                
                # 尝试登录
                login_data = {
                    'username': 'imcycyc',
                    'password': 'Ming980913.'
                }
                
                login_response = client.post('/login', data=login_data, follow_redirects=True)
                print(f"登录响应状态: {login_response.status_code}")
                
                # 登录后再次测试API
                stats_response_after_login = client.get('/api/stats/cookie-usage')
                print(f"登录后Cookie统计API状态: {stats_response_after_login.status_code}")
                
                if stats_response_after_login.status_code == 200:
                    print("✅ 登录后API调用成功！")
                    data = stats_response_after_login.get_json()
                    print(f"Cookie池统计: {data.get('pool_stats', {})}")
                    print(f"每日统计记录数: {len(data.get('daily_stats', []))}")
                    print(f"调试信息: {data.get('debug_info', {})}")
                else:
                    print(f"❌ 登录后API仍然失败: {stats_response_after_login.status_code}")
                    print(f"错误内容: {stats_response_after_login.get_data(as_text=True)}")
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {str(e)}")
                import traceback
                print(f"错误详情: {traceback.format_exc()}")

def list_all_routes():
    """列出所有路由"""
    print("\n📋 所有注册的路由:")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        for rule in app.url_map.iter_rules():
            methods = ','.join(rule.methods - {'HEAD', 'OPTIONS'})
            print(f"  {rule.rule:<30} [{methods:<10}] -> {rule.endpoint}")

if __name__ == "__main__":
    print("🚀 Flask应用本地测试")
    
    # 列出所有路由
    list_all_routes()
    
    # 测试路由功能
    test_local_routes()
    
    print("\n🎯 测试完成！")
