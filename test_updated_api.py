#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的API
"""

import requests
import json
from datetime import datetime

def test_health_api():
    """测试健康检查API"""
    print("🏥 测试健康检查API")
    print("=" * 40)
    
    try:
        response = requests.get("https://apix.naoy.me/chaxun/api/health", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查成功")
            print(f"版本: {data.get('version', 'unknown')}")
            print(f"功能: {data.get('features', [])}")
            
            # 检查是否包含新版本标识
            if data.get('version') == '2025-08-05-v3':
                print("✅ 服务器代码已更新到最新版本")
                return True
            else:
                print("❌ 服务器代码版本过旧")
                return False
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

def test_stats_test_route():
    """测试统计测试路由"""
    print("\n🧪 测试统计测试路由")
    print("=" * 40)
    
    try:
        response = requests.get("https://apix.naoy.me/chaxun/api/stats/test", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 测试路由成功")
            print(f"消息: {data.get('message')}")
            print(f"路径: {data.get('path')}")
            return True
        else:
            print(f"❌ 测试路由失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试路由异常: {str(e)}")
        return False

def test_cookie_stats_without_auth():
    """测试Cookie统计API（未登录）"""
    print("\n📊 测试Cookie统计API（未登录）")
    print("=" * 40)
    
    try:
        response = requests.get("https://apix.naoy.me/chaxun/api/stats/cookie-usage", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 401:
            try:
                data = response.json()
                print("✅ 正确返回401认证错误（JSON格式）")
                print(f"错误信息: {data.get('error')}")
                print(f"错误代码: {data.get('error_code')}")
                return True
            except json.JSONDecodeError:
                print("❌ 返回401但不是JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            try:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def test_cookie_stats_with_auth():
    """测试Cookie统计API（已登录）"""
    print("\n🔐 测试Cookie统计API（已登录）")
    print("=" * 40)
    
    session = requests.Session()
    
    try:
        # 先登录
        login_url = "https://apix.naoy.me/chaxun/login"
        login_data = {
            'username': 'imcycyc',
            'password': 'Ming980913.'
        }
        
        print("🔑 正在登录...")
        login_response = session.post(login_url, data=login_data, timeout=10)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 登录成功，测试API
            print("📡 调用Cookie统计API...")
            api_response = session.get("https://apix.naoy.me/chaxun/api/stats/cookie-usage", timeout=10)
            print(f"API状态码: {api_response.status_code}")
            print(f"API响应头: {dict(api_response.headers)}")
            
            if api_response.status_code == 200:
                try:
                    data = api_response.json()
                    print("✅ Cookie统计API调用成功！")
                    print(f"成功状态: {data.get('success')}")
                    
                    if data.get('success'):
                        pool_stats = data.get('pool_stats', {})
                        daily_stats = data.get('daily_stats', [])
                        print(f"Cookie池统计: 总数{pool_stats.get('total')}, 可用{pool_stats.get('available')}, 已用{pool_stats.get('used')}")
                        print(f"每日统计: {len(daily_stats)}条记录")
                        return True
                    else:
                        print(f"❌ API返回失败: {data.get('error')}")
                        return False
                        
                except json.JSONDecodeError:
                    print("❌ API返回非JSON格式")
                    print(f"响应内容: {api_response.text[:200]}...")
                    return False
            else:
                print(f"❌ API调用失败: {api_response.status_code}")
                print(f"响应内容: {api_response.text[:200]}...")
                return False
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 API更新验证测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    
    # 测试1: 健康检查
    results.append(("健康检查", test_health_api()))
    
    # 测试2: 测试路由
    results.append(("测试路由", test_stats_test_route()))
    
    # 测试3: 未登录状态的Cookie统计API
    results.append(("Cookie统计API(未登录)", test_cookie_stats_without_auth()))
    
    # 测试4: 已登录状态的Cookie统计API
    results.append(("Cookie统计API(已登录)", test_cookie_stats_with_auth()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Cookie统计功能应该能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查服务器配置。")

if __name__ == "__main__":
    main()
